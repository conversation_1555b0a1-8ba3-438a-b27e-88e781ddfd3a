package com.StarProApi.service.impl;

import com.StarProApi.entity.*;
import com.StarProApi.common.PageList;
import com.StarProApi.dao.*;
import com.StarProApi.service.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 业务层实现类
 * TypechoForumCommentServiceImpl
 * <AUTHOR>
 * @date 2023/02/20
 */
@Service
public class TypechoForumCommentServiceImpl implements TypechoForumCommentService {

    @Autowired
	TypechoForumCommentDao dao;

    @Override
    public int insert(TypechoForumComment typechoForumComment) {
        return dao.insert(typechoForumComment);
    }

    @Override
    public int batchInsert(List<TypechoForumComment> list) {
    	return dao.batchInsert(list);
    }

    @Override
    public int update(TypechoForumComment typechoForumComment) {
    	return dao.update(typechoForumComment);
    }

    @Override
    public int delete(Object key) {
    	return dao.delete(key);
    }

    @Override
    public int batchDelete(List<Object> keys) {
        return dao.batchDelete(keys);
    }

	@Override
	public TypechoForumComment selectByKey(Object key) {
		return dao.selectByKey(key);
	}

	@Override
	public List<TypechoForumComment> selectList(TypechoForumComment typechoForumComment) {
		return dao.selectList(typechoForumComment);
	}

	@Override
	public PageList<TypechoForumComment> selectPage(TypechoForumComment typechoForumComment, Integer offset, Integer pageSize,String order,String searchKey) {
		PageList<TypechoForumComment> pageList = new PageList<>();

		int total = this.total(typechoForumComment,searchKey);

		Integer totalPage;
		if (total % pageSize != 0) {
			totalPage = (total /pageSize) + 1;
		} else {
			totalPage = total /pageSize;
		}

		int page = (offset - 1) * pageSize;

		List<TypechoForumComment> list = dao.selectPage(typechoForumComment, page, pageSize,order,searchKey);

		pageList.setList(list);
		pageList.setStartPageNo(offset);
		pageList.setPageSize(pageSize);
		pageList.setTotalCount(total);
		pageList.setTotalPageCount(totalPage);
		return pageList;
	}

	@Override
	public int total(TypechoForumComment typechoForumComment,String searchKey) {
		return dao.total(typechoForumComment,searchKey);
	}
}