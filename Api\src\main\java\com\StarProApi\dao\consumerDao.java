package com.StarProApi.dao;

import com.StarProApi.entity.TypechoContents;
import com.StarProApi.entity.identifyConsumer;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface consumerDao {

    List<identifyConsumer> queryInfo(@Param("identifyConsumer") identifyConsumer dto,@Param("page") int page,@Param("pageSize") int pageSize);

    int insert(identifyConsumer dto);

    Integer remove(Object key);

    Integer update(identifyConsumer dto);

    /**
     * [主键查询]
     **/
    identifyConsumer selectByKey(Object uid);
}
