package com.StarProApi.entity;

import java.io.Serializable;
import lombok.Data;
import java.util.Date;
import java.util.List;

/**
 * TypechoForumSection
 * <AUTHOR> 2023-02-20
 */
@Data
public class TypechoForumSection implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id  
     */
    private Integer id;

    /**
     * name  版块名称
     */
    private String name;

    /**
     * pic  版块图片
     */
    private String pic;

    /**
     * bg  版块背景
     */
    private String bg;

    /**
     * text  版块介绍
     */
    private String text;

    /**
     * type  sort为大类，section为版块
     */
    private String type;

    /**
     * restrict  发帖等级（根据权限表限制）
     */
    private Integer restrictKey;

    /**
     * 上级板块
     */
    private Integer parent;

    /**
     * slug  缩略名
     */
    private String slug;

    /**
     * 排序  order
     */
    private Integer orderKey;

    /**
     * 是否推荐  isrecommend
     */
    private Integer isrecommend;
}