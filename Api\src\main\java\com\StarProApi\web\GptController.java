package com.StarProApi.web;

import com.StarProApi.annotation.LoginRequired;
import com.StarProApi.common.*;
import com.StarProApi.entity.*;
import com.StarProApi.service.*;
import com.alibaba.dashscope.app.Application;
import com.alibaba.dashscope.app.ApplicationParam;
import com.alibaba.dashscope.app.ApplicationResult;
import com.alibaba.dashscope.app.ApplicationUsage;
import com.alibaba.dashscope.exception.InputRequiredException;
import com.alibaba.dashscope.exception.NoApiKeyException;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Controller
@RequestMapping(value = "/SProgpt")
public class GptController {
    @Autowired
    private TypechoGptService gptService;

    @Autowired
    private TypechoGptChatService gptChatService;

    @Autowired
    private TypechoGptMsgService gptMsgService;

    @Autowired
    private TypechoUsersService usersService;

    @Autowired
    private TypechoApiconfigService apiconfigService;

    @Autowired
    private TypechoPaylogService paylogService;

    @Autowired
    private SecurityService securityService;


    @Autowired
    private RedisTemplate redisTemplate;

    @Value("${web.prefix}")
    private String dataprefix;

    @Value("${mybatis.configuration.variables.prefix}")
    private String prefix;

    @Autowired
    private JdbcTemplate jdbcTemplate;

    RedisHelp redisHelp = new RedisHelp();

    UserStatus UStatus = new UserStatus();

    ResultAll Result = new ResultAll();

    EditFile editFile = new EditFile();


    /***
     * 发送消息&创建聊天
     */
    @RequestMapping(value = "/sendMsg",method = RequestMethod.POST)
    @ResponseBody
    @LoginRequired(purview = "0")
    public String sendMsg(@RequestParam(value = "token", required = false) String  token,
                          @RequestParam(value = "gptid", required = false) Integer  gptid,
                          @RequestParam(value = "msg", required = false) String  msg){
        try {
            Map map =redisHelp.getMapValue(this.dataprefix+"_"+"userInfo"+token,redisTemplate);
            Integer uid =Integer.parseInt(map.get("uid").toString());
            //请求次数限制
            String isRepeated = redisHelp.getRedis(uid+"_isAiMsg",redisTemplate);
            if(isRepeated==null){
                redisHelp.setRedis(uid+"_isAiMsg","1",5,redisTemplate);
            }else{
                return Result.getResultJson(0,"你的操作太频繁了，请等待几秒再试试吧！",null);
            }

            Integer isvip = 0;
            if(map.get("isvip")!=null){
                isvip  =Integer.parseInt(map.get("isvip").toString());
            }

            TypechoGpt gpt = gptService.selectByKey(gptid);
            if(gpt==null){
                return Result.getResultJson(0, "当前大模型不存在", null);
            }
            if(!gpt.getType().equals(0)){
                return Result.getResultJson(0, "当前模型非聊天用途", null);
            }
            //判断是否VIP可用
            if(gpt.getIsVip().equals(1)){
                if(isvip.equals(0)){
                    return Result.getResultJson(0, "当前大模型仅VIP权限可用", null);
                }
            }
            //获取当前时间
            Long date = System.currentTimeMillis();
            String curTime = String.valueOf(date).substring(0,10);
            //如果是收费的，就先扣钱。扣钱但聊天失败了可以后台补，但绝对不能允许白嫖
            Integer price = gpt.getPrice();
            if(price > 0){
                TypechoUsers user = usersService.selectByKey(uid);
                Integer assets = user.getAssets();
                if(price>assets){
                    return Result.getResultJson(0,"当前资产不足，请充值",null);
                }
                Integer newassets = assets - price;
                //更新用户资产与登录状态
                TypechoUsers updateUser = new TypechoUsers();
                updateUser.setUid(user.getUid());
                updateUser.setAssets(newassets);
                usersService.update(updateUser);
                TypechoPaylog paylog = new TypechoPaylog();
                paylog.setStatus(1);
                paylog.setCreated(Integer.parseInt(curTime));
                paylog.setUid(uid);
                paylog.setOutTradeNo(curTime+"buyshop");
                paylog.setTotalAmount("-"+price);
                paylog.setPaytype("buyshop");
                paylog.setSubject("购买AI大模型["+gpt.getName()+"]请求次数");
                paylogService.insert(paylog);
            }
            //是否已经聊天过
            TypechoGptChat gptChat = new TypechoGptChat();
            gptChat.setUid(uid);
            gptChat.setGptid(gptid);
            List<TypechoGptChat> chats = gptChatService.selectList(gptChat);


            ApplicationParam param = ApplicationParam.builder()
                    .appId(gpt.getAppId())
                    .apiKey(gpt.getApiKey())
                    .prompt(msg)
                    .build();

            if(chats.size()>0){
                String sessionId = chats.get(0).getSessionId();
                param = ApplicationParam.builder()
                        .appId(gpt.getAppId())
                        .apiKey(gpt.getApiKey())
                        .sessionId(sessionId)
                        .prompt(msg)
                        .build();
            }

            Application application = new Application();
            ApplicationResult result = null;
            try {
                result = application.call(param);
            } catch (NoApiKeyException e) {
                e.printStackTrace();
                throw new RuntimeException(e);
            } catch (InputRequiredException e) {
                e.printStackTrace();
                throw new RuntimeException(e);
            }


            System.out.printf("requestId: %s, text: %s, finishReason: %s\n",
                    result.getRequestId(), result.getOutput().getText(), result.getOutput().getFinishReason());

            if (result.getUsage() != null && result.getUsage().getModels() != null) {
                for (ApplicationUsage.ModelUsage usage : result.getUsage().getModels()) {
                    System.out.printf("modelId: %s, inputTokens: %d, outputTokens: %d\n",
                            usage.getModelId(), usage.getInputTokens(), usage.getOutputTokens());
                }

                //如果聊天过，增加消息，如果没有聊天过，增加新的gpt聊天室
                if(chats.size()>0){
                    Integer chatId = chats.get(0).getId();
                    gptChat.setId(chatId);
                    gptChat.setReplyTime(Integer.parseInt(curTime));
                    gptChatService.update(gptChat);

                }else{
                    gptChat.setSessionId(result.getOutput().getSessionId());
                    gptChat.setCreated(Integer.parseInt(curTime));
                    gptChat.setReplyTime(Integer.parseInt(curTime));
                    gptChatService.insert(gptChat);
                }
                //加入新的消息，包括我自己的和AI回复的。
                //加入我自己的
                TypechoGptMsg myMsg = new TypechoGptMsg();
                myMsg.setUid(uid);
                myMsg.setCreated(Integer.parseInt(curTime));
                myMsg.setGptid(gptid);
                myMsg.setText(msg);
                myMsg.setIsAI(0);
                gptMsgService.insert(myMsg);
                //加入AI的
                //再获取当前时间
                Long curDate = System.currentTimeMillis();
                String aiMsgTime = String.valueOf(curDate).substring(0,10);
                TypechoGptMsg aiMsg = new TypechoGptMsg();
                aiMsg.setUid(uid);
                aiMsg.setCreated(Integer.parseInt(aiMsgTime));
                aiMsg.setGptid(gptid);
                aiMsg.setText(result.getOutput().getText());
                aiMsg.setIsAI(1);
                gptMsgService.insert(aiMsg);
                //返回AI的回信
                JSONObject data = new JSONObject();
                data.put("created" , Integer.parseInt(aiMsgTime));
                data.put("text"  , result.getOutput().getText());
                redisHelp.deleteKeysWithPattern("*"+this.dataprefix+"_gptlastList_"+gptid+"_"+uid+"_*",redisTemplate,this.dataprefix);
                redisHelp.deleteKeysWithPattern("*"+this.dataprefix+"_gptmsgList_"+gptid+"_"+uid+"_*",redisTemplate,this.dataprefix);
                return Result.getResultJson(1, "发送成功", data);
            }
            return Result.getResultJson(0, "消息接收出错，请重试", null);
        }catch (Exception e){
            e.printStackTrace();
            return Result.getResultJson(0,"接口请求异常，请联系管理员",null);

        }

    }

    /***
     * 发送文本（针对AI应用）
     */
    @RequestMapping(value = "/sendText",method = RequestMethod.POST)
    @ResponseBody
    @LoginRequired(purview = "0")
    public String sendText(@RequestParam(value = "token", required = false) String  token,
                          @RequestParam(value = "gptid", required = false) Integer  gptid,
                          @RequestParam(value = "msg", required = false) String  msg){
        try {
            Map map =redisHelp.getMapValue(this.dataprefix+"_"+"userInfo"+token,redisTemplate);
            Integer uid =Integer.parseInt(map.get("uid").toString());
            //请求次数限制
            String isRepeated = redisHelp.getRedis(uid+"_isAiMsg",redisTemplate);
            if(isRepeated==null){
                redisHelp.setRedis(uid+"_isAiMsg","1",5,redisTemplate);
            }else{
                return Result.getResultJson(0,"你的操作太频繁了，请等待几秒再试试吧！",null);
            }

            Integer isvip = 0;
            if(map.get("isvip")!=null){
                isvip  =Integer.parseInt(map.get("isvip").toString());
            }

            TypechoGpt gpt = gptService.selectByKey(gptid);
            if(gpt==null){
                return Result.getResultJson(0, "当前大模型不存在", null);
            }
            if(!gpt.getType().equals(1)){
                return Result.getResultJson(0, "当前模型非应用用途", null);
            }
            //判断是否VIP可用
            if(gpt.getIsVip().equals(1)){
                if(isvip.equals(0)){
                    return Result.getResultJson(0, "当前大模型仅VIP权限可用", null);
                }
            }
            //获取当前时间
            Long date = System.currentTimeMillis();
            String curTime = String.valueOf(date).substring(0,10);
            //如果是收费的，就先扣钱。扣钱但聊天失败了可以后台补，但绝对不能允许白嫖
            Integer price = gpt.getPrice();
            if(price > 0){
                TypechoUsers user = usersService.selectByKey(uid);
                Integer assets = user.getAssets();
                if(price>assets){
                    return Result.getResultJson(0,"当前资产不足，请充值",null);
                }
                Integer newassets = assets - price;
                //更新用户资产与登录状态
                TypechoUsers updateUser = new TypechoUsers();
                updateUser.setUid(user.getUid());
                updateUser.setAssets(newassets);
                usersService.update(updateUser);
                TypechoPaylog paylog = new TypechoPaylog();
                paylog.setStatus(1);
                paylog.setCreated(Integer.parseInt(curTime));
                paylog.setUid(uid);
                paylog.setOutTradeNo(curTime+"buyshop");
                paylog.setTotalAmount("-"+price);
                paylog.setPaytype("buyshop");
                paylog.setSubject("购买AI大模型["+gpt.getName()+"]请求次数");
                paylogService.insert(paylog);
            }
            msg = gpt.getPrompt()+msg;
            ApplicationParam param = ApplicationParam.builder()
                    .appId(gpt.getAppId())
                    .apiKey(gpt.getApiKey())
                    .prompt(msg)
                    .build();


            Application application = new Application();
            ApplicationResult result = null;
            try {
                result = application.call(param);
            } catch (NoApiKeyException e) {
                e.printStackTrace();
                throw new RuntimeException(e);
            } catch (InputRequiredException e) {
                e.printStackTrace();
                throw new RuntimeException(e);
            }


            System.out.printf("requestId: %s, text: %s, finishReason: %s\n",
                    result.getRequestId(), result.getOutput().getText(), result.getOutput().getFinishReason());

            if (result.getUsage() != null && result.getUsage().getModels() != null) {
                for (ApplicationUsage.ModelUsage usage : result.getUsage().getModels()) {
                    System.out.printf("modelId: %s, inputTokens: %d, outputTokens: %d\n",
                            usage.getModelId(), usage.getInputTokens(), usage.getOutputTokens());
                }
                //再获取当前时间
                Long curDate = System.currentTimeMillis();
                String aiMsgTime = String.valueOf(curDate).substring(0,10);
                //返回AI的回信
                JSONObject data = new JSONObject();
                data.put("created" , Integer.parseInt(aiMsgTime));
                data.put("text"  , result.getOutput().getText());
                return Result.getResultJson(1, "请求成功", data);
            }
            return Result.getResultJson(0, "消息接收出错，请重试", null);
        }catch (Exception e){
            e.printStackTrace();
            return Result.getResultJson(0,"接口请求异常，请联系管理员",null);

        }

    }

    /***
     * 获取最新的消息，限制条数
     */
    @RequestMapping(value = "/lastMsg")
    @ResponseBody
    @LoginRequired(purview = "0")
    public String lastMsg(@RequestParam(value = "token", required = false) String  token,
                          @RequestParam(value = "gptid", required = false) Integer  gptid,
                          @RequestParam(value = "limit"       , required = false, defaultValue = "50") Integer limit){
        try {
            if(limit>300){
                limit = 300;
            }
            Map map =redisHelp.getMapValue(this.dataprefix+"_"+"userInfo"+token,redisTemplate);
            Integer uid =Integer.parseInt(map.get("uid").toString());
            TypechoGpt gpt = gptService.selectByKey(gptid);
            if(gpt==null){
                return Result.getResultJson(0, "当前大模型不存在", null);
            }
            //是否已经聊天过
            TypechoGptChat gptChat = new TypechoGptChat();
            gptChat.setUid(uid);
            gptChat.setGptid(gptid);
            List<TypechoGptChat> chats = gptChatService.selectList(gptChat);
            if(chats.size() < 1){
                return Result.getResultJson(0, "你还未发起过聊天", null);
            }

            List jsonList = new ArrayList();
            List cacheList = redisHelp.getList(this.dataprefix+"_"+"gptlastList_"+gptid+"_"+uid+"_"+limit,redisTemplate);

            if(cacheList.size()>0){
                jsonList = cacheList;
            }else{
                TypechoGptMsg query = new TypechoGptMsg();
                query.setGptid(gptid);
                query.setUid(uid);
                PageList<TypechoGptMsg> pageList = gptMsgService.selectPage(query, 1, limit,null);
                List<TypechoGptMsg> list = pageList.getList();
                if(list.size() < 1){
                    JSONObject noData = new JSONObject();
                    noData.put("code" , 1);
                    noData.put("msg"  , "");
                    noData.put("data" , new ArrayList());
                    noData.put("count", 0);
                    return noData.toString();
                }
                for (int i = 0; i < list.size(); i++) {
                    Map json = JSONObject.parseObject(JSONObject.toJSONString(list.get(i)), Map.class);
                    TypechoGptMsg msg = list.get(i);
                    Integer userid = msg.getUid();
                    Map userJson = UserStatus.getUserInfo(userid,apiconfigService,usersService);
                    json.put("userJson",userJson);
                    jsonList.add(json);
                }
            }
            redisHelp.delete(this.dataprefix+"_"+"gptlastList_"+gptid+"_"+uid+"_"+limit,redisTemplate);
            redisHelp.setList(this.dataprefix+"_"+"gptlastList_"+gptid+"_"+uid+"_"+limit,jsonList,120,redisTemplate);
            JSONObject response = new JSONObject();
            response.put("code" , 1);
            response.put("msg"  , "");
            response.put("data" , jsonList);
            response.put("count", jsonList.size());
            return response.toString();
        }catch (Exception e){
            e.printStackTrace();
            return Result.getResultJson(0,"接口请求异常，请联系管理员",null);

        }
    }

    /***
     * 消息分页查询（用于查询历史记录）
     */
    @RequestMapping(value = "/msgList",method = RequestMethod.POST)
    @ResponseBody
    @LoginRequired(purview = "0")
    public String msgList(@RequestParam(value = "token", required = false) String  token,
                          @RequestParam(value = "gptid", required = false) Integer  gptid,
                         @RequestParam(value = "page", required = false) Integer  page,
                          @RequestParam(value = "searchKey"        , required = false, defaultValue = "") String searchKey,
                          @RequestParam(value = "limit"       , required = false, defaultValue = "50") Integer limit){
        try {
            if(limit>300){
                limit = 300;
            }
            Map map =redisHelp.getMapValue(this.dataprefix+"_"+"userInfo"+token,redisTemplate);
            Integer uid =Integer.parseInt(map.get("uid").toString());
            TypechoGpt gpt = gptService.selectByKey(gptid);
            if(gpt==null){
                return Result.getResultJson(0, "当前大模型不存在", null);
            }
            //是否已经聊天过
            TypechoGptChat gptChat = new TypechoGptChat();
            gptChat.setUid(uid);
            gptChat.setGptid(gptid);
            List<TypechoGptChat> chats = gptChatService.selectList(gptChat);
            if(chats.size() < 1){
                return Result.getResultJson(0, "你还未发起过聊天", null);
            }

            List jsonList = new ArrayList();
            List cacheList = redisHelp.getList(this.dataprefix+"_"+"gptmsgList_"+gptid+"_"+uid+"_"+page+"_"+limit+"_"+searchKey,redisTemplate);

            if(cacheList.size()>0){
                jsonList = cacheList;
            }else{
                TypechoGptMsg query = new TypechoGptMsg();
                query.setGptid(gptid);
                query.setUid(uid);
                PageList<TypechoGptMsg> pageList = gptMsgService.selectPage(query, page, limit,searchKey);
                List<TypechoGptMsg> list = pageList.getList();
                if(list.size() < 1){
                    JSONObject noData = new JSONObject();
                    noData.put("code" , 1);
                    noData.put("msg"  , "");
                    noData.put("data" , new ArrayList());
                    noData.put("count", 0);
                    return noData.toString();
                }
                for (int i = 0; i < list.size(); i++) {
                    Map json = JSONObject.parseObject(JSONObject.toJSONString(list.get(i)), Map.class);
                    TypechoGptMsg msg = list.get(i);
                    Integer userid = msg.getUid();
                    Map userJson = UserStatus.getUserInfo(userid,apiconfigService,usersService);
                    json.put("userJson",userJson);
                    jsonList.add(json);
                }
            }
            redisHelp.delete(this.dataprefix+"_"+"gptmsgList_"+gptid+"_"+uid+"_"+page+"_"+limit+"_"+searchKey,redisTemplate);
            redisHelp.setList(this.dataprefix+"_"+"gptmsgList_"+gptid+"_"+uid+"_"+page+"_"+limit+"_"+searchKey,jsonList,120,redisTemplate);
            JSONObject response = new JSONObject();
            response.put("code" , 1);
            response.put("msg"  , "");
            response.put("data" , jsonList);
            response.put("count", jsonList.size());
            return response.toString();
        }catch (Exception e){
            e.printStackTrace();
            return Result.getResultJson(0,"接口请求异常，请联系管理员",null);

        }
    }

    /***
     * 管理员消息分页查询
     */
    @RequestMapping(value = "/systemMsgList",method = RequestMethod.POST)
    @ResponseBody
    @LoginRequired(purview = "2")
    public String systemMsgList(@RequestParam(value = "token", required = false) String  token,
                                @RequestParam(value = "searchParams", required = false) String  searchParams,
                          @RequestParam(value = "page", required = false) Integer  page,
                          @RequestParam(value = "searchKey"        , required = false, defaultValue = "") String searchKey,
                          @RequestParam(value = "limit"       , required = false, defaultValue = "50") Integer limit){
        try {
            if(limit>300){
                limit = 300;
            }
            TypechoGptMsg query = new TypechoGptMsg();
            String sqlParams = "null";
            Map map =redisHelp.getMapValue(this.dataprefix+"_"+"userInfo"+token,redisTemplate);
            if (StringUtils.isNotBlank(searchParams)) {
                JSONObject object = JSON.parseObject(searchParams);
                //如果不是管理员或者编辑，则只查询开放状态评论
                query = object.toJavaObject(TypechoGptMsg.class);
                Map paramsJson = JSONObject.parseObject(JSONObject.toJSONString(query), Map.class);
                sqlParams = paramsJson.toString();
            }

            List jsonList = new ArrayList();
            List cacheList = redisHelp.getList(this.dataprefix+"_"+"systemMsgList_"+page+"_"+limit+"_"+searchKey+"_"+sqlParams,redisTemplate);

            if(cacheList.size()>0){
                jsonList = cacheList;
            }else{
                PageList<TypechoGptMsg> pageList = gptMsgService.selectPage(query, page, limit,searchKey);
                List<TypechoGptMsg> list = pageList.getList();
                if(list.size() < 1){
                    JSONObject noData = new JSONObject();
                    noData.put("code" , 1);
                    noData.put("msg"  , "");
                    noData.put("data" , new ArrayList());
                    noData.put("count", 0);
                    return noData.toString();
                }
                for (int i = 0; i < list.size(); i++) {
                    Map json = JSONObject.parseObject(JSONObject.toJSONString(list.get(i)), Map.class);
                    TypechoGptMsg msg = list.get(i);
                    Integer userid = msg.getUid();
                    Map userJson = UserStatus.getUserInfo(userid,apiconfigService,usersService);
                    json.put("userJson",userJson);

                    Integer gptId = msg.getGptid();
                    TypechoGpt gpt = gptService.selectByKey(gptId);
                    Map gptJson = JSONObject.parseObject(JSONObject.toJSONString(gpt), Map.class);
                    json.put("gptJson",gptJson);
                    jsonList.add(json);
                }
            }
            redisHelp.delete(this.dataprefix+"_"+"systemMsgList_"+page+"_"+limit+"_"+searchKey+"_"+sqlParams,redisTemplate);
            redisHelp.setList(this.dataprefix+"_"+"systemMsgList_"+page+"_"+limit+"_"+searchKey+"_"+sqlParams,jsonList,120,redisTemplate);
            JSONObject response = new JSONObject();
            response.put("code" , 1);
            response.put("msg"  , "");
            response.put("data" , jsonList);
            response.put("count", jsonList.size());
            return response.toString();
        }catch (Exception e){
            e.printStackTrace();
            return Result.getResultJson(0,"接口请求异常，请联系管理员",null);

        }
    }

    //gpt管控和查询
    /***
     * 增加AI
     */
    @RequestMapping(value = "/gptAdd")
    @ResponseBody
    @LoginRequired(purview = "2")
    public String gptAdd(@RequestParam(value = "params", required = false) String  params,
                         @RequestParam(value = "token", required = false) String  token) {
        try{
            Map map =redisHelp.getMapValue(this.dataprefix+"_"+"userInfo"+token,redisTemplate);
            Integer uid =Integer.parseInt(map.get("uid").toString());
            TypechoGpt insert = null;
            if (StringUtils.isNotBlank(params)) {
                JSONObject object = JSON.parseObject(params);
                insert = object.toJavaObject(TypechoGpt.class);
            }
            if(insert.getName()==null||insert.getAvatar()==null){
                return Result.getResultJson(0,"请传入必要的参数",null);
            }
            Long date = System.currentTimeMillis();
            String curTime = String.valueOf(date).substring(0,10);
            insert.setCreated(Integer.parseInt(curTime));
            int rows = gptService.insert(insert);
            if(rows>0){
                redisHelp.deleteKeysWithPattern("*"+this.dataprefix+"_gptList_*",redisTemplate,this.dataprefix);
            }
            editFile.setLog("管理员"+uid+"新增了GPT");
            JSONObject response = new JSONObject();
            response.put("code" , rows);
            response.put("msg"  , rows > 0 ? "添加成功" : "添加失败");
            return response.toString();
        }catch (Exception e){
            e.printStackTrace();
            return Result.getResultJson(0,"接口请求异常，请联系管理员",null);
        }

    }
    /***
     * 修改AI
     */
    @RequestMapping(value = "/gptEdit")
    @ResponseBody
    @LoginRequired(purview = "2")
    public String gptEdit(@RequestParam(value = "params", required = false) String  params,
                             @RequestParam(value = "token", required = false) String  token) {
        try{
            TypechoGpt update = null;
            Map map =redisHelp.getMapValue(this.dataprefix+"_"+"userInfo"+token,redisTemplate);
            Integer uid =Integer.parseInt(map.get("uid").toString());
            if (StringUtils.isNotBlank(params)) {
                JSONObject object = JSON.parseObject(params);
                update = object.toJavaObject(TypechoGpt.class);
            }
            if(update.getName()==null||update.getAvatar()==null){
                return Result.getResultJson(0,"请传入必要的参数",null);
            }
            Integer gptID = update.getId();
            TypechoGpt gpt = gptService.selectByKey(gptID);
            if(gpt==null){
                return Result.getResultJson(0,"数据不存在",null);
            }
            if(update.getType()!=null){
                if(!gpt.getType().equals(update.getType())){
                    return Result.getResultJson(0,"为防止数据异常，类型Type不允许修改",null);
                }
            }

            int rows = gptService.update(update);
            if(rows>0){
                redisHelp.deleteKeysWithPattern("*"+this.dataprefix+"_gptInfo_"+update.getId()+"_*",redisTemplate,this.dataprefix);
                redisHelp.deleteKeysWithPattern("*"+this.dataprefix+"_gptList_*",redisTemplate,this.dataprefix);
            }
            editFile.setLog("管理员"+uid+"修改了GPT"+update.getId());
            JSONObject response = new JSONObject();
            response.put("code" , rows);
            response.put("msg"  , rows > 0 ? "修改成功" : "修改失败");
            return response.toString();
        }catch (Exception e){
            e.printStackTrace();
            return Result.getResultJson(0,"接口请求异常，请联系管理员",null);
        }
    }

    /***
     * 删除AI
     */
    @RequestMapping(value = "/gptDelete")
    @ResponseBody
    @LoginRequired(purview = "2")
    public String gptDelete(@RequestParam(value = "token", required = false,defaultValue = "") String  token,
                            @RequestParam(value = "id", required = false) String  id) {

        try{
            TypechoGpt gpt = gptService.selectByKey(id);
            if(gpt == null){
                return Result.getResultJson(0,"数据不存在",null);
            }
            int rows = gptService.delete(id);
            if(rows > 0){
                //删除该gpt所有消息和聊天室
                jdbcTemplate.execute("DELETE FROM "+this.prefix+"_gpt_chat WHERE gptid="+id+";");
                jdbcTemplate.execute("DELETE FROM "+this.prefix+"_gpt_msg WHERE gptid="+id+";");
                redisHelp.deleteKeysWithPattern("*"+this.dataprefix+"_gptInfo_"+id+"_*",redisTemplate,this.dataprefix);
                redisHelp.deleteKeysWithPattern("*"+this.dataprefix+"_gptList_*",redisTemplate,this.dataprefix);
            }
            JSONObject response = new JSONObject();
            response.put("code" , rows);
            response.put("msg"  , rows > 0 ? "删除成功" : "删除失败");
            return response.toString();
        }catch (Exception e){
            e.printStackTrace();
            return Result.getResultJson(0,"接口请求异常，请联系管理员",null);
        }
    }

    /***
     * AI列表
     */
    @RequestMapping(value = "/gptList")
    @ResponseBody
    @LoginRequired(purview = "-1")
    public String gptList (@RequestParam(value = "searchParams", required = false) String  searchParams,
                           @RequestParam(value = "searchKey"        , required = false, defaultValue = "") String searchKey,
                            @RequestParam(value = "page"        , required = false, defaultValue = "1") Integer page,
                           @RequestParam(value = "order"        , required = false, defaultValue = "created") String order,
                            @RequestParam(value = "limit"       , required = false, defaultValue = "15") Integer limit) {
        List jsonList = new ArrayList();
        TypechoGpt query = new TypechoGpt();
        String sqlParams = "null";
        Integer total = 0;
        if (StringUtils.isNotBlank(searchParams)) {
            JSONObject object = JSON.parseObject(searchParams);
            query = object.toJavaObject(TypechoGpt.class);
            Map paramsJson = JSONObject.parseObject(JSONObject.toJSONString(query), Map.class);
            sqlParams = paramsJson.toString();
        }
        total = gptService.total(query,searchKey);
        List cacheList = redisHelp.getList(this.dataprefix+"_"+"gptList_"+page+"_"+limit+"_"+sqlParams+"_"+order+"_"+searchKey,redisTemplate);
        try {
            if (cacheList.size() > 0) {
                jsonList = cacheList;
            } else {
                PageList<TypechoGpt> pageList = gptService.selectPage(query, page, limit, searchKey,order);
                List<TypechoGpt> list = pageList.getList();
                if(list.size() < 1){
                    JSONObject noData = new JSONObject();
                    noData.put("code" , 1);
                    noData.put("msg"  , "");
                    noData.put("data" , new ArrayList());
                    noData.put("count", 0);
                    noData.put("total", total);
                    return noData.toString();
                }
                for (int i = 0; i < list.size(); i++) {
                    Map json = JSONObject.parseObject(JSONObject.toJSONString(list.get(i)), Map.class);
                    json.remove("appId");
                    json.remove("apiKey");
                    jsonList.add(json);
                }
                redisHelp.delete(this.dataprefix+"_"+"gptList_"+page+"_"+limit+"_"+sqlParams+"_"+order+"_"+searchKey,redisTemplate);
                redisHelp.setList(this.dataprefix+"_"+"gptList_"+page+"_"+limit+"_"+sqlParams+"_"+order+"_"+searchKey,jsonList,300,redisTemplate);
            }
        }catch (Exception e){
            e.printStackTrace();
            if(cacheList.size()>0){
                jsonList = cacheList;
            }
        }
        JSONObject response = new JSONObject();
        response.put("code" , 1);
        response.put("msg"  , "");
        response.put("data" , jsonList);
        response.put("count", jsonList.size());
        response.put("total", total);
        return response.toString();

    }


    /***
     * 查询AI详情
     */
    @RequestMapping(value = "/gptInfo")
    @ResponseBody
    @LoginRequired(purview = "0")
    public String gptInfo(@RequestParam(value = "id", required = false) String  id,
                          @RequestParam(value = "token", required = false,defaultValue = "") String  token){
        try{
            Map gptJson = new HashMap<>();

            Integer uStatus = UStatus.getStatus(token,this.dataprefix,redisTemplate);
            String group = "";
            Map map = new HashMap();
            Integer uid = 0;
            if(uStatus.equals(1)){
                map =redisHelp.getMapValue(this.dataprefix+"_"+"userInfo"+token,redisTemplate);
                group = map.get("group").toString();
                uid =Integer.parseInt(map.get("uid").toString());
            }
            Map cacheInfo = redisHelp.getMapValue(this.dataprefix+"_"+"gptInfo_"+id+"_"+uid,redisTemplate);
            if(cacheInfo.size()>0){
                gptJson = cacheInfo;
            }else{
                TypechoGpt gpt = gptService.selectByKey(id);
                if(gpt==null){
                    return Result.getResultJson(0,"数据不存在",null);
                }
                gptJson = JSONObject.parseObject(JSONObject.toJSONString(gpt), Map.class);
                if(!group.equals("administrator")){
                    gptJson.remove("appId");
                    gptJson.remove("apiKey");
                }
                redisHelp.delete(this.dataprefix+"_"+"gptInfo_"+id+"_"+uid,redisTemplate);
                redisHelp.setKey(this.dataprefix+"_"+"gptInfo_"+id+"_"+uid,gptJson,600,redisTemplate);
            }

            JSONObject response = new JSONObject();

            response.put("code", 1);
            response.put("msg", "");
            response.put("data", gptJson);

            return response.toString();
        }catch (Exception e){
            e.printStackTrace();
            JSONObject response = new JSONObject();
            response.put("code", 0);
            response.put("msg", "");
            response.put("data", null);

            return response.toString();
        }

    }


    /***
     * AI聊天室列表
     */
    @RequestMapping(value = "/gptChatList")
    @ResponseBody
    @LoginRequired(purview = "0")
    public String gptChatList (@RequestParam(value = "searchParams", required = false) String  searchParams,
                           @RequestParam(value = "page"        , required = false, defaultValue = "1") Integer page,
                           @RequestParam(value = "order"        , required = false, defaultValue = "created") String order,
                           @RequestParam(value = "limit"       , required = false, defaultValue = "15") Integer limit,
                               @RequestParam(value = "token", required = false,defaultValue = "") String  token) {
        List jsonList = new ArrayList();
        Integer uStatus = UStatus.getStatus(token,this.dataprefix,redisTemplate);
        String group = "";
        Map map = new HashMap();
        Integer uid = 0;
        TypechoGptChat query = new TypechoGptChat();
        String sqlParams = "null";
        Integer total = 0;
        if (StringUtils.isNotBlank(searchParams)) {
            JSONObject object = JSON.parseObject(searchParams);
            query = object.toJavaObject(TypechoGptChat.class);
            Map paramsJson = JSONObject.parseObject(JSONObject.toJSONString(query), Map.class);
            sqlParams = paramsJson.toString();
        }
        if(uStatus.equals(1)){
            map =redisHelp.getMapValue(this.dataprefix+"_"+"userInfo"+token,redisTemplate);
            uid =Integer.parseInt(map.get("uid").toString());
            group = map.get("group").toString();
            if(!group.equals("administrator")){
                query.setUid(uid);
            }
        }
        total = gptChatService.total(query);
        List cacheList = redisHelp.getList(this.dataprefix+"_"+"gptChatList_"+page+"_"+limit+"_"+uid+"_"+sqlParams+"_"+order,redisTemplate);
        try {
            if (cacheList.size() > 0) {
                jsonList = cacheList;
            } else {
                PageList<TypechoGptChat> pageList = gptChatService.selectPage(query, page, limit,order);
                List<TypechoGptChat> list = pageList.getList();
                if(jsonList.size() < 1){
                    JSONObject noData = new JSONObject();
                    noData.put("code" , 1);
                    noData.put("msg"  , "");
                    noData.put("data" , new ArrayList());
                    noData.put("count", 0);
                    noData.put("total", total);
                    return noData.toString();
                }
                for (int i = 0; i < list.size(); i++) {
                    Map json = JSONObject.parseObject(JSONObject.toJSONString(list.get(i)), Map.class);
                    Integer userid = list.get(i).getUid();
                    Map userJson = UserStatus.getUserInfo(userid,apiconfigService,usersService);
                    json.put("userJson",userJson);
                    Integer gptid = list.get(i).getGptid();
                    TypechoGpt gpt = gptService.selectByKey(gptid);
                    Map gptJson = JSONObject.parseObject(JSONObject.toJSONString(gpt), Map.class);
                    json.put("gptJson",gptJson);
                    jsonList.add(json);
                }
                redisHelp.delete(this.dataprefix+"_"+"gptChatList_"+page+"_"+limit+"_"+uid+"_"+sqlParams+"_"+order,redisTemplate);
                redisHelp.setList(this.dataprefix+"_"+"gptChatList_"+page+"_"+limit+"_"+uid+"_"+sqlParams+"_"+order,jsonList,300,redisTemplate);
            }
        }catch (Exception e){
            e.printStackTrace();
            if(cacheList.size()>0){
                jsonList = cacheList;
            }
        }
        JSONObject response = new JSONObject();
        response.put("code" , 1);
        response.put("msg"  , "");
        response.put("data" , jsonList);
        response.put("count", jsonList.size());
        response.put("total", total);
        return response.toString();

    }

    /***
     * AI聊天室详情
     */
    @RequestMapping(value = "/gptChatInfo")
    @ResponseBody
    @LoginRequired(purview = "0")
    public String gptChatInfo(@RequestParam(value = "id", required = false) String  id,
                              @RequestParam(value = "token", required = false,defaultValue = "") String  token){
        try{
            Map gptChatJson = new HashMap<>();
            Map map = new HashMap();
            Integer uid = 0;
            String group = "";
            Integer uStatus = UStatus.getStatus(token,this.dataprefix,redisTemplate);
            if(uStatus.equals(1)){
                map =redisHelp.getMapValue(this.dataprefix+"_"+"userInfo"+token,redisTemplate);
                uid =Integer.parseInt(map.get("uid").toString());
                group = map.get("group").toString();

            }
            Map cacheInfo = redisHelp.getMapValue(this.dataprefix+"_"+"gptChatInfo_"+id+"_"+uid,redisTemplate);

            if(cacheInfo.size()>0){
                gptChatJson = cacheInfo;
            }else{
                TypechoGptChat gptChat = gptChatService.selectByKey(id);
                if(gptChat==null){
                    return Result.getResultJson(0,"数据不存在",null);
                }
                gptChatJson = JSONObject.parseObject(JSONObject.toJSONString(gptChat), Map.class);
                Integer userid = gptChat.getUid();
                if(!group.equals("administrator")){
                    if(!userid.equals(uid)){
                        return Result.getResultJson(0,"你无权限查看该数据",null);
                    }
                }
                Map userJson = UserStatus.getUserInfo(userid,apiconfigService,usersService);
                gptChatJson.put("userJson",userJson);
                Integer gptid = gptChat.getGptid();
                TypechoGpt gpt = gptService.selectByKey(gptid);
                Map gptJson = JSONObject.parseObject(JSONObject.toJSONString(gpt), Map.class);
                gptChatJson.put("gptJson",gptJson);

                redisHelp.delete(this.dataprefix+"_"+"gptChatInfo_"+id+"_"+uid,redisTemplate);
                redisHelp.setKey(this.dataprefix+"_"+"gptChatInfo_"+id+"_"+uid,gptChatJson,600,redisTemplate);
            }

            JSONObject response = new JSONObject();

            response.put("code", 1);
            response.put("msg", "");
            response.put("data", gptChatJson);

            return response.toString();
        }catch (Exception e){
            e.printStackTrace();
            JSONObject response = new JSONObject();
            response.put("code", 0);
            response.put("msg", "");
            response.put("data", null);

            return response.toString();
        }

    }

    /***
     * 删除AI聊天室
     */
    @RequestMapping(value = "/gptChatDelete")
    @ResponseBody
    @LoginRequired(purview = "0")
    public String gptChatDelete(@RequestParam(value = "token", required = false,defaultValue = "") String  token,
                            @RequestParam(value = "id", required = false) Integer  id) {

        try{
            Map map =redisHelp.getMapValue(this.dataprefix+"_"+"userInfo"+token,redisTemplate);
            Integer uid =Integer.parseInt(map.get("uid").toString());
            TypechoGptChat gptChat = new TypechoGptChat();
            gptChat.setGptid(id);
            gptChat.setUid(uid);
            List<TypechoGptChat> list = gptChatService.selectList(gptChat);
            if(list.size()<1){
                return Result.getResultJson(0,"数据不存在",null);
            }else{
                gptChat = list.get(0);
            }

            String group = map.get("group").toString();
            //如果不是管理员，则只能删除自己的聊天室
            if(!group.equals("administrator")){

                if(!gptChat.getUid().equals(uid)){
                    return Result.getResultJson(0,"你无权进行操作",null);
                }
            }
            int rows = gptChatService.delete(gptChat.getId());
            if(rows > 0){
                //删除该聊天室所有消息
                jdbcTemplate.execute("DELETE FROM "+this.prefix+"_gpt_msg WHERE gptid="+gptChat.getGptid()+";");
                redisHelp.deleteKeysWithPattern("*"+this.dataprefix+"gptChatInfo_"+id+"_*",redisTemplate,this.dataprefix);
                redisHelp.deleteKeysWithPattern("*"+this.dataprefix+"_gptChatList_*",redisTemplate,this.dataprefix);
                redisHelp.deleteKeysWithPattern("*"+this.dataprefix+"_gptlastList_"+gptChat.getGptid()+"_"+gptChat.getUid()+"_*",redisTemplate,this.dataprefix);
                redisHelp.deleteKeysWithPattern("*"+this.dataprefix+"_gptmsgList_"+gptChat.getGptid()+"_"+gptChat.getUid()+"_*",redisTemplate,this.dataprefix);

            }
            JSONObject response = new JSONObject();
            response.put("code" , rows);
            response.put("msg"  , rows > 0 ? "删除成功" : "删除失败");
            return response.toString();
        }catch (Exception e){
            e.printStackTrace();
            return Result.getResultJson(0,"接口请求异常，请联系管理员",null);
        }
    }


}
