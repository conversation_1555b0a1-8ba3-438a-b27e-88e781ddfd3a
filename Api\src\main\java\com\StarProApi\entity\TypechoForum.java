package com.StarProApi.entity;

import java.io.Serializable;
import lombok.Data;
import java.util.Date;
import java.util.List;

/**
 * TypechoForum
 * <AUTHOR> 2023-02-20
 */
@Data
public class TypechoForum implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id  
     */
    private Integer id;

    /**
     * title  帖子标题
     */
    private String title;

    /**
     * section  所属版块
     */
    private Integer section;

    /**
     * typeid  所属板块子类
     */
    private Integer typeid;

    /**
     * created  发布时间
     */
    private Integer created;

    /**
     * modified  修改时间
     */
    private Integer modified;

    /**
     * text  帖子内容
     */
    private String text;

    /**
     * authorId  作者uid
     */
    private Integer authorId;

    /**
     * status  帖子状态，0审核，1发布，3锁定
     */
    private Integer status;

    /**
     * commentsNum  回帖数量
     */
    private Integer commentsNum;

    /**
     * views  阅读数量
     */
    private Integer views;

    /**
     * likes  点赞数量
     */
    private Integer likes;

    /**
     * isTop  是否置顶，0普通，1当前版块置顶，2全局置顶
     */
    private Integer isTop;


    /**
     * isrecommend  是否加精&推荐
     */
    private Integer isrecommend;

    /**
     * isswiper  是否轮播
     */
    private Integer isswiper;

    /**
     * replyTime  回复时间
     */
    private Integer replyTime;

    /**
     * isMd  是否为markdown文本
     */
    private Integer isMd;

    /**
     * sid  商品id，只用于付费阅读
     */
    private Integer sid;


}