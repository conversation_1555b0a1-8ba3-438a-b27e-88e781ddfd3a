package com.StarProApi.service;

import java.util.Map;
import java.util.List;
import com.StarProApi.entity.*;
import com.StarProApi.common.PageList;

/**
 * 业务层
 * TypechoForumSectionService
 * <AUTHOR>
 * @date 2023/02/20
 */
public interface TypechoForumSectionService {

    /**
     * [新增]
     **/
    int insert(TypechoForumSection typechoForumSection);

    /**
     * [批量新增]
     **/
    int batchInsert(List<TypechoForumSection> list);

    /**
     * [更新]
     **/
    int update(TypechoForumSection typechoForumSection);

    /**
     * [删除]
     **/
    int delete(Object key);

    /**
     * [批量删除]
     **/
    int batchDelete(List<Object> keys);

    /**
     * [主键查询]
     **/
    TypechoForumSection selectByKey(Object key);

    /**
     * [条件查询]
     **/
    List<TypechoForumSection> selectList (TypechoForumSection typechoForumSection);

    /**
     * [分页条件查询]
     **/
    PageList<TypechoForumSection> selectPage (TypechoForumSection typechoForumSection, Integer page, Integer pageSize,String order,String searchKey);

    /**
     * [总量查询]
     **/
    int total(TypechoForumSection typechoForumSection);
}
