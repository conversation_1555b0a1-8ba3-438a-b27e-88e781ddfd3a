<?php
session_start();
?>



<?php
include_once 'Nav.php';
$id = $_GET['id'];

$article = "SELECT * FROM typecho_forum_comment WHERE id='$id' limit 1";
$resarticle = mysqli_query($connect, $article);
$mod = mysqli_fetch_array($resarticle);
?>
<div class="row">

    <div class="col-lg-12">
        <div class="card">
            <div class="card-body">
                <h4 class="header-title mb-3 size_18">审核评论</h4>
                    <div class="form-group col-sm-4">
                        <label for="validationCustom01">发布者UID</label>
                        <input type="text" class="form-control" id="validationCustom01" placeholder="请输入发布者UID"
                               name="uid" value="<?php echo $mod['uid'] ?>" readonly>
                    </div>
                    <label for="validationCustom01">评论内容</label>
                        <textarea id="notice" class="form-control" rows="6" name="text" readonly><?php echo $mod['text'] ?></textarea>
                        <br />
                    <div class="form-group mb-3 text_right">
                        <a class="fabu" onclick="Pass('<?php echo $id ;?>')">
                            <button class="btn btn-primary" id="payoutPost" style="margin-right:10px">通过</button>
                        </a>
                        <a class="fabu" onclick="Refuse('<?php echo $id ;?>')">
                            <button class="btn btn-danger" id="payoutPost">驳回</button>
                        </a>
                    </div>

            </div> <!-- end card-body-->
        </div> <!-- end card-->
    </div> <!-- end col-->
</div>


<!--<script src="https://cdn.bootcss.com/jquery/1.11.3/jquery.min.js"></script>-->


<script>
    function Pass(id) {
        if (confirm('您确认要通过该评论吗？')) {
            location.href = 'comTzAuditPost.php?id=' + id +'&status=Pass';
        }
    }
    function Refuse(id) {
        if (confirm('您确认要拒绝该评论吗？')) {
            location.href = 'comTzAuditPost.php?id=' + id +'&status=Refuse';
        }
    }
    
</script>

<?php
include_once 'Footer.php';
?>

</body>
</html>