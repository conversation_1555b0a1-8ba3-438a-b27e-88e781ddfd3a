package com.StarProApi.service;

import java.util.Map;
import java.util.List;
import com.StarProApi.entity.*;
import com.StarProApi.common.PageList;

/**
 * 业务层
 * TypechoForumCommentService
 * <AUTHOR>
 * @date 2023/02/20
 */
public interface TypechoForumCommentService {

    /**
     * [新增]
     **/
    int insert(TypechoForumComment typechoForumComment);

    /**
     * [批量新增]
     **/
    int batchInsert(List<TypechoForumComment> list);

    /**
     * [更新]
     **/
    int update(TypechoForumComment typechoForumComment);

    /**
     * [删除]
     **/
    int delete(Object key);

    /**
     * [批量删除]
     **/
    int batchDelete(List<Object> keys);

    /**
     * [主键查询]
     **/
    TypechoForumComment selectByKey(Object key);

    /**
     * [条件查询]
     **/
    List<TypechoForumComment> selectList (TypechoForumComment typechoForumComment);

    /**
     * [分页条件查询]
     **/
    PageList<TypechoForumComment> selectPage (TypechoForumComment typechoForumComment, Integer page, Integer pageSize,String order,String searchKey);

    /**
     * [总量查询]
     **/
    int total(TypechoForumComment typechoForumComment,String searchKey);
}
