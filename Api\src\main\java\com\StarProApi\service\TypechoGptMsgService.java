package com.StarProApi.service;

import java.util.Map;
import java.util.List;
import com.StarProApi.entity.*;
import com.StarProApi.common.PageList;

/**
 * 业务层
 * TypechoGptMsgService
 * <AUTHOR>
 * @date 2024/06/17
 */
public interface TypechoGptMsgService {

    /**
     * [新增]
     **/
    int insert(TypechoGptMsg typechoGptMsg);

    /**
     * [批量新增]
     **/
    int batchInsert(List<TypechoGptMsg> list);

    /**
     * [更新]
     **/
    int update(TypechoGptMsg typechoGptMsg);

    /**
     * [删除]
     **/
    int delete(Object key);

    /**
     * [批量删除]
     **/
    int batchDelete(List<Object> keys);

    /**
     * [主键查询]
     **/
    TypechoGptMsg selectByKey(Object key);

    /**
     * [条件查询]
     **/
    List<TypechoGptMsg> selectList (TypechoGptMsg typechoGptMsg);

    /**
     * [分页条件查询]
     **/
    PageList<TypechoGptMsg> selectPage (TypechoGptMsg typechoGptMsg, Integer page, Integer pageSize,String searchKey);

    /**
     * [总量查询]
     **/
    int total(TypechoGptMsg typechoGptMsg,String searchKey);
}
