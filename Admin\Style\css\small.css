.col-sm-12 {
  grid-column: span 12;
}
.col-sm-11 {
  grid-column: span 11;
}
.col-sm-10 {
  grid-column: span 10;
}
.col-sm-9 {
  grid-column: span 9;
}
.col-sm-8 {
  grid-column: span 8;
}
.col-sm-7 {
  grid-column: span 7;
}
.col-sm-6 {
  grid-column: span 6;
}
.col-sm-5 {
  grid-column: span 5;
}
.col-sm-4 {
  grid-column: span 4;
}
.col-sm-3 {
  grid-column: span 3;
}
.col-sm-2 {
  grid-column: span 2;
}
.col-sm-1 {
  grid-column: span 1;
}
.central {
  width: 728px;
  margin: 2rem auto;
  padding: 1rem;
  box-sizing: border-box;
}
.love_img img {
    width: 100%!important;
}
.central.bg .row .card .inputbox {
    display: flex;
    justify-content: center;
    align-items: flex-start;
}