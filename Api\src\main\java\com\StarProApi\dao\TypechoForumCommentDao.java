package com.StarProApi.dao;

import com.StarProApi.entity.*;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * dao层接口
 * TypechoForumCommentDao
 * <AUTHOR>
 * @date 2023/02/20
 */
@Mapper
public interface TypechoForumCommentDao {

    /**
     * [新增]
     **/
    int insert(TypechoForumComment typechoForumComment);

    /**
     * [批量新增]
     **/
    int batchInsert(List<TypechoForumComment> list);

    /**
     * [更新]
     **/
    int update(TypechoForumComment typechoForumComment);

    /**
     * [删除]
     **/
    int delete(Object key);

    /**
     * [批量删除]
     **/
    int batchDelete(List<Object> list);

    /**
     * [主键查询]
     **/
    TypechoForumComment selectByKey(Object key);

    /**
     * [条件查询]
     **/
    List<TypechoForumComment> selectList (TypechoForumComment typechoForumComment);

    /**
     * [分页条件查询]
     **/
    List<TypechoForumComment> selectPage (@Param("typechoForumComment") TypechoForumComment typechoForumComment, @Param("page") Integer page, @Param("pageSize") Integer pageSize,@Param("order") String order,@Param("searchKey") String searchKey);

    /**
     * [总量查询]
     **/
    int total(@Param("typechoForumComment") TypechoForumComment typechoForumComment, @Param("searchKey") String searchKey);
}
