package com.StarProApi.service.impl;

import com.StarProApi.entity.*;
import com.StarProApi.common.PageList;
import com.StarProApi.dao.*;
import com.StarProApi.service.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 业务层实现类
 * TypechoForumModeratorServiceImpl
 * <AUTHOR>
 * @date 2023/02/20
 */
@Service
public class TypechoForumModeratorServiceImpl implements TypechoForumModeratorService {

    @Autowired
	TypechoForumModeratorDao dao;

    @Override
    public int insert(TypechoForumModerator typechoForumModerator) {
        return dao.insert(typechoForumModerator);
    }

    @Override
    public int batchInsert(List<TypechoForumModerator> list) {
    	return dao.batchInsert(list);
    }

    @Override
    public int update(TypechoForumModerator typechoForumModerator) {
    	return dao.update(typechoForumModerator);
    }

    @Override
    public int delete(Object key) {
    	return dao.delete(key);
    }

    @Override
    public int batchDelete(List<Object> keys) {
        return dao.batchDelete(keys);
    }

	@Override
	public TypechoForumModerator selectByKey(Object key) {
		return dao.selectByKey(key);
	}

	@Override
	public List<TypechoForumModerator> selectList(TypechoForumModerator typechoForumModerator) {
		return dao.selectList(typechoForumModerator);
	}

	@Override
	public PageList<TypechoForumModerator> selectPage(TypechoForumModerator typechoForumModerator, Integer offset, Integer pageSize) {
		PageList<TypechoForumModerator> pageList = new PageList<>();

		int total = this.total(typechoForumModerator);

		Integer totalPage;
		if (total % pageSize != 0) {
			totalPage = (total /pageSize) + 1;
		} else {
			totalPage = total /pageSize;
		}

		int page = (offset - 1) * pageSize;

		List<TypechoForumModerator> list = dao.selectPage(typechoForumModerator, page, pageSize);

		pageList.setList(list);
		pageList.setStartPageNo(offset);
		pageList.setPageSize(pageSize);
		pageList.setTotalCount(total);
		pageList.setTotalPageCount(totalPage);
		return pageList;
	}

	@Override
	public int total(TypechoForumModerator typechoForumModerator) {
		return dao.total(typechoForumModerator);
	}
}