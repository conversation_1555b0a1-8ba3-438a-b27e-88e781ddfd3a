package com.StarProApi.entity;

import java.io.Serializable;
import lombok.Data;
import java.util.Date;
import java.util.List;

/**
 * TypechoForumComment
 * <AUTHOR> 2023-02-20
 */
@Data
public class TypechoForumComment implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id  
     */
    private Integer id;

    /**
     * uid  评论者
     */
    private Integer uid;

    /**
     * created  评论时间
     */
    private Integer created;

    /**
     * forumid  所属帖子
     */
    private Integer forumid;

    /**
     * likes  评论点赞
     */
    private Integer likes;

    /**
     * text  评论内容
     */
    private String text;

    /**
     * parent  上级评论
     */
    private Integer parent;

    /**
     * section  所属版块
     */
    private Integer section;

    /**
     * section  帖子评论状态，0为未审核，1为显示
     */
    private Integer status;

    /**
     * pic
     */
    private String pic;
}