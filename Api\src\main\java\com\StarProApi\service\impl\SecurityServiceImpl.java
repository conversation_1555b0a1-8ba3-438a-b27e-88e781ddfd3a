package com.StarProApi.service.impl;

import com.StarProApi.common.HttpClient;
import com.StarProApi.common.RedisHelp;
import com.aliyun.dysmsapi20170525.Client;
import com.aliyun.dysmsapi20170525.models.SendSmsRequest;
import com.aliyun.dysmsapi20170525.models.SendSmsResponse;
import com.aliyun.tea.TeaException;
import com.aliyun.teautil.Common;
import com.aliyun.teautil.models.RuntimeOptions;
import com.StarProApi.common.UserStatus;
import com.StarProApi.entity.*;
import com.StarProApi.common.PageList;
import com.StarProApi.dao.*;
import com.StarProApi.service.*;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.tencentcloudapi.common.Credential;
import com.tencentcloudapi.common.exception.TencentCloudSDKException;
import com.tencentcloudapi.common.profile.ClientProfile;
import com.tencentcloudapi.common.profile.HttpProfile;
import com.tencentcloudapi.ims.v20201229.ImsClient;
import com.tencentcloudapi.ims.v20201229.models.ImageModerationRequest;
import com.tencentcloudapi.ims.v20201229.models.ImageModerationResponse;
import com.tencentcloudapi.tms.v20201229.TmsClient;
import com.tencentcloudapi.tms.v20201229.models.TextModerationRequest;
import com.tencentcloudapi.tms.v20201229.models.TextModerationResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Random;

import static com.StarProApi.web.TypechoUsersController.createClient;

@Service
public class SecurityServiceImpl implements SecurityService {
    @Autowired
    private TypechoInboxService inboxService;

    @Autowired
    private TypechoUsersService usersService;

    @Autowired
    private TypechoApiconfigService apiconfigService;

    @Autowired
    private RedisTemplate redisTemplate;

    @Value("${web.prefix}")
    private String dataprefix;

    UserStatus UStatus = new UserStatus();
    RedisHelp redisHelp =new RedisHelp();

    HttpClient HttpClient = new HttpClient();

    @Override
    public void safetyMessage(String msg,String type){
        //向所有管理员发送警告
        try{

            TypechoUsers user = new TypechoUsers();
            user.setGroupKey("administrator");
            List<TypechoUsers> userList = usersService.selectList(user);
            for (int i = 0; i < userList.size(); i++) {
                TypechoInbox inbox = new TypechoInbox();
                Integer uid = userList.get(i).getUid();
                Long date = System.currentTimeMillis();
                String created = String.valueOf(date).substring(0,10);
                TypechoInbox insert = new TypechoInbox();
                insert.setUid(uid);
                insert.setTouid(uid);
                insert.setType(type);
                insert.setText(msg);
                insert.setCreated(Integer.parseInt(created));
                inboxService.insert(insert);
            }
            System.err.println("有用户存在违规行为，已向所有管理员发送警告");
        }catch (Exception e){
            System.err.println(e);
        }
    }

    //文本违规检测
    @Override
    public Map textViolation(String text){
        try{
            TypechoApiconfig apiconfig = UStatus.getConfig(this.dataprefix,apiconfigService,redisTemplate);
            // 密钥可前往官网控制台 https://console.cloud.tencent.com/cam/capi 进行获取
            Credential cred = new Credential(apiconfig.getCmsSecretId(),apiconfig.getCmsSecretKey());
            // 实例化一个http选项，可选的，没有特殊需求可以跳过
            HttpProfile httpProfile = new HttpProfile();
            httpProfile.setEndpoint("tms.tencentcloudapi.com");
            // 实例化一个client选项，可选的，没有特殊需求可以跳过
            ClientProfile clientProfile = new ClientProfile();
            clientProfile.setHttpProfile(httpProfile);
            // 实例化要请求产品的client对象,clientProfile是可选的
            TmsClient client = new TmsClient(cred, apiconfig.getCmsRegion(), clientProfile);
            // 实例化一个请求对象,每个接口都会对应一个request对象
            TextModerationRequest req = new TextModerationRequest();
            req.setContent(text);
            // 返回的resp是一个TextModerationResponse的实例，与请求对象对应
            TextModerationResponse resp = client.TextModeration(req);
            // 输出json格式的字符串回包
            String resText = TextModerationResponse.toJsonString(resp);
            System.out.println(resText);
            Map resMap = JSONObject.parseObject(JSON.parseObject(resText).toString());
            return resMap;
        } catch (TencentCloudSDKException e) {
            System.out.println(e.toString());
            return null;

        }

    }

    //发送短信验证码
    @Override
    public Integer sendSMSCode(String phone){
        Integer status = 0;
        try {
            TypechoApiconfig apiconfig = UStatus.getConfig(this.dataprefix,apiconfigService,redisTemplate);
            if(apiconfig.getSmsType().equals(0)){
                // 替换成自己的
                String accesskeyId = apiconfig.getCodeAccessKeyId();
                String accesskeySecret = apiconfig.getCodeAccessKeySecret();
                Client client = createClient(accesskeyId, accesskeySecret,apiconfig.getCodeEndpoint());
                //生成六位随机验证码
                Random random = new Random();
                String randCode = "";
                for (int i = 0; i < 6; i++) {
                    randCode += random.nextInt(10);
                }
                //存入redis并发送邮件

                JSONObject jsonObject = new JSONObject(1);
                jsonObject.put("code", randCode);
                SendSmsRequest sendSmsRequest = new SendSmsRequest()
                        .setSignName(apiconfig.getCodeSignName())
                        .setTemplateCode(apiconfig.getCodeTemplate())
                        .setPhoneNumbers(phone)
                        .setTemplateParam(jsonObject.toJSONString());
                RuntimeOptions runtime = new RuntimeOptions();
                String returnMessage = null;
                try {
                    // 复制代码运行请自行打印 API 的返回值
                    SendSmsResponse sendSmsResponse = client.sendSmsWithOptions(sendSmsRequest, runtime);
                    // 获取返回体code
                    String code = sendSmsResponse.getBody().getCode();
                    // 获取返回体状态
                    Integer statusCode = sendSmsResponse.getStatusCode();
                    System.out.println(JSONObject.toJSONString(sendSmsResponse));
                    redisHelp.delete(this.dataprefix + "_" + "sendSMS" + phone, redisTemplate);
                    redisHelp.setRedis(this.dataprefix + "_" + "sendSMS" + phone, randCode, 1800, redisTemplate);
                    status = 1;
                } catch (TeaException error) {
                    // 如有需要，请打印 error
                    System.err.println(Common.assertAsString(error.message));
                    status = 0;
                } catch (Exception errorMsg) {
                    TeaException error = new TeaException(errorMsg.getMessage(), errorMsg);
                    // 如有需要，请打印 error
                    System.err.println(Common.assertAsString(error.message));
                    status = 0;
                }
            }
            if(apiconfig.getSmsType().equals(1)){
                //生成六位随机验证码
                Random random = new Random();
                String randCode = "";
                for (int i = 0; i < 4; i++) {
                    randCode += random.nextInt(10);
                }
                String username = apiconfig.getSmsbaoUsername();    //在短信宝注册的用户名
                String APIKey = apiconfig.getSmsbaoApikey();
                String content = "【" + apiconfig.getSmsbaoTemplate() + "】您的验证码是"+ randCode + "。如非本人操作，请忽略本短信" ;
                String url = "http://api.smsbao.com/sms?u=" + username + "&p=" + APIKey + "&m=" + phone + "&c=" + content;
                String res = HttpClient.doGet(url);
                if (res == null) {
                    status = 0;
                }
                // res 返回的是"0\r\n"，帮我折分后取0前面的数字即可
                int number = Integer.parseInt(res.substring(0, 1));
                if (number == 0) {
                    redisHelp.delete(this.dataprefix + "_" + "sendSMS" + phone, redisTemplate);
                    redisHelp.setRedis(this.dataprefix + "_" + "sendSMS" + phone, randCode, 1800, redisTemplate);
                    status = 1;
                } else {
                    status = 0;
                }
            }
        }catch (Exception e){
            return 0;
        }
        return  status;
    }
    //图片违规检测
    @Override
    public Map picViolation(String text,Integer type){
        try{
            TypechoApiconfig apiconfig = UStatus.getConfig(this.dataprefix,apiconfigService,redisTemplate);
            // 密钥可前往官网控制台 https://console.cloud.tencent.com/cam/capi 进行获取
            Credential cred = new Credential(apiconfig.getCmsSecretId(),apiconfig.getCmsSecretKey());
            // 实例化一个http选项，可选的，没有特殊需求可以跳过
            HttpProfile httpProfile = new HttpProfile();
            httpProfile.setEndpoint("ims.tencentcloudapi.com");
            // 实例化一个client选项，可选的，没有特殊需求可以跳过
            ClientProfile clientProfile = new ClientProfile();
            clientProfile.setHttpProfile(httpProfile);
            // 实例化要请求产品的client对象,clientProfile是可选的
            ImsClient client = new ImsClient(cred, apiconfig.getCmsRegion(), clientProfile);
            // 实例化一个请求对象,每个接口都会对应一个request对象
            ImageModerationRequest req = new ImageModerationRequest();
            //type为0，则为base64检测，其他值为远程url检测。
            if(type.equals(0)){
                req.setFileContent(text);
            }else{
                req.setFileUrl(text);
            }
            // 返回的resp是一个ImageModerationResponse的实例，与请求对象对应
            ImageModerationResponse resp = client.ImageModeration(req);
            // 输出json格式的字符串回包
            String resText = ImageModerationResponse.toJsonString(resp);
            System.out.println(resText);
            Map resMap = JSONObject.parseObject(JSON.parseObject(resText).toString());
            return resMap;
        } catch (TencentCloudSDKException e) {
            System.out.println(e.toString());
            return null;
        }
    }
}
