<?php
session_start();
$captcha = substr(str_shuffle('1345689'), 0, 4);
$_SESSION['captcha'] = $captcha;
$image = imagecreatetruecolor(100, 30);

$bg_color = imagecolorallocate($image, 255, 255, 255);
$text_color = imagecolorallocate($image, 0, 0, 0);

imagefill($image, 0, 0, $bg_color);

for ($i = 0; $i < 5; $i++) {
    $line_color = imagecolorallocate($image, rand(0, 255), rand(0, 255), rand(0, 255));
    imageline($image, rand(0, 100), rand(0, 30), rand(0, 100), rand(0, 30), $line_color);
}

for ($i = 0; $i < 50; $i++) {
    $point_color = imagecolorallocate($image, rand(0, 255), rand(0, 255), rand(0, 255));
    imagesetpixel($image, rand(0, 100), rand(0, 30), $point_color);
}


imagestring($image, 25, 20, 8, $captcha, $text_color);


header('Content-type: image/png');
imagepng($image);

imagedestroy($image);
?>
