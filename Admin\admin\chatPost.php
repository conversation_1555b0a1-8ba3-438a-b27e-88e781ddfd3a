<?php
session_start();
?>

<?php
include_once 'connect.php';
$id = $_GET['id'];
$uid = $_GET['uid'];
$status = $_GET['status'];
$file = $_SERVER['PHP_SELF'];
$created = time();

if (isset($_SESSION['loginadmin']) && $_SESSION['loginadmin'] <> '') {
$redisKeys = $connectRedis->keys('starapi_*');
    foreach ($redisKeys as $redisKey) {
        $connectRedis->del($redisKey);
    }
    if ($status === 'Noban') {
        $sql = "UPDATE typecho_chat SET ban = '0' WHERE id = '$id'"; 
        $result = mysqli_query($connect, $sql);
        $sql2 = "INSERT INTO typecho_chat_msg (uid, cid, text, created, type) VALUES ('$uid','$id', 'noban', '$created', '4')"; 
        $result2 = mysqli_query($connect, $sql2);
        
        if ($result&&$result2) {
            echo "<script>alert('解除禁言成功');history.back();</script>";
        } else {
            echo "<script>alert('解除禁言失败')';history.back();</script>";
        }
    } else if($status === 'Ban'){
        $sql = "UPDATE typecho_chat SET ban = '$uid' WHERE id = '$id'"; 
        $result = mysqli_query($connect, $sql);
        $sql2 = "INSERT INTO typecho_chat_msg (uid, cid, text, created, type) VALUES ('$uid','$id', 'ban', '$created', '4')"; 
        $result2 = mysqli_query($connect, $sql2);
        
        if ($result&&$result2) {
            echo "<script>alert('禁言成功');history.back();</script>";
        } else {
            echo "<script>alert('禁言失败');history.back();</script>";
        }
    } else if ($status === 'Del') {
        $sql = "DELETE FROM typecho_chat WHERE id = $id";
        $result = mysqli_query($connect, $sql);
        if ($result) {
            echo "<script>alert('删除成功');history.back();</script>";
        } else {
            echo "<script>alert('删除失败')';history.back();</script>";
        }
    }  else {
        echo "<script>alert('参数错误');history.back();</script>";
    }
} else {
    echo "<script>alert('非法操作，行为已记录');location.href = 'warning.php?route=$file';</script>";
}