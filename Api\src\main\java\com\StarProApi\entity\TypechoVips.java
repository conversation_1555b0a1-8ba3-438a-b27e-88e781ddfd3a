package com.StarProApi.entity;

import java.io.Serializable;
import lombok.Data;
import java.util.Date;
import java.util.List;

/**
 * TypechoVips
 * <AUTHOR> 2023-06-09
 */
@Data
public class TypechoVips implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id  
     */
    private Integer id;

    /**
     * orderKey  排序，越大越靠前
     */
    private Integer orderKey;

    /**
     * name  套餐名称（如：月付VIP）
     */
    private String name;

    /**
     * price  套餐价格，正整数
     */
    private Integer price;

    /**
     * day  获得VIP天数
     */
    private Integer day;

    /**
     * giftDay  额外奖励天数，为0则不奖励
     */
    private Integer giftDay;

    /**
     * intro  套餐介绍
     */
    private String intro;
}