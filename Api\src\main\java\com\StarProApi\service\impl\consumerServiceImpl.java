package com.StarProApi.service.impl;

import com.StarProApi.common.PageList;
import com.StarProApi.entity.TypechoContents;
import com.StarProApi.entity.identifyConsumer;
import com.StarProApi.service.consumerService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.StarProApi.dao.consumerDao;

import java.util.List;


@Service
public class consumerServiceImpl implements consumerService {
    @Autowired
    private consumerDao consumerDao;
    @Override
    public PageList<identifyConsumer> queryInfo(identifyConsumer consumer, int page, int pageSize) {
        PageList<identifyConsumer> pageList = new PageList<>();
        page = (page - 1) * pageSize;

        List<identifyConsumer> list = consumerDao.queryInfo(consumer,page,pageSize);
        pageList.setList(list);
        pageList.setStartPageNo(page);
        pageList.setPageSize(pageSize);

        return pageList;
    }

    @Override
    public int insert(identifyConsumer dto) {
        return consumerDao.insert(dto);
    }

    @Override
    public identifyConsumer selectByKey(Object uid) {
        return consumerDao.selectByKey(uid);
    }

    @Override
    public Integer remove(Object key) {
        return consumerDao.remove(key);
    }

    @Override
    public Integer update(identifyConsumer dto) {
        return consumerDao.update(dto);
    }

}
