<?php
session_start();
?>

<?php
include_once 'connect.php';

$curl = curl_init();
$url = $API_APP_LIST.'?webkey='.$api_key;
curl_setopt_array($curl, array(
   CURLOPT_URL => $url,
   CURLOPT_RETURNTRANSFER => true,
   CURLOPT_ENCODING => '',
   CURLOPT_MAXREDIRS => 10,
   CURLOPT_TIMEOUT => 0,
   CURLOPT_SSL_VERIFYPEER => false,
   CURLOPT_SSL_VERIFYHOST => false,
   CURLOPT_FOLLOWLOCATION => true,
   CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
   CURLOPT_CUSTOMREQUEST => 'GET',
));
$response = curl_exec($curl);
$responseData = json_decode($response, true);  
?>

<?php
include_once 'Nav.php';
?>

<link href="/admin/assets/css/vendor/dataTables.bootstrap4.css" rel="stylesheet" type="text/css"/>
<link href="/admin/assets/css/vendor/responsive.bootstrap4.css" rel="stylesheet" type="text/css"/>
<link href="/admin/assets/css/vendor/buttons.bootstrap4.css" rel="stylesheet" type="text/css"/>
<link href="/admin/assets/css/vendor/select.bootstrap4.css" rel="stylesheet" type="text/css"/>
<!-- third party css end -->


<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <h4 class="header-title mb-3">应用列表<a class="fabu" href="appAdd.php">
                        <button type="button" class="btn btn-success2 btn-sm btn-rounded right_10">
                            <i class="dripicons-upload"></i> 创建
                        </button>
                    </a></h4>
                <table id="basic-textlong" class="table dt-responsive nowrap" width="100%">
                    <thead>
                    <tr>
                        <th>应用名称</th>
                        <th>应用Key</th>
                        <th>应用类型</th>
                        <th>广告联盟ID</th>
                        <th style="width: 125px;">操作</th>
                    </tr>
                    </thead>

                    <tbody>
                    <?php
                     foreach ($responseData['data'] as $app) {
                    ?>
                    <tr>
                        <td><?php echo $app['name']; ?></td> 
                        <td><?php echo $app['keyKey']; ?></td> 
                        <td>
                            <h6>
                                <?php if ($app['type'] == 'app') { ?><span class="badge badge-info-lighten">APP</span><?php } else { ?><span class="badge badge-warning-lighten">Web</span><?php } ?>
                            </h6>
                        </td>
                        <td><?php echo $app['adpid']; ?></td> 
                        <td>
                            <a href="appEdit.php?id=<?php echo $app['id']; ?>&name=<?php echo $app['name']; ?>&adpid=<?php echo $app['adpid']; ?>">
                                <button style="white-space: nowrap;" type="button" class="btn btn-info btn-rounded">
                                    <i class="dripicons-document-edit"></i> 编辑
                                </button>
                            </a>
                            <a href="javascript:del('<?php echo $app['name']; ?>', '<?php echo $app['id']; ?>');">
                                <button style="white-space: nowrap;" type="button" class="btn btn-danger btn-rounded">
                                    <i class="mdi mdi-delete-empty mr-1"></i>删除
                                </button>
                            </a>
                        </td>
                    </tr>
                    <?php
                    }
                    ?>
                    </tbody>
                </table>

            </div>  
        </div>  
    </div> 
</div>


<script>
    function del(name,id) {
        if (confirm('您确认要删除' + name + '应用吗？')) {
            location.href = 'appDel.php?id=' + id;
        }
    }
</script>


<?php
include_once 'Footer.php';
?>

<!-- third party js -->
<script src="/admin/assets/js/vendor/jquery.dataTables.min.js"></script>
<script src="/admin/assets/js/vendor/dataTables.bootstrap4.js"></script>
<script src="/admin/assets/js/vendor/dataTables.responsive.min.js"></script>
<script src="/admin/assets/js/vendor/responsive.bootstrap4.min.js"></script>
<script src="/admin/assets/js/vendor/dataTables.buttons.min.js"></script>
<script src="/admin/assets/js/vendor/buttons.bootstrap4.min.js"></script>
<script src="/admin/assets/js/vendor/buttons.html5.min.js"></script>
<script src="/admin/assets/js/vendor/buttons.flash.min.js"></script>
<script src="/admin/assets/js/vendor/buttons.print.min.js"></script>
<script src="/admin/assets/js/vendor/dataTables.keyTable.min.js"></script>
<script src="/admin/assets/js/vendor/dataTables.select.min.js"></script>
<!-- third party js ends -->
<!-- demo app -->
<script src="/admin/assets/js/pages/demo.datatable-init.js"></script>
<!-- end demo js-->


</body>
</html>