<?php
session_start();
?>
<?php
include_once 'connect.php';
$cid = $_POST['cid'];
$title = htmlspecialchars(trim($_POST['articletitle']),ENT_QUOTES);
$text = $_POST['articletext'];
$uid = trim($_POST['uid']);
$category = trim($_POST['category']);
$time = time();
$file = $_SERVER['PHP_SELF'];


if (isset($_SESSION['loginadmin']) && $_SESSION['loginadmin'] <> '') {
    $charu = "UPDATE typecho_contents SET title = '$title' , modified = '$time', text = '$text', authorId = '$uid' WHERE cid = '$cid'";
    $result = mysqli_query($connect, $charu);
    $charu2 = "UPDATE typecho_relationships SET mid = '$category' WHERE cid = '$cid'";
    $result2 = mysqli_query($connect, $charu2);
    $redisKeys = $connectRedis->keys('starapi_*');
        foreach ($redisKeys as $redisKey) {
            $connectRedis->del($redisKey);
        }
    if ($result&&$result2) {
            echo "<script>alert('修改成功');location.href = 'articleAdmin.php';</script>";
        } else {
            echo "<script>alert('修改失败');location.href = 'articleAdmin.php';</script>";   
        }
} else {
    echo "<script>alert('非法操作，行为已记录');location.href = 'warning.php?route=$file';</script>";
}
