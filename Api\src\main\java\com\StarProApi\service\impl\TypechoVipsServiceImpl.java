package com.StarProApi.service.impl;

import com.StarProApi.entity.*;
import com.StarProApi.common.PageList;
import com.StarProApi.dao.*;
import com.StarProApi.service.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 业务层实现类
 * TypechoVipsServiceImpl
 * <AUTHOR>
 * @date 2023/06/09
 */
@Service
public class TypechoVipsServiceImpl implements TypechoVipsService {

    @Autowired
	TypechoVipsDao dao;

    @Override
    public int insert(TypechoVips typechoVips) {
        return dao.insert(typechoVips);
    }

    @Override
    public int batchInsert(List<TypechoVips> list) {
    	return dao.batchInsert(list);
    }

    @Override
    public int update(TypechoVips typechoVips) {
    	return dao.update(typechoVips);
    }

    @Override
    public int delete(Object key) {
    	return dao.delete(key);
    }

    @Override
    public int batchDelete(List<Object> keys) {
        return dao.batchDelete(keys);
    }

	@Override
	public TypechoVips selectByKey(Object key) {
		return dao.selectByKey(key);
	}

	@Override
	public List<TypechoVips> selectList(TypechoVips typechoVips) {
		return dao.selectList(typechoVips);
	}

	@Override
	public PageList<TypechoVips> selectPage(TypechoVips typechoVips, Integer offset, Integer pageSize) {
		PageList<TypechoVips> pageList = new PageList<>();

		int total = this.total(typechoVips);

		Integer totalPage;
		if (total % pageSize != 0) {
			totalPage = (total /pageSize) + 1;
		} else {
			totalPage = total /pageSize;
		}

		int page = (offset - 1) * pageSize;

		List<TypechoVips> list = dao.selectPage(typechoVips, page, pageSize);

		pageList.setList(list);
		pageList.setStartPageNo(offset);
		pageList.setPageSize(pageSize);
		pageList.setTotalCount(total);
		pageList.setTotalPageCount(totalPage);
		return pageList;
	}

	@Override
	public int total(TypechoVips typechoVips) {
		return dao.total(typechoVips);
	}
}