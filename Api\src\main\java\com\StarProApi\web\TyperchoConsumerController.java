package com.StarProApi.web;

import com.StarProApi.annotation.LoginRequired;
import com.StarProApi.common.PageList;
import com.StarProApi.common.ResultAll;
import com.StarProApi.common.UserStatus;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.StarProApi.entity.identifyConsumer;
import com.StarProApi.service.impl.companyServiceImpl;
import com.StarProApi.service.impl.consumerServiceImpl;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Controller;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
/**
 * 实名认证
 * TypechoConsumer
 * <AUTHOR>
 * @date 2021/11/29
 */
@Component
@Controller
@RequestMapping(value = "/SProConsumer")
public class TyperchoConsumerController {
    protected static final Integer DEFAULT_PAGE = 1;
    protected static final Integer DEFAULT_PAGE_SIZE = 10;

    @Autowired
    private consumerServiceImpl consumerService;

    @Autowired
    private companyServiceImpl companyService;

    UserStatus UStatus = new UserStatus();

    @Value("${web.prefix}")
    private String dataprefix;

    @Value("${mybatis.configuration.variables.prefix}")
    private String prefix;

    @Autowired
    private RedisTemplate redisTemplate;

    ResultAll Result = new ResultAll();

    @RequestMapping(value = "/query")
    @ResponseBody
    @LoginRequired(purview = "0")
    public String queryConsumer(@RequestParam(value = "searchParams", required = false) String searchParams,
                                      @RequestParam(value = "page", required = false, defaultValue = "1") Integer page,
                                      @RequestParam(value = "pageSize", required = false, defaultValue = "10") Integer pageSize,
                                      @RequestParam(value = "token", required = false) String  token) {

        identifyConsumer dto = new identifyConsumer();
        if (StringUtils.isNotBlank(searchParams)) {
            JSONObject object = JSON.parseObject(searchParams);
            dto = object.toJavaObject(identifyConsumer.class);
        }

        PageList<identifyConsumer> list = consumerService.queryInfo(dto, page, pageSize);

        return list.toString();
    }

    @RequestMapping(value = "/remove")
    @ResponseBody
    @LoginRequired(purview = "0")
    public String removeConsumer(@RequestParam(value = "key", required = false) String  key,
                                 @RequestParam(value = "token", required = false) String  token){

        Integer rows = consumerService.remove(key);
        JSONObject response = new JSONObject();
        response.put("code" , rows);
        response.put("msg"  , rows > 0 ? "操作成功" : "操作失败");
        return response.toString();
    }

    @RequestMapping(value = "/insert")
    @ResponseBody
    @LoginRequired(purview = "0")
    public String insertConsumer(@RequestParam(value = "searchParams", required = false) String searchParams,
                                 @RequestParam(value = "token", required = false) String  token){

        identifyConsumer dto = new identifyConsumer();
        JSONObject response = new JSONObject();
        if (StringUtils.isNotBlank(searchParams)) {
            JSONObject object = JSON.parseObject(searchParams);
            dto = object.toJavaObject(identifyConsumer.class);
        }
        //插入数据前判断是否存在该记录
        PageList<identifyConsumer> list = consumerService.queryInfo(dto, DEFAULT_PAGE, DEFAULT_PAGE_SIZE);
        if (ObjectUtils.isEmpty(list.getList()) && list.getList().stream().count() == 0) {
            Integer rows = consumerService.insert(dto);
            response.put("code", rows);
            response.put("msg", rows > 0 ? "操作成功" : "操作失败");
        }else{
            response.put("msg", "操作失败，已存在该认证信息！");
        }
        return response.toString();
    }
    @RequestMapping(value = "/update")
    @ResponseBody
    @LoginRequired(purview = "0")
    public String updateConsumer(@RequestParam(value = "searchParams", required = false) String searchParams,
                                 @RequestParam(value = "token", required = false) String  token){

        identifyConsumer dto = new identifyConsumer();
        if (StringUtils.isNotBlank(searchParams)) {
            JSONObject object = JSON.parseObject(searchParams);
            dto = object.toJavaObject(identifyConsumer.class);
        }

        Integer rows = consumerService.update(dto);
        JSONObject response = new JSONObject();
        response.put("code" , rows);
        response.put("msg"  , rows > 0 ? "操作成功" : "操作失败");
        return response.toString();
    }
}
