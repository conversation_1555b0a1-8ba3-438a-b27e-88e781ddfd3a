package com.StarProApi.web;

import com.StarProApi.annotation.LoginRequired;
import com.StarProApi.common.PageList;
import com.StarProApi.common.ResultAll;
import com.StarProApi.common.UserStatus;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.StarProApi.entity.identifyCompany;
import com.StarProApi.service.impl.companyServiceImpl;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Controller;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

/**
 * 蓝V认证
 * TypechoCompany
 * <AUTHOR>
 * @date 2021/11/29
 */
@Component
@Controller
@RequestMapping(value = "/SProCompany")
public class TyperchoCompanyController {
    protected static final String DEFAULT_PAGE = "1";
    protected static final String DEFAULT_PAGE_SIZE = "10";
    @Autowired
    private companyServiceImpl companyService;

    UserStatus UStatus = new UserStatus();

    @Value("${web.prefix}")
    private String dataprefix;

    @Value("${mybatis.configuration.variables.prefix}")
    private String prefix;

    @Autowired
    private RedisTemplate redisTemplate;

    ResultAll Result = new ResultAll();
    @RequestMapping(value = "/test")
    @ResponseBody
    public String test() {
        PageList<identifyCompany> list = companyService.queryAll();
        return list.toString();
    }

    @RequestMapping(value = "/query")
    @ResponseBody
    @LoginRequired(purview = "0")
    public String queryCompany(@RequestParam(value = "searchParams", required = false) String searchParams,
                               @RequestParam(value = "page", required = false, defaultValue = DEFAULT_PAGE) Integer page,
                               @RequestParam(value = "pageSize", required = false, defaultValue = DEFAULT_PAGE_SIZE) Integer pageSize,
                               @RequestParam(value = "token", required = false) String  token) {
        identifyCompany dto = new identifyCompany();
        if (StringUtils.isNotBlank(searchParams)) {
            JSONObject object = JSON.parseObject(searchParams);
            dto = object.toJavaObject(identifyCompany.class);
        }
        PageList<identifyCompany> list = companyService.queryInfo(dto, page, pageSize);
        return list.toString();
    }

    @RequestMapping(value = "/remove")
    @ResponseBody
    @LoginRequired(purview = "0")
    public String removeCompany(@RequestParam(value = "key", required = false) String key,
                                @RequestParam(value = "token", required = false) String  token) {

        Integer rows = companyService.remove(key);
        JSONObject response = new JSONObject();
        response.put("code", rows);
        response.put("msg", rows > 0 ? "操作成功" : "操作失败");
        return response.toString();
    }

    @RequestMapping(value = "/insert")
    @ResponseBody
    @LoginRequired(purview = "0")
    public String insertCompany(@RequestParam(value = "searchParams", required = false) String searchParams,
                                @RequestParam(value = "token", required = false) String  token) {
        JSONObject response = new JSONObject();
        identifyCompany dto = new identifyCompany();
        if (StringUtils.isNotBlank(searchParams)) {
            JSONObject object = JSON.parseObject(searchParams);
            dto = object.toJavaObject(identifyCompany.class);
        }
        //插入企业前判断是否存在该记录
        PageList<identifyCompany> list = companyService.queryInfo(dto);
        if (ObjectUtils.isEmpty(list.getList()) && list.getList().stream().count() == 0) {
            Integer rows = companyService.insert(dto);
            response.put("code", rows);
            response.put("msg", rows > 0 ? "操作成功" : "操作失败");
        } else {
            response.put("msg", "操作失败，已存在该企业信息！");
        }
        return response.toString();
    }

    @RequestMapping(value = "/update")
    @ResponseBody
    @LoginRequired(purview = "0")
    public String updateCompany(@RequestParam(value = "searchParams", required = false) String searchParams,
                                @RequestParam(value = "token", required = false) String  token) {
        identifyCompany dto = new identifyCompany();
        JSONObject response = new JSONObject();
        if (StringUtils.isNotBlank(searchParams)) {
            JSONObject object = JSON.parseObject(searchParams);
            dto = object.toJavaObject(identifyCompany.class);
        }
        if (!StringUtils.isEmpty(dto.getUid().toString())) {
            if(StringUtils.isEmpty(dto.getRegno())){
                response.put("msg","请输入企业工商注册号！");
            }

            Integer rows = companyService.update(dto);
            response.put("code", rows);
            response.put("msg", rows > 0 ? "操作成功" : "操作失败");
        } else {
            response.put("msg","请检查数据完整性！");
        }
        return response.toString();
    }
}
