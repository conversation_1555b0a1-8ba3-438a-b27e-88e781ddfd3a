package com.StarProApi.service.impl;

import com.StarProApi.common.*;
import com.StarProApi.entity.TypechoApiconfig;
import com.StarProApi.service.UploadService;
import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import com.google.gson.Gson;
import com.qcloud.cos.COSClient;
import com.qcloud.cos.ClientConfig;
import com.qcloud.cos.auth.BasicCOSCredentials;
import com.qcloud.cos.auth.COSCredentials;
import com.qcloud.cos.model.PutObjectRequest;
import com.qcloud.cos.model.PutObjectResult;
import com.qcloud.cos.region.Region;
import com.qiniu.common.QiniuException;
import com.qiniu.http.Response;
import com.qiniu.storage.Configuration;
import com.qiniu.storage.UploadManager;
import com.qiniu.storage.model.DefaultPutRet;
import com.qiniu.util.Auth;
import org.apache.commons.net.ftp.FTP;
import org.apache.commons.net.ftp.FTPClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.StringUtils;
import org.springframework.boot.system.ApplicationHome;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
//javacv库
import org.bytedeco.javacv.FFmpegFrameGrabber;
import org.bytedeco.javacv.Frame;
import org.bytedeco.javacv.Java2DFrameConverter;
//jcodec库
//import org.jcodec.api.FrameGrab;
//import org.jcodec.api.JCodecException;
//import org.jcodec.common.io.NIOUtils;
//import org.jcodec.common.model.Picture;
//import org.jcodec.scale.AWTUtil;
import java.nio.file.Files;
import java.nio.file.StandardCopyOption;
import java.io.*;
import java.net.URLDecoder;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class UploadServiceImpl implements UploadService {

    @Value("${upload.allowed-extensions:}")
    private String allowedExtensionsStr;

    ResultAll Result = new ResultAll();
    baseFull baseFull = new baseFull();
    EditFile editFile = new EditFile();

    private List<String> getAllowedExtensions() {
        System.out.println("开始获取允许的文件扩展名列表");
        System.out.println("配置文件中的扩展名字符串: " + allowedExtensionsStr);

        if (StringUtils.isEmpty(allowedExtensionsStr)) {
            System.out.println("配置为空，使用默认扩展名列表");
            List<String> defaultExtensions = Arrays.asList(
                    ".jpg", ".jpeg", ".png", ".gif", ".bmp", ".webp",
                    ".mp4", ".avi", ".mov",
                    ".zip", ".rar", ".7z",
                    ".doc", ".docx", ".xls", ".xlsx", ".ppt", ".pptx", ".pdf", ".txt",
                    ".mp3", ".wav", ".ogg", ".flac",
                    ".apk", ".ipa"
            );
            System.out.println("默认扩展名列表: " + defaultExtensions);
            return defaultExtensions;
        }

        // 将配置字符串转换为列表，并确保所有扩展名都是小写
        List<String> extensions = Arrays.stream(allowedExtensionsStr.split(","))
                .map(String::trim)
                .map(String::toLowerCase)
                .collect(Collectors.toList());

        System.out.println("处理后的允许扩展名列表: " + extensions);
        return extensions;
    }

    //    private BufferedImage extractFirstFrame(File videoFile) throws IOException, JCodecException {
//        FrameGrab grab = FrameGrab.createFrameGrab(NIOUtils.readableChannel(videoFile));
//        Picture picture = grab.getNativeFrame();
//        return AWTUtil.toBufferedImage(picture);
//    }
    public BufferedImage extractFirstFrame(File videoFile) throws Exception {
        FFmpegFrameGrabber grabber = new FFmpegFrameGrabber(videoFile);
        try {
            grabber.start();
            int totalFrames = grabber.getLengthInFrames();
            System.out.println("视频总帧数: " + totalFrames);
            if (totalFrames < 5) {
                System.out.println("视频帧数不足5帧");
                grabber.setFrameNumber(totalFrames);
            } else {
                grabber.setFrameNumber(5);
            }
            Frame frame = grabber.grabImage();
            if (frame == null) {
                System.out.println("第5帧为空，尝试提取其他帧");
                for (int i = 1; i <= totalFrames; i++) {
                    grabber.setFrameNumber(i);
                    frame = grabber.grabImage();
                    if (frame != null) {
                        System.out.println("成功提取到第 " + i + " 帧");
                        break;
                    }
                }
            }
            Java2DFrameConverter converter = new Java2DFrameConverter();
            BufferedImage bufferedImage = converter.convert(frame);
            grabber.stop();
            grabber.release();
            System.out.println("bufferedImage：" + bufferedImage);
            return bufferedImage;
        } catch (Exception e) {
            e.printStackTrace();
            throw new Exception("提取视频帧失败", e);
        } finally {
            grabber.close();
        }
    }
    public String base64Upload(String base64Img, String dataprefix, TypechoApiconfig apiconfig, Integer uid) {
        try {
            // 解析base64数据
            String[] parts = base64Img.split(",");
            String mimeType = parts[0].split(";")[0].split(":")[1];
            String base64Image = parts[1];

            // 确定文件类型
            String eName;
            switch (mimeType.toLowerCase()) {
                case "image/jpeg":
                    eName = ".jpg";
                    break;
                case "image/png":
                    eName = ".png";
                    break;
                case "image/gif":
                    eName = ".gif";
                    break;
                case "image/bmp":
                    eName = ".bmp";
                    break;
                case "image/webp":
                    eName = ".webp";
                    break;
                default:
                    eName = ".jpg";
            }

            // 解码base64数据
            byte[] imageBytes = Base64.getDecoder().decode(base64Image);
            InputStream inputStream = new ByteArrayInputStream(imageBytes);

            // 生成文件名和日期路径
            String newFileName = UUID.randomUUID() + eName;
            Calendar cal = Calendar.getInstance();
            int year = cal.get(Calendar.YEAR);
            int month = cal.get(Calendar.MONTH) + 1;
            int day = cal.get(Calendar.DATE);

            // 根据不同的存储方式上传
            if(apiconfig.getUploadType().equals("cos")){
                COSCredentials cred = new BasicCOSCredentials(apiconfig.getCosAccessKey(), apiconfig.getCosSecretKey());
                ClientConfig clientConfig = new ClientConfig(new Region(apiconfig.getCosBucket()));
                COSClient cosclient = new COSClient(cred, clientConfig);
                String bucketName = apiconfig.getCosBucketName();
                String key = "/" + apiconfig.getCosPrefix() + "/" + year + "/" + month + "/" + day + "/" + newFileName;
                try {
                    PutObjectRequest putObjectRequest = new PutObjectRequest(bucketName, key, inputStream, null);
                    PutObjectResult putObjectResult = cosclient.putObject(putObjectRequest);
                    Map<String, String> info = new HashMap<>();
                    info.put("url", apiconfig.getCosPath() + putObjectRequest.getKey());
                    editFile.setLog("用户" + uid + "通过cosUpload成功上传了base64图片");
                    return uid == 0 ?
                            Result.getResultWeUp(0, "上传成功", info) :
                            Result.getResultJson(1, "上传成功", info);
                } finally {
                    cosclient.shutdown();
                    if(inputStream != null) {
                        inputStream.close();
                    }
                }
            }

            if(apiconfig.getUploadType().equals("local")){
                String decodeClassespath;
                if(apiconfig.getLocalPath()!=null && !apiconfig.getLocalPath().isEmpty()){
                    decodeClassespath = apiconfig.getLocalPath();
                }else{
                    ApplicationHome h = new ApplicationHome(getClass());
                    File jarF = h.getSource();
                    String classespath = jarF.getParentFile().toString()+"/files/static";
                    try {
                        decodeClassespath = URLDecoder.decode(classespath,"utf-8");
                    } catch (UnsupportedEncodingException e) {
                        decodeClassespath = "/opt/files/static";
                    }
                }

                File directory = new File(decodeClassespath+"/upload/"+year+"/"+month+"/"+day);
                if(!directory.exists()){
                    directory.mkdirs();
                }

                File file = new File(directory, newFileName);
                try {
                    Files.copy(inputStream, file.toPath(), StandardCopyOption.REPLACE_EXISTING);
                    Map<String,String> info = new HashMap<>();
                    info.put("url", apiconfig.getWebinfoUploadUrl()+"upload/"+year+"/"+month+"/"+day+"/"+newFileName);
                    editFile.setLog("用户"+uid+"通过localUpload成功上传了base64图片");
                    return uid == 0 ?
                            Result.getResultWeUp(0, "上传成功", info) :
                            Result.getResultJson(1, "上传成功", info);
                } catch (IOException e) {
                    e.printStackTrace();
                    editFile.setLog("用户"+uid+"通过localUpload上传base64图片失败");
                    return uid == 0 ?
                            Result.getResultWeUp(1, "上传失败", null) :
                            Result.getResultJson(0, "上传失败", null);
                }
            }

            if(apiconfig.getUploadType().equals("oss")){
                OSS ossClient = new OSSClientBuilder().build(
                        apiconfig.getAliyunEndpoint(),
                        apiconfig.getAliyunAccessKeyId(),
                        apiconfig.getAliyunAccessKeySecret()
                );
                try {
                    String key = apiconfig.getAliyunFilePrefix()+"/"+year+"/"+month+"/"+day+"/"+newFileName;
                    ossClient.putObject(apiconfig.getAliyunAucketName(), key, inputStream);
                    String url = apiconfig.getAliyunUrlPrefix()+key;
                    Map<String,String> info = new HashMap<>();
                    info.put("url", url);
                    editFile.setLog("用户"+uid+"通过ossUpload成功上传了base64图片");
                    return uid == 0 ?
                            Result.getResultWeUp(0, "上传成功", info) :
                            Result.getResultJson(1, "上传成功", info);
                } finally {
                    ossClient.shutdown();
                }
            }

            if(apiconfig.getUploadType().equals("qiniu")){
                String key = "/app/"+year+"/"+month+"/"+day+"/"+newFileName;
                Configuration cfg = new Configuration();
                UploadManager uploadManager = new UploadManager(cfg);
                try {
                    Auth auth = Auth.create(apiconfig.getQiniuAccessKey(), apiconfig.getQiniuSecretKey());
                    String upToken = auth.uploadToken(apiconfig.getQiniuBucketName());
                    Response response = uploadManager.put(imageBytes, key, upToken);
                    DefaultPutRet putRet = new Gson().fromJson(response.bodyString(), DefaultPutRet.class);
                    String returnPath = apiconfig.getQiniuDomain() + putRet.key;
                    Map<String,String> info = new HashMap<>();
                    info.put("url", returnPath);
                    editFile.setLog("用户"+uid+"通过qiniuUpload成功上传了base64图片");
                    return uid == 0 ?
                            Result.getResultWeUp(0, "上传成功", info) :
                            Result.getResultJson(1, "上传成功", info);
                } catch (QiniuException ex) {
                    ex.printStackTrace();
                    editFile.setLog("用户"+uid+"通过qiniuUpload上传base64图片失败");
                    return uid == 0 ?
                            Result.getResultWeUp(1, "上传失败", null) :
                            Result.getResultJson(0, "上传失败", null);
                }
            }

            editFile.setLog("用户" + uid + "上传base64图片失败：不支持的上传方式");
            return uid == 0 ?
                    Result.getResultWeUp(1, "不支持的上传方式", null) :
                    Result.getResultJson(0, "不支持的上传方式", null);

        } catch (Exception e) {
            e.printStackTrace();
            editFile.setLog("用户" + uid + "上传base64图片失败");
            return uid == 0 ?
                    Result.getResultWeUp(1, "上传失败", null) :
                    Result.getResultJson(0, "上传失败", null);
        }
    }
    public String cosUpload(MultipartFile file, String dataprefix, TypechoApiconfig apiconfig, Integer uid) {
        String oldFileName = file.getOriginalFilename();
        String eName = "";
        try {
            eName = oldFileName.substring(oldFileName.lastIndexOf("."));
        } catch (Exception e) {
            oldFileName = oldFileName + ".png";
            eName = oldFileName.substring(oldFileName.lastIndexOf("."));
        }
        List<String> allowedExtensions = getAllowedExtensions();
        String lowercaseFileType = eName.toLowerCase();
        System.out.println("当前文件扩展名: " + lowercaseFileType);
        System.out.println("是否允许上传: " + allowedExtensions.contains(lowercaseFileType));
        if (!allowedExtensions.contains(lowercaseFileType)) {
            if (uid == 0) {
                return Result.getResultWeUp(1, "不支持上传该文件类型", null);
            } else {
                return Result.getResultJson(0, "不支持上传该文件类型", null);
            }
        }
        Integer uploadLevel = apiconfig.getUploadLevel();
        if (uploadLevel.equals(1)) {
            return Result.getResultJson(0, "管理员已关闭上传功能", null);
        }
        if (uploadLevel.equals(0)) {
            BufferedImage bi = null;
            try {
                bi = ImageIO.read(file.getInputStream());
            } catch (IOException e) {
                e.printStackTrace();
            }
            if (bi == null && !eName.equalsIgnoreCase(".WEBP")) {
                if (uid == 0) {
                    return Result.getResultWeUp(1, "当前只允许上传图片文件", null);
                } else {
                    return Result.getResultJson(0, "当前只允许上传图片文件", null);
                }
            }
        }
        if (uploadLevel.equals(2)) {
            BufferedImage bi = null;
            try {
                bi = ImageIO.read(file.getInputStream());
            } catch (IOException e) {
                e.printStackTrace();
            }
            Integer isVideo = baseFull.isVideo(eName);
            if (bi == null && !eName.equalsIgnoreCase(".WEBP") && !isVideo.equals(1)) {
                if (uid == 0) {
                    return Result.getResultWeUp(1, "请上传图片或者视频文件", null);
                } else {
                    return Result.getResultJson(0, "请上传图片或者视频文件", null);
                }
            }
        }

        String newFileName = UUID.randomUUID() + eName;
        Calendar cal = Calendar.getInstance();
        int year = cal.get(Calendar.YEAR);
        int month = cal.get(Calendar.MONTH) + 1;
        int day = cal.get(Calendar.DATE);

        COSCredentials cred = new BasicCOSCredentials(apiconfig.getCosAccessKey(), apiconfig.getCosSecretKey());
        ClientConfig clientConfig = new ClientConfig(new Region(apiconfig.getCosBucket()));
        COSClient cosclient = new COSClient(cred, clientConfig);
        String bucketName = apiconfig.getCosBucketName();

        File localFile = null;
        try {
            localFile = File.createTempFile("temp", null);
            file.transferTo(localFile);
            String key = "/" + apiconfig.getCosPrefix() + "/" + year + "/" + month + "/" + day + "/" + newFileName;
            PutObjectRequest putObjectRequest = new PutObjectRequest(bucketName, key, localFile);
            PutObjectResult putObjectResult = cosclient.putObject(putObjectRequest);

            Map<String, String> info = new HashMap<>();
            info.put("url", apiconfig.getCosPath() + putObjectRequest.getKey());

            String coverUrl = null;
            Integer isVideo = baseFull.isVideo(eName);
            if (isVideo.equals(1)) {
                try {
                    BufferedImage frame = extractFirstFrame(localFile);
                    if (frame != null) {
                        File coverFile = File.createTempFile("cover", ".jpg");
                        ImageIO.write(frame, "jpg", coverFile);
                        String coverKey = "/" + apiconfig.getCosPrefix() + "/" + year + "/" + month + "/" + day + "/" + UUID.randomUUID() + ".jpg";
                        PutObjectRequest coverPutRequest = new PutObjectRequest(bucketName, coverKey, coverFile);
                        cosclient.putObject(coverPutRequest);
                        coverUrl = apiconfig.getCosPath() + coverKey;
                        coverFile.delete();
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }

            if (coverUrl != null) {
                info.put("poster", coverUrl);
            }
            editFile.setLog("用户" + uid + "通过 cosUpload 成功上传了文件");
            if (uid == 0) {
                return Result.getResultWeUp(0, "上传成功", info);
            } else {
                return Result.getResultJson(1, "上传成功", info);
            }

        } catch (IOException e) {
            editFile.setLog("用户" + uid + "通过 cosUpload 上传文件失败");
            if (uid == 0) {
                return Result.getResultWeUp(1, "上传失败", null);
            } else {
                return Result.getResultJson(0, "上传失败", null);
            }
        } finally {
            cosclient.shutdown();
        }
    }


    public String localUpload(MultipartFile file, String dataprefix, TypechoApiconfig apiconfig, Integer uid) {
        String filename = file.getOriginalFilename();
        String filetype = "";
        try {
            filetype = filename.substring(filename.lastIndexOf("."));
        } catch (Exception e) {
            filename = filename + ".png";
            filetype = filename.substring(filename.lastIndexOf("."));
        }

        // 允许的文件类型检查
        List<String> allowedExtensions = getAllowedExtensions();
        String lowercaseFileType = filetype.toLowerCase();
        System.out.println("当前文件扩展名: " + lowercaseFileType);
        System.out.println("是否允许上传: " + allowedExtensions.contains(lowercaseFileType));
        if (!allowedExtensions.contains(lowercaseFileType)) {
            if (uid == 0) {
                return Result.getResultWeUp(1, "不支持上传该文件类型", null);
            } else {
                return Result.getResultJson(0, "不支持上传该文件类型", null);
            }
        }

        String newfile = UUID.randomUUID() + filetype;

        // 上传级别检查
        Integer uploadLevel = apiconfig.getUploadLevel();
        if (uploadLevel.equals(1)) {
            return Result.getResultJson(0, "管理员已关闭上传功能", null);
        }

        // 图片检查
        if (uploadLevel.equals(0)) {
            BufferedImage bi = null;
            try {
                bi = ImageIO.read(file.getInputStream());
            } catch (IOException e) {
                e.printStackTrace();
            }
            if (bi == null && !filetype.equalsIgnoreCase(".WEBP")) {
                if (uid == 0) {
                    return Result.getResultWeUp(1, "当前只允许上传图片文件", null);
                } else {
                    return Result.getResultJson(0, "当前只允许上传图片文件", null);
                }
            }
        }

        // 图片和视频检查
        if (uploadLevel.equals(2)) {
            BufferedImage bi = null;
            try {
                bi = ImageIO.read(file.getInputStream());
            } catch (IOException e) {
                e.printStackTrace();
            }
            Integer isVideo = baseFull.isVideo(filetype);
            if (bi == null && !filetype.equalsIgnoreCase(".WEBP") && !isVideo.equals(1)) {
                if (uid == 0) {
                    return Result.getResultWeUp(1, "请上传图片或者视频文件", null);
                } else {
                    return Result.getResultJson(0, "请上传图片或者视频文件", null);
                }
            }
        }

        // 获取上传路径
        String decodeClassespath;
        if (apiconfig.getLocalPath() != null && !apiconfig.getLocalPath().isEmpty()) {
            decodeClassespath = apiconfig.getLocalPath();
        } else {
            ApplicationHome h = new ApplicationHome(getClass());
            File jarF = h.getSource();
            String classespath = jarF.getParentFile().toString() + "/files/static";
            try {
                decodeClassespath = URLDecoder.decode(classespath, "utf-8");
            } catch (UnsupportedEncodingException e) {
                e.printStackTrace();
                decodeClassespath = "/opt/files/static";
            }
        }

        // 获取当前日期
        Calendar cal = Calendar.getInstance();
        int year = cal.get(Calendar.YEAR);
        int month = cal.get(Calendar.MONTH) + 1;
        int day = cal.get(Calendar.DATE);

        try {
            // 构建上传目录路径
            String uploadDir = String.format("%s%supload%s%d%s%d%s%d",
                    decodeClassespath,
                    File.separator,
                    File.separator,
                    year,
                    File.separator,
                    month,
                    File.separator,
                    day);

            // 创建目录
            File directory = new File(uploadDir);
            if (!directory.exists()) {
                boolean created = directory.mkdirs();
                if (!created) {
                    throw new IOException("无法创建目录: " + uploadDir);
                }
            }

            // 检查目录权限
            if (!directory.canWrite()) {
                throw new IOException("目录没有写入权限: " + uploadDir);
            }

            // 创建目标文件
            File file1 = new File(directory, newfile);

            System.out.println("开始文件上传");
            file.transferTo(file1);
            System.out.println("文件上传成功");

            // 构建返回信息
            Map<String, String> info = new HashMap<>();
            String uploadUrl = String.format("%supload/%d/%d/%d/%s",
                    apiconfig.getWebinfoUploadUrl(),
                    year, month, day, newfile);
            info.put("url", uploadUrl);

            // 处理视频文件
            String coverUrl = null;
            Integer isVideo = baseFull.isVideo(filetype);
            if (isVideo.equals(1)) {
                System.out.println("确定为视频文件");
                try {
                    System.out.println("获取视频封面");
                    BufferedImage frame = extractFirstFrame(file1);
                    if (frame != null) {
                        String coverFileName = UUID.randomUUID() + ".jpg";
                        File coverFile = new File(directory, coverFileName);
                        ImageIO.write(frame, "jpg", coverFile);
                        coverUrl = String.format("%supload/%d/%d/%d/%s",
                                apiconfig.getWebinfoUploadUrl(),
                                year, month, day, coverFileName);
                        System.out.println("视频封面获取成功，路径：" + coverUrl);
                    }
                } catch (Exception e) {
                    System.out.println("获取视频封面失败");
                    e.printStackTrace();
                }
            }

            if (coverUrl != null) {
                info.put("poster", coverUrl);
            }

            editFile.setLog("用户" + uid + "通过 localUpload 成功上传了文件");
            if (uid == 0) {
                return Result.getResultWeUp(0, "上传成功", info);
            } else {
                return Result.getResultJson(1, "上传成功", info);
            }

        } catch (IOException e) {
            System.out.println("文件上传失败: " + e.getMessage());
            e.printStackTrace();
            editFile.setLog("用户" + uid + "通过 localUpload 上传文件失败");
            if (uid == 0) {
                return Result.getResultWeUp(1, "上传失败", null);
            } else {
                return Result.getResultJson(0, "上传失败", null);
            }
        }
    }



    public String ossUpload(MultipartFile file, String dataprefix, TypechoApiconfig apiconfig, Integer uid) {

        OSS ossClient = new OSSClientBuilder().build(apiconfig.getAliyunEndpoint(), apiconfig.getAliyunAccessKeyId(), apiconfig.getAliyunAccessKeySecret());
        InputStream inputStream = null;
        try {
            inputStream = file.getInputStream();
        } catch (IOException e) {
            e.printStackTrace();
        }
        Calendar cal = Calendar.getInstance();
        int year = cal.get(Calendar.YEAR);
        int month = cal.get(Calendar.MONTH) + 1;
        int day = cal.get(Calendar.DATE);
        String filename = file.getOriginalFilename();
        String eName = "";
        try {
            eName = filename.substring(filename.lastIndexOf("."));
        } catch (Exception e) {
            filename = filename + ".png";
            eName = filename.substring(filename.lastIndexOf("."));
        }
        List<String> allowedExtensions = getAllowedExtensions();
        String lowercaseFileType = eName.toLowerCase();
        System.out.println("当前文件扩展名: " + lowercaseFileType);
        System.out.println("是否允许上传: " + allowedExtensions.contains(lowercaseFileType));
        if (!allowedExtensions.contains(lowercaseFileType)) {
            if (uid == 0) {
                return Result.getResultWeUp(1, "不支持上传该文件类型", null);
            } else {
                return Result.getResultJson(0, "不支持上传该文件类型", null);
            }
        }
        Integer uploadLevel = apiconfig.getUploadLevel();
        if (uploadLevel.equals(1)) {
            if (uid == 0) {
                return Result.getResultWeUp(1, "管理员已关闭上传功能", null);
            } else {
                return Result.getResultJson(0, "管理员已关闭上传功能", null);
            }
        }
        BufferedImage bi = null;
        try {
            bi = ImageIO.read(file.getInputStream());
        } catch (IOException e) {
            e.printStackTrace();
        }
        Integer isVideo = baseFull.isVideo(eName);
        if (uploadLevel.equals(0)) {
            if (bi == null && !eName.equalsIgnoreCase(".WEBP")) {
                if (uid == 0) {
                    return Result.getResultWeUp(1, "当前只允许上传图片文件", null);
                } else {
                    return Result.getResultJson(0, "当前只允许上传图片文件", null);
                }
            }
        }
        if (uploadLevel.equals(2)) {
            if (bi == null && !eName.equalsIgnoreCase(".WEBP") && !isVideo.equals(1)) {
                if (uid == 0) {
                    return Result.getResultWeUp(1, "请上传图片或者视频文件", null);
                } else {
                    return Result.getResultJson(0, "请上传图片或者视频文件", null);
                }
            }
        }
        String newFileName = UUID.randomUUID() + eName;
        filename = newFileName;
        String key = apiconfig.getAliyunFilePrefix() + "/" + year + "/" + month + "/" + day + "/" + filename;
        ossClient.putObject(apiconfig.getAliyunAucketName(), key, inputStream);
        ossClient.shutdown();
        String url = apiconfig.getAliyunUrlPrefix() + key;

        // 如果是视频文件，提取并上传封面
        String coverUrl = null;
        if (isVideo.equals(1)) {
            try {
                File videoFile = File.createTempFile("video", eName);
                file.transferTo(videoFile);
                BufferedImage frame = extractFirstFrame(videoFile);
                if (frame != null) {
                    File coverFile = File.createTempFile("cover", ".jpg");
                    ImageIO.write(frame, "jpg", coverFile);
                    String coverKey = apiconfig.getAliyunFilePrefix() + "/" + year + "/" + month + "/" + day + "/" + UUID.randomUUID() + ".jpg";
                    ossClient.putObject(apiconfig.getAliyunAucketName(), coverKey, new FileInputStream(coverFile));
                    coverUrl = apiconfig.getAliyunUrlPrefix() + coverKey;
                    coverFile.delete();
                }
                videoFile.delete();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        Map<String, String> info = new HashMap<>();
        info.put("url", url);
        if (coverUrl != null) {
            info.put("poster", coverUrl);
        }
        editFile.setLog("用户" + uid + "通过ossUpload成功上传了文件");
        if (uid == 0) {
            return Result.getResultWeUp(0, "上传成功", info);
        } else {
            return Result.getResultJson(1, "上传成功", info);
        }
    }

    public String qiniuUpload(MultipartFile file, String dataprefix, TypechoApiconfig apiconfig, Integer uid) {
        InputStream inputStream = null;
        try {
            inputStream = file.getInputStream();
        } catch (IOException e) {
            e.printStackTrace();
        }
        Calendar cal = Calendar.getInstance();
        int year = cal.get(Calendar.YEAR);
        int month = cal.get(Calendar.MONTH) + 1;
        int day = cal.get(Calendar.DATE);
        String filename = file.getOriginalFilename();
        String eName = "";
        try {
            eName = filename.substring(filename.lastIndexOf("."));
        } catch (Exception e) {
            filename = filename + ".png";
            eName = filename.substring(filename.lastIndexOf("."));
        }
        List<String> allowedExtensions = getAllowedExtensions();
        String lowercaseFileType = eName.toLowerCase();
        System.out.println("当前文件扩展名: " + lowercaseFileType);
        System.out.println("是否允许上传: " + allowedExtensions.contains(lowercaseFileType));
        if (!allowedExtensions.contains(lowercaseFileType)) {
            if (uid == 0) {
                return Result.getResultWeUp(1, "不支持上传该文件类型", null);
            } else {
                return Result.getResultJson(0, "不支持上传该文件类型", null);
            }
        }
        Integer uploadLevel = apiconfig.getUploadLevel();
        if (uploadLevel.equals(1)) {
            if (uid == 0) {
                return Result.getResultWeUp(1, "管理员已关闭上传功能", null);
            } else {
                return Result.getResultJson(0, "管理员已关闭上传功能", null);
            }
        }
        BufferedImage bi = null;
        try {
            bi = ImageIO.read(file.getInputStream());
        } catch (IOException e) {
            e.printStackTrace();
        }
        Integer isVideo = baseFull.isVideo(eName);
        if (uploadLevel.equals(0)) {
            if (bi == null && !eName.equalsIgnoreCase(".WEBP")) {
                if (uid == 0) {
                    return Result.getResultWeUp(1, "当前只允许上传图片文件", null);
                } else {
                    return Result.getResultJson(0, "当前只允许上传图片文件", null);
                }
            }
        }
        if (uploadLevel.equals(2)) {
            if (bi == null && !eName.equalsIgnoreCase(".WEBP") && !isVideo.equals(1)) {
                if (uid == 0) {
                    return Result.getResultWeUp(1, "请上传图片或者视频文件", null);
                } else {
                    return Result.getResultJson(0, "请上传图片或者视频文件", null);
                }
            }
        }
        String newFileName = UUID.randomUUID() + eName;
        filename = newFileName;
        String key = "/app/" + year + "/" + month + "/" + day + "/" + filename;
        Configuration cfg = new Configuration();
        UploadManager uploadManager = new UploadManager(cfg);
        try {
            Auth auth = Auth.create(apiconfig.getQiniuAccessKey(), apiconfig.getQiniuSecretKey());
            String upToken = auth.uploadToken(apiconfig.getQiniuBucketName());
            FileInputStream fileInputStream = (FileInputStream) file.getInputStream();
            try {
                Response response = uploadManager.put(fileInputStream, key, upToken, null, null);
                DefaultPutRet putRet = new Gson().fromJson(response.bodyString(), DefaultPutRet.class);
                String returnPath = apiconfig.getQiniuDomain() + putRet.key;

                // 如果是视频文件，提取并上传封面
                String coverUrl = null;
                if (isVideo.equals(1)) {
                    try {
                        File videoFile = File.createTempFile("video", eName);
                        file.transferTo(videoFile);
                        BufferedImage frame = extractFirstFrame(videoFile);
                        if (frame != null) {
                            File coverFile = File.createTempFile("cover", ".jpg");
                            ImageIO.write(frame, "jpg", coverFile);
                            String coverKey = "/app/" + year + "/" + month + "/" + day + "/" + UUID.randomUUID() + ".jpg";
                            uploadManager.put(new FileInputStream(coverFile), coverKey, upToken, null, null);
                            coverUrl = apiconfig.getQiniuDomain() + coverKey;
                            coverFile.delete();
                        }
                        videoFile.delete();
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }

                Map<String, String> info = new HashMap<>();
                info.put("url", returnPath);
                if (coverUrl != null) {
                    info.put("poster", coverUrl);
                }
                editFile.setLog("用户" + uid + "通过qiniuUpload成功上传了文件");
                if (uid == 0) {
                    return Result.getResultWeUp(0, "上传成功", info);
                } else {
                    return Result.getResultJson(1, "上传成功", info);
                }
            } catch (QiniuException ex) {
                Response r = ex.response;
                System.err.println(r.toString());
                try {
                    System.err.println(r.bodyString());
                } catch (QiniuException ex2) {
                    //ignore
                }
                if (uid == 0) {
                    return Result.getResultWeUp(0, "上传失败", null);
                } else {
                    return Result.getResultJson(1, "上传失败", null);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            if (uid == 0) {
                return Result.getResultWeUp(0, "上传失败", null);
            } else {
                return Result.getResultJson(1, "上传失败", null);
            }
        }
    }

    public String ftpUpload(MultipartFile file, String dataprefix, TypechoApiconfig apiconfig, Integer uid) {
        FTPClient ftpClient = new FTPClient();
        try {
            ApplicationHome h = new ApplicationHome(getClass());
            File jarF = h.getSource();
            String classespath = jarF.getParentFile().toString() + "/files";
            String decodeClassespath = URLDecoder.decode(classespath, "utf-8");
            String fileDir = decodeClassespath + "/temp";
            File dir = new File(fileDir);

            if (!dir.exists()) {
                dir.mkdirs();
            }

            String originalFileName = file.getOriginalFilename();
            String suffix = "";
            try {
                suffix = originalFileName.substring(originalFileName.lastIndexOf("."));
            } catch (Exception e) {
                originalFileName = originalFileName + ".png";
                suffix = originalFileName.substring(originalFileName.lastIndexOf("."));
            }
            List<String> allowedExtensions = getAllowedExtensions();
            String lowercaseFileType = suffix.toLowerCase();
            System.out.println("当前文件扩展名: " + lowercaseFileType);
            System.out.println("是否允许上传: " + allowedExtensions.contains(lowercaseFileType));
            if (!allowedExtensions.contains(lowercaseFileType)) {
                if (uid == 0) {
                    return Result.getResultWeUp(1, "不支持上传该文件类型", null);
                } else {
                    return Result.getResultJson(0, "不支持上传该文件类型", null);
                }
            }
            Integer uploadLevel = apiconfig.getUploadLevel();
            if (uploadLevel.equals(0)) {
                BufferedImage bi = ImageIO.read(file.getInputStream());
                if (bi == null && !suffix.equalsIgnoreCase(".WEBP")) {
                    if (uid == 0) {
                        return Result.getResultWeUp(1, "当前只允许上传图片文件", null);
                    } else {
                        return Result.getResultJson(0, "当前只允许上传图片文件", null);
                    }
                }
            }
            BufferedImage bi = null;
            try {
                bi = ImageIO.read(file.getInputStream());
            } catch (IOException e) {
                e.printStackTrace();
            }
            Integer isVideo = baseFull.isVideo(suffix);
            if (uploadLevel.equals(2)) {
                if (bi == null && !suffix.equalsIgnoreCase(".WEBP") && !isVideo.equals(1)) {
                    if (uid == 0) {
                        return Result.getResultWeUp(1, "请上传图片或者视频文件", null);
                    } else {
                        return Result.getResultJson(0, "请上传图片或者视频文件", null);
                    }
                }
            }

            String newFileName = UUID.randomUUID() + suffix;
            File file1 = new File(dir, newFileName);
            try {
                file.transferTo(file1);
                System.out.println("上传文件成功！");
            } catch (IOException e) {
                System.out.println("上传文件失败！");
                e.printStackTrace();
            }

            String key = apiconfig.getFtpBasePath() + "/" + file1.getName();

            ftpClient.setConnectTimeout(2000 * 60);
            ftpClient.setControlEncoding("utf-8");
            ftpClient.connect(apiconfig.getFtpHost(), apiconfig.getFtpPort());
            ftpClient.login(apiconfig.getFtpUsername(), apiconfig.getFtpPassword());
            ftpClient.enterLocalPassiveMode();

            String remotePath = apiconfig.getFtpBasePath();
            if (!ftpClient.changeWorkingDirectory(remotePath)) {
                ftpClient.makeDirectory(remotePath);
                ftpClient.changeWorkingDirectory(remotePath);
            }
            ftpClient.setFileType(FTP.BINARY_FILE_TYPE);
            ftpClient.storeFile(key, new FileInputStream(file1));
            ftpClient.disconnect();
            Map<String, String> info = new HashMap<>();
            info.put("url", apiconfig.getWebinfoUploadUrl() + key);

            // 如果是视频文件，提取并上传封面
            String coverUrl = null;
            if (isVideo.equals(1)) {
                try {
                    File videoFile = File.createTempFile("video", suffix);
                    file.transferTo(videoFile);
                    BufferedImage frame = extractFirstFrame(videoFile);
                    if (frame != null) {
                        File coverFile = File.createTempFile("cover", ".jpg");
                        ImageIO.write(frame, "jpg", coverFile);
                        String coverKey = apiconfig.getFtpBasePath() + "/" + UUID.randomUUID() + ".jpg";
                        ftpClient.storeFile(coverKey, new FileInputStream(coverFile));
                        coverUrl = apiconfig.getWebinfoUploadUrl() + coverKey;
                        coverFile.delete();
                    }
                    videoFile.delete();
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }

            if (coverUrl != null) {
                info.put("poster", coverUrl);
            }
            editFile.setLog("用户" + uid + "通过ftpUpload成功上传了文件");
            if (uid == 0) {
                return Result.getResultWeUp(0, "上传成功", info);
            } else {
                return Result.getResultJson(1, "上传成功", info);
            }
        } catch (Exception e) {
            e.printStackTrace();
            editFile.setLog("用户" + uid + "通过ftpUpload上传文件失败");
            if (uid == 0) {
                return Result.getResultWeUp(1, "上传失败", null);
            } else {
                return Result.getResultJson(0, "上传失败", null);
            }
        }
    }

}
