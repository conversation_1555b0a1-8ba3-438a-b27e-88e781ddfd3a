package com.StarProApi.service;

import com.StarProApi.common.PageList;
import com.StarProApi.entity.identifyCompany;

public interface companyService {
    PageList<identifyCompany> queryInfo(identifyCompany dto, int page, int pageSize);

    PageList<identifyCompany> queryInfo(identifyCompany dto);
    int insert(identifyCompany dto);


    Integer remove(String key);

    Integer update(identifyCompany dto);

    identifyCompany selectByKey(Object uid);

    PageList<identifyCompany> queryAll();
}
