<?php
session_start();
?>



<?php
include_once 'Nav.php';

?>
<div class="row">

    <div class="col-lg-12">
        <div class="card">
            <div class="card-body">
                <h4 class="header-title mb-3 size_18">创建群聊</h4>

                <form class="needs-validation" m action="chatGroupAddPost.php" method="post" onsubmit="return check()"
                      novalidate>
                    <div class="form-group col-sm-4">
                        <label for="validationCustom01">群主UID</label>
                        <input type="number" class="form-control" id="validationCustom01" placeholder="请输入群主UID"
                               name="uid" required>
                    </div>
                    <div class="form-group mb-3">
                        <label for="validationCustom01">群聊名称</label>
                        <input type="text" class="form-control" id="validationCustom01" placeholder="请输入群聊名称"
                               name="name" required>
                    </div>
                    <div class="form-group mb-3" id="validationCustom011">
                        <label>群聊头像<button type="button" id="uploadButton" class="btn btn-success2 btn-sm btn-rounded right_10"><i class="dripicons-upload"></i> 上传图片</button></label>
                        <input type="text" class="form-control" id="picLinkInput" placeholder="图片链接" name="pic" readonly>
                        <input type="file" id="uploadImage" accept="image/*" style="display: none;">
                    </div>

                    <div class="form-group mb-3 text_right">
                        <button class="btn btn-primary" type="submit" id="chatGroupAdd">创建群聊</button>
                    </div>
                </form>

            </div> <!-- end card-body-->
        </div> <!-- end card-->
    </div> <!-- end col-->
</div>


<!--<script src="https://cdn.bootcss.com/jquery/1.11.3/jquery.min.js"></script>-->


<script>
    function check() {
        let uid = document.getElementsByName('uid')[0].value.trim();
        let name = document.getElementsByName('name')[0].value.trim();
        let pic = document.getElementsByName('pic')[0].value.trim();
        if (uid.length == 0) {
            alert("群主UID不能为空");
            return false;
        } else if (name.length == 0) {
            alert("群聊名称不能为空");
            return false;
        } else if (pic.length == 0) {
            alert("请上传群聊头像");
            return false;
        }

    }
       // 获取上传图片按钮和文件上传输入框
    var uploadButton = document.getElementById("uploadButton");
    var uploadImage = document.getElementById("uploadImage");
    var picLinkInput = document.getElementById("picLinkInput");
uploadFiles(uploadButton, uploadImage, picLinkInput, 'null', 'null');

<?php
include_once 'uploadJs.php';
?>
</script>

<?php
include_once 'Footer.php';
?>

</body>
</html>