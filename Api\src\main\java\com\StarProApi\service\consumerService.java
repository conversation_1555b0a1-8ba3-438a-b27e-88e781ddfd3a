package com.StarProApi.service;

import com.StarProApi.common.PageList;
import com.StarProApi.entity.identifyConsumer;

public interface consumerService {
    PageList<identifyConsumer> queryInfo(identifyConsumer consumer, int page, int pageSize);

    int insert(identifyConsumer dto);

    Integer remove(Object key);

    identifyConsumer selectByKey(Object uid);

    Integer update(identifyConsumer dto);
}
