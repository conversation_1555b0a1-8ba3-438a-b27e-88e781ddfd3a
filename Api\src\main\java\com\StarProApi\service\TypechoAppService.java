package com.StarProApi.service;

import java.util.Map;
import java.util.List;
import com.StarProApi.entity.*;
import com.StarProApi.common.PageList;

/**
 * 业务层
 * TypechoAppService
 * <AUTHOR>
 * @date 2023/06/09
 */
public interface TypechoAppService {

    /**
     * [新增]
     **/
    int insert(TypechoApp typechoApp);

    /**
     * [批量新增]
     **/
    int batchInsert(List<TypechoApp> list);

    /**
     * [更新]
     **/
    int update(TypechoApp typechoApp);

    /**
     * [删除]
     **/
    int delete(Object key);

    /**
     * [批量删除]
     **/
    int batchDelete(List<Object> keys);

    /**
     * [主键查询]
     **/
    TypechoApp selectByKey(Object key);

    /**
     * [条件查询]
     **/
    List<TypechoApp> selectList (TypechoApp typechoApp);

    /**
     * [分页条件查询]
     **/
    PageList<TypechoApp> selectPage (TypechoApp typechoApp, Integer page, Integer pageSize);

    /**
     * [总量查询]
     **/
    int total(TypechoApp typechoApp);
}
