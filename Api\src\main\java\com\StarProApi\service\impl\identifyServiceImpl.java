package com.StarProApi.service.impl;

import com.StarProApi.common.PageList;
import com.StarProApi.entity.TypechoApiconfig;
import com.StarProApi.service.TypechoApiconfigService;
import com.alibaba.fastjson.JSON;
import com.StarProApi.entity.identifyCompany;
import com.StarProApi.entity.identifyConsumer;
import com.StarProApi.service.identifyService;
import com.StarProApi.common.HttpUtils;
import net.dreamlu.mica.core.utils.StringUtil;
import org.apache.http.HttpResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.PropertySource;
import org.springframework.stereotype.Service;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLEncoder;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
@PropertySource(value="classpath:application.properties", encoding="utf-8", ignoreResourceNotFound = true)
public class identifyServiceImpl implements identifyService {
    @Autowired
    private consumerServiceImpl consumerService;
    @Autowired
    private companyServiceImpl companyService;

    @Autowired
    private TypechoApiconfigService apiconfigService;


    @Override
    public PageList<identifyConsumer> identifyConsumer(identifyConsumer dto) {
        PageList<identifyConsumer> pageList = new PageList<>();
        String idCard = dto.getIdCard();
        String name = dto.getName();
        Integer rows = 0;
        TypechoApiconfig apiconfig = apiconfigService.selectByKey(1);
        try {
            String urlSend = apiconfig.getIdentifyiIdcardHost() + apiconfig.getIdentifyiIdcardPath() + "?idCard=" + idCard + "&name="+ URLEncoder.encode(name, "UTF-8");// 【5】拼接请求链接
            URL url = new URL(urlSend);
            HttpURLConnection httpURLCon = (HttpURLConnection) url.openConnection();
            httpURLCon.setRequestProperty("Authorization", "APPCODE " + apiconfig.getIdentifyiIdcardAppcode());//格式Authorization:APPCODE (中间是英文空格)
            int httpCode = httpURLCon.getResponseCode();
            if (httpCode == 200) {
                String json = read(httpURLCon.getInputStream());
                System.out.print("获取返回的json："+json);
                //接口成功返回，判断审核状态
                Map map = JSON.parseObject(json, Map.class);
                if("01".equals(map.get("status").toString())) {
                    dto.setIdentifyStatus("1");
                    rows = consumerService.update(dto);
                }else{
                    dto.setIdentifyStatus("0");
                }
                String msg = inputMsg(map.get("status").toString(),rows);
                pageList.setMsg(msg);
            } else {
                dto.setIdentifyStatus("0");
                Map<String, List<String>> map = httpURLCon.getHeaderFields();
                String error = map.get("X-Ca-Error-Message").get(0);
                if (httpCode == 400 && error.equals("Invalid AppCode `not exists`")) {
                    System.out.println("AppCode错误 ");
                    pageList.setMsg("AppCode错误 ");
                } else if (httpCode == 400 && error.equals("Invalid Url")) {
                    System.out.println("请求的 Method、Path 或者环境错误");
                    pageList.setMsg("请求的 Method、Path 或者环境错误");
                } else if (httpCode == 400 && error.equals("Invalid Param Location")) {
                    System.out.println("参数错误");
                    pageList.setMsg("参数错误");
                } else if (httpCode == 403 && error.equals("Unauthorized")) {
                    System.out.println("服务未被授权（或URL和Path不正确）");
                    pageList.setMsg("服务未被授权（或URL和Path不正确）");
                } else if (httpCode == 403 && error.equals("Quota Exhausted")) {
                    System.out.println("套餐包次数用完 ");
                    pageList.setMsg("套餐包次数用完");
                } else if (httpCode == 403 && error.equals("Api Market Subscription quota exhausted")) {
                    System.out.println("套餐包次数用完，请续购套餐");
                    pageList.setMsg("套餐包次数用完，请续购套餐");
                } else {
                    pageList.setMsg("参数名错误 或 其他错误");
                    System.out.println("参数名错误 或 其他错误");
                    System.out.println(error);
                }
            }
        } catch (Exception e) {
             e.printStackTrace();
        }
        pageList.getList().add(dto);

        return pageList;
    }

    @Override
    public PageList<identifyCompany> identifyCompany(identifyCompany dto) {
        TypechoApiconfig apiconfig = apiconfigService.selectByKey(1);
        PageList<identifyCompany> pageList = new PageList<>();
        Integer rows = 0;
        //如果信用代码与企业名称均没有，直接返回
        if(StringUtil.isEmpty(dto.getRegno()) && StringUtil.isEmpty(dto.getEntname())){
            dto.setIdentifyStatus("false");
            pageList.setList((List<identifyCompany>) dto);
            return pageList;
        }

        Map<String, String> headers = new HashMap<String, String>();
        //最后在header中的格式(中间是英文空格)为Authorization:APPCODE 83359fd73fe94948385f570e3c139105
        headers.put("Authorization", "APPCODE " + apiconfig.getIdentifyiCompanyAppcode());
        //根据API的要求，定义相对应的Content-Type
        headers.put("Content-Type", "application/x-www-form-urlencoded; charset=UTF-8");
        Map<String, String> querys = new HashMap<String, String>();
        Map<String, String> bodys = new HashMap<String, String>();
        if (!StringUtil.isEmpty(dto.getRegno())){
            bodys.put("biz_content", "{ regno: "+dto.getRegno() +", name: "+dto.getName()+", idcard: "+dto.getIdcard()+" }");
        } else{
            bodys.put("biz_content", "{entname : "+dto.getEntname() +", name: "+dto.getName()+", idcard: "+dto.getIdcard()+" }");
        }

        try {
            HttpResponse response = HttpUtils.doPost(apiconfig.getIdentifyiCompanyHost(), apiconfig.getIdentifyiCompanyPath(), HttpUtils.POST, headers, querys, bodys);
            System.out.println(response.toString());

            if(response.getStatusLine().getStatusCode() ==1){
                //验证通过后，插入个人实名认证数据
                dto.setIdentifyStatus("1");
                rows = companyService.update(dto);
            }else{
                dto.setIdentifyStatus("0");
            }
            String msg = inputMsg(String.valueOf(response.getStatusLine().getStatusCode()),rows);
            pageList.setMsg(msg);
        } catch (Exception e) {
            e.printStackTrace();
        }
        pageList.getList().add(dto);

        return pageList;
    }


    private static String read(InputStream is) throws IOException {
        StringBuffer sb = new StringBuffer();
        BufferedReader br = new BufferedReader(new InputStreamReader(is));
        String line = null;
        while ((line = br.readLine()) != null) {
            line = new String(line.getBytes(), "utf-8");
            sb.append(line);
        }
        br.close();
        return sb.toString();
    }
    private String inputMsg(String status,Integer rows){
        switch (status) {
            case "01":
                return rows>0?"认证成功":"认证成功，但同步本地失败。";
            case "02":
                return "实名认证不通过！";
            case "202":
                return "无法认证！(户口迁移，现役军人，刚退役军人，大学生迁户，身份证过期未办理或新办未更新。)";
            case "203":
                return "异常情况！";
            case "204":
                return "姓名格式不正确！";
            case "205":
                return "身份证格式不正确！";
            case "9999":
                return "系统维护!";
            case "1":
                return "认证成功";
            case "2":
                return "认证失败，数据不一致";
            case "400":
                return "认证失败！";
        }
        return "系统异常，请联系管理员！";
    }

}
