<?php
session_start();
?>

<?php
include_once 'connect.php';
$sql = "select * from typecho_contents order by cid desc";
$contents = mysqli_query($connect, $sql);
?>

<?php
include_once 'Nav.php';
?>

<link href="/admin/assets/css/vendor/dataTables.bootstrap4.css" rel="stylesheet" type="text/css"/>
<link href="/admin/assets/css/vendor/responsive.bootstrap4.css" rel="stylesheet" type="text/css"/>
<link href="/admin/assets/css/vendor/buttons.bootstrap4.css" rel="stylesheet" type="text/css"/>
<link href="/admin/assets/css/vendor/select.bootstrap4.css" rel="stylesheet" type="text/css"/>
<!-- third party css end -->


<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <h4 class="header-title mb-3">文章管理</h4><br />
                <div style="display:flex;justify-content: space-between;">
                <div>
                <button type="button" id="delete-selected" class="btn btn-success2 btn-sm btn-rounded left_10">
                    <i class="mdi mdi-delete-empty mr-1"></i> 批量删除
                </button>
                <button type="button" id="plPass-selected" class="btn btn-success2 btn-sm btn-rounded left_10">
                    <i class="mdi dripicons-rocket mr-1"></i> 批量通过
                </button>
                <button type="button" id="plRefuse-selected" class="btn btn-success2 btn-sm btn-rounded left_10">
                    <i class="mdi dripicons-warning mr-1"></i> 批量拒绝
                </button>
                </div>
                <div>
                <a class="fabu" href="articleAdd.php">
                        <button type="button" class="btn btn-success2 btn-sm btn-rounded right_10">
                            <i class="dripicons-upload"></i> 发布文章
                        </button>
                    </a><a class="fabu" href="metaAdmin.php">
                        <button type="button" class="btn btn-success2 btn-sm btn-rounded right_10">
                            <i class="dripicons-tags"></i> 分类管理
                        </button>
                    </a>
                </div>
                </div>
                <br /><br />
                <table id="basic-textlong-dx" class="table dt-responsive nowrap" width="100%">
                    <thead>
                    <tr>
                        <th style="width: 30px;">选择</th>
                        <th>cid</th>
                        <th>标题</th>
                        <th>时间</th>
                        <th>作者</th>
                        <th>状态</th>
                        <th style="width: 125px;">操作</th>
                    </tr>
                    </thead>

                    <tbody>
                    <?php
                    while ($articledata = mysqli_fetch_array($contents)) {
                        ?>
                        <tr>
                            <td class="select-checkbox"></td>
                            <td><?php echo $articledata['cid'] ?></td>
                            <td>
                                <?php echo $articledata['title'] ?>
                            </td>
                            <td>
                                <small class="text-muted"><?php 
                                $timestamp = $articledata['created'];
                                $date = date("Y-m-d H:i:s", $timestamp);
                                echo $date?></small>
                                
                            </td>
                            <td><small class="text-muted">UID:<?php echo $articledata['authorId'] ?></small></td>
                           <td>
                                <h5>
                                    <?php if ($articledata['status']== 'publish') { ?><span class="badge badge-success-lighten">已发布</span><?php } else if ($articledata['status']== 'reject') { ?><span class="badge badge-danger-lighten">已拒绝</span><?php } else { ?><span class="badge badge-warning-lighten">待审核</span><?php }?>
                                </h5>
                            </td>
                            <td>
                                <a href="articleAudit.php?cid=<?php echo $articledata['cid']; ?>">
                                <button style="white-space: nowrap;" type="button"
                                        class="btn btn-warning btn-rounded">
                                    <i class="dripicons-inbox"></i> 审核
                                </button>
                                </a>
                                <a href="articleEdit.php?cid=<?php echo $articledata['cid']; ?>">
                                <button style="white-space: nowrap;" type="button"
                                        class="btn btn-info btn-rounded">
                                    <i class="dripicons-document-edit"></i> 编辑
                                </button>
                                </a>
                                <a href="javascript:del(<?php echo $articledata['cid']; ?>);">
                                    <button style="white-space: nowrap;" type="button"
                                            class="btn btn-danger btn-rounded">
                                        <i class="mdi mdi-delete-empty mr-1"></i>删除
                                    </button>
                                </a>
                            </td>
                        </tr>
                        <?php
                    }
                    ?>
                    </tbody>
                </table>

            </div>  
        </div>  
    </div> 
</div>


<script>
    $('#delete-selected').click(function() {  
     
        var table = $('#basic-textlong-dx').DataTable();  
        var rows = table.rows({ 'selected': true }).nodes();  
        var ids = [];  
        $.each(rows, function(index, row) {  
            var id = $(row).children('td').eq(1).text();
            ids.push(id);  
        });  
        if (ids.length > 0) {  
            if (confirm('您确认要删除选中的帖子吗？')) {
                location.href = 'articleAuditPost.php?ids=' + ids +'&status=selected';
            }
        } else {  
            alert('请至少选择一篇帖子进行删除。');  
        }  
     
    }); 
   $('#plPass-selected').click(function() {  
     
        var table = $('#basic-textlong-dx').DataTable();  
        var rows = table.rows({ 'selected': true }).nodes();  
        var ids = [];  
        $.each(rows, function(index, row) {  
            var id = $(row).children('td').eq(1).text();
            ids.push(id);  
        });  
        if (ids.length > 0) {  
            if (confirm('您确认要通过选中的帖子吗？')) {
                location.href = 'articleAuditPost.php?ids=' + ids +'&status=plPass';
            }
        } else {  
            alert('请至少选择一篇帖子进行操作。');  
        }  
     
    }); 
    $('#plRefuse-selected').click(function() {  
     
        var table = $('#basic-textlong-dx').DataTable();  
        var rows = table.rows({ 'selected': true }).nodes();  
        var ids = [];  
        $.each(rows, function(index, row) {  
            var id = $(row).children('td').eq(1).text();
            ids.push(id);  
        });  
        if (ids.length > 0) {  
            if (confirm('您确认要拒绝选中的帖子吗？')) {
                location.href = 'articleAuditPost.php?ids=' + ids +'&status=plRefuse';
            }
        } else {  
            alert('请至少选择一篇帖子进行操作。');  
        }  
     
    }); 
    function del(cid) {
        if (confirm('您确认要删除cid为' + cid + '的文章吗？')) {
            location.href = 'articleAuditPost.php?cid=' + cid +'&status=Del';
        }
    }
</script>


<?php
include_once 'Footer.php';
?>

<!-- third party js -->
<script src="/admin/assets/js/vendor/jquery.dataTables.min.js"></script>
<script src="/admin/assets/js/vendor/dataTables.bootstrap4.js"></script>
<script src="/admin/assets/js/vendor/dataTables.responsive.min.js"></script>
<script src="/admin/assets/js/vendor/responsive.bootstrap4.min.js"></script>
<script src="/admin/assets/js/vendor/dataTables.buttons.min.js"></script>
<script src="/admin/assets/js/vendor/buttons.bootstrap4.min.js"></script>
<script src="/admin/assets/js/vendor/buttons.html5.min.js"></script>
<script src="/admin/assets/js/vendor/buttons.flash.min.js"></script>
<script src="/admin/assets/js/vendor/buttons.print.min.js"></script>
<script src="/admin/assets/js/vendor/dataTables.keyTable.min.js"></script>
<script src="/admin/assets/js/vendor/dataTables.select.min.js"></script>
<!-- third party js ends -->
<!-- demo app -->
<script src="/admin/assets/js/pages/demo.datatable-init.js"></script>
<!-- end demo js-->


</body>
</html>