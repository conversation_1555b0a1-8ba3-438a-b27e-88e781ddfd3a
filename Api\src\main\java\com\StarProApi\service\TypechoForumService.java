package com.StarProApi.service;

import java.util.Map;
import java.util.List;
import com.StarProApi.entity.*;
import com.StarProApi.common.PageList;

/**
 * 业务层
 * TypechoForumService
 * <AUTHOR>
 * @date 2023/02/20
 */
public interface TypechoForumService {

    /**
     * [新增]
     **/
    int insert(TypechoForum typechoForum);

    /**
     * [批量新增]
     **/
    int batchInsert(List<TypechoForum> list);

    /**
     * [更新]
     **/
    int update(TypechoForum typechoForum);

    /**
     * [删除]
     **/
    int delete(Object key);

    /**
     * [批量删除]
     **/
    int batchDelete(List<Object> keys);

    /**
     * [主键查询]
     **/
    TypechoForum selectByKey(Object key);

    /**
     * [条件查询]
     **/
    List<TypechoForum> selectList (TypechoForum typechoForum);

    /**
     * [分页条件查询]
     **/
    PageList<TypechoForum> selectPage (TypechoForum typechoForum, Integer page, Integer pageSize,String order,String searchKey);

    /**
     * [总量查询]
     **/
    int total(TypechoForum typechoForum,String searchKey);
}
