package com.StarProApi.service;

import java.util.Map;
import java.util.List;
import com.StarProApi.entity.*;
import com.StarProApi.common.PageList;

/**
 * 业务层
 * TypechoVipsService
 * <AUTHOR>
 * @date 2023/06/09
 */
public interface TypechoVipsService {

    /**
     * [新增]
     **/
    int insert(TypechoVips typechoVips);

    /**
     * [批量新增]
     **/
    int batchInsert(List<TypechoVips> list);

    /**
     * [更新]
     **/
    int update(TypechoVips typechoVips);

    /**
     * [删除]
     **/
    int delete(Object key);

    /**
     * [批量删除]
     **/
    int batchDelete(List<Object> keys);

    /**
     * [主键查询]
     **/
    TypechoVips selectByKey(Object key);

    /**
     * [条件查询]
     **/
    List<TypechoVips> selectList (TypechoVips typechoVips);

    /**
     * [分页条件查询]
     **/
    PageList<TypechoVips> selectPage (TypechoVips typechoVips, Integer page, Integer pageSize);

    /**
     * [总量查询]
     **/
    int total(TypechoVips typechoVips);
}
