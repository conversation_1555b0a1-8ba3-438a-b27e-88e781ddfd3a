package com.StarProApi.service.impl;

import com.StarProApi.entity.*;
import com.StarProApi.common.PageList;
import com.StarProApi.dao.*;
import com.StarProApi.service.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 业务层实现类
 * TypechoGptServiceImpl
 * <AUTHOR>
 * @date 2024/06/17
 */
@Service
public class TypechoGptServiceImpl implements TypechoGptService {

    @Autowired
	TypechoGptDao dao;

    @Override
    public int insert(TypechoGpt typechoGpt) {
        return dao.insert(typechoGpt);
    }

    @Override
    public int batchInsert(List<TypechoGpt> list) {
    	return dao.batchInsert(list);
    }

    @Override
    public int update(TypechoGpt typechoGpt) {
    	return dao.update(typechoGpt);
    }

    @Override
    public int delete(Object key) {
    	return dao.delete(key);
    }

    @Override
    public int batchDelete(List<Object> keys) {
        return dao.batchDelete(keys);
    }

	@Override
	public TypechoGpt selectByKey(Object key) {
		return dao.selectByKey(key);
	}

	@Override
	public List<TypechoGpt> selectList(TypechoGpt typechoGpt) {
		return dao.selectList(typechoGpt);
	}

	@Override
	public PageList<TypechoGpt> selectPage(TypechoGpt typechoGpt, Integer offset, Integer pageSize,String searchKey,String order) {
		PageList<TypechoGpt> pageList = new PageList<>();

		int total = this.total(typechoGpt,searchKey);

		Integer totalPage;
		if (total % pageSize != 0) {
			totalPage = (total /pageSize) + 1;
		} else {
			totalPage = total /pageSize;
		}

		int page = (offset - 1) * pageSize;

		List<TypechoGpt> list = dao.selectPage(typechoGpt, page, pageSize,searchKey,order);

		pageList.setList(list);
		pageList.setStartPageNo(offset);
		pageList.setPageSize(pageSize);
		pageList.setTotalCount(total);
		pageList.setTotalPageCount(totalPage);
		return pageList;
	}

	@Override
	public int total(TypechoGpt typechoGpt,String searchKey) {
		return dao.total(typechoGpt,searchKey);
	}
}