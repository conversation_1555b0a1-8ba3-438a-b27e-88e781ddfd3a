<?php
session_start();
?>
<?php
include_once 'Nav.php';
$sql = "SELECT * FROM Sy_pages";
$result = mysqli_query($connect, $sql);
// 检查查询结果是否为空
if (mysqli_num_rows($result) > 0) {
    // 获取第一行数据作为结果集
    $row = mysqli_fetch_assoc($result);
}
?>
<div class="row">
    <div class="col-lg-12">
        <div class="card">
            <div class="card-body">
                <h4 class="header-title mb-3">分类页设置</h4>

                <form class="needs-validation" action="categoryPagePost.php" method="post" onsubmit="return check()"
                      novalidate>
                    <div class="form-group mb-3">
                    <script>
                        function myOnClickHandler2(obj) {
                            var input = document.getElementById("switch1");
                            console.log(input);
                            if (obj.checked) {
                                console.log("打开");
                                input.value = "1";
                            } else {
                                console.log("关闭");
                                input.value = "0";
                            }
                        }
                    </script>
                    <label for="validationCustom01">用户统计条</label>
                    <?php
                    if ($row['Usernumber']==1) {
                        echo '<input type="checkbox" name="Usernumber" id="switch1" value="1" data-switch="success"
                           onclick="myOnClickHandler2(this)" checked>';
                    }else{
                        echo '<input type="checkbox" name="Usernumber" id="switch1" value="0" data-switch="success"
                           onclick="myOnClickHandler2(this)">';
                    }
                    ?>
                    <label id="switchurl" style="display:block;" for="switch1" data-on-label="打开"
                           data-off-label="关闭"></label>
                    </div>
                    <div class="form-group col-sm-4" id="Circlestyle">
                        <label for="validationCustom01">分类排版样式</label>
                        <select class="form-control" id="example-select" name="Circlestyle">
                            <?php
                            if ($row['Circlestyle']==1) {
                                echo '<option value="1" selected>板块排版</option>
                                      <option value="2">大图排版</option>';
                            }else{
                                echo '<option value="1">板块排版</option>
                                      <option value="2" selected>大图排版</option>';
                            }
                            ?>
                            
                        </select>
                    </div>
                    <div class="form-group mb-3 text_right">
                        <button class="btn btn-success" type="submit" id="categoryPagePost">保存修改</button>
                    </div>
                </form>

            </div>  
        </div> 
    </div>  
</div>


<!--<script src="https://cdn.bootcss.com/jquery/1.11.3/jquery.min.js"></script>-->


<?php
include_once 'Footer.php';
?>

</body>
</html>