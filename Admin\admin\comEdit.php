<?php
session_start();
?>



<?php
include_once 'Nav.php';
$coid = $_GET['coid'];
$article = "SELECT * FROM typecho_comments WHERE coid='$coid' limit 1";
$resarticle = mysqli_query($connect, $article);
$mod = mysqli_fetch_array($resarticle);

?>
<div class="row">

    <div class="col-lg-12">
        <div class="card">
            <div class="card-body">
                <h4 class="header-title mb-3 size_18">编辑评论</h4>

                <form class="needs-validation" action="comEditPost.php" method="post" onsubmit="return check()"
                      novalidate>
                    <div class="form-group col-sm-4">
                        <label for="validationCustom01">评论id</label>
                        <input type="number" class="form-control" id="validationCustom01"
                               name="coid" value="<?php echo $coid ?>" readonly>
                    </div>
                    <div class="form-group col-sm-4">
                        <label for="validationCustom01">发布者UID</label>
                        <input type="number" class="form-control" id="validationCustom01" placeholder="请输入发布者UID"
                               name="uid" value="<?php echo $mod['authorId'] ?>" required>
                    </div>
                    <label for="validationCustom01">评论内容</label>
                        <textarea id="notice" class="form-control" rows="6" name="text" required><?php echo $mod['text'] ?></textarea>
                        <br />
                    <div class="form-group mb-3 text_right">
                        <button class="btn btn-primary" type="submit" id="comEditPost">修改评论</button>
                    </div>
                </form>

            </div> <!-- end card-body-->
        </div> <!-- end card-->
    </div> <!-- end col-->
</div>


<!--<script src="https://cdn.bootcss.com/jquery/1.11.3/jquery.min.js"></script>-->

<script>
    function check() {
        let uid = document.getElementsByName('uid')[0].value.trim();
        let text = document.getElementsByName('text')[0].value.trim();
        if (uid.length == 0) {
            alert("发布者UID不能为空");
            return false;
        } else if (text.length == 0) {
            alert("评论不能为空");
            return false;
        }

    }

</script>

<?php
include_once 'Footer.php';
?>

</body>
</html>