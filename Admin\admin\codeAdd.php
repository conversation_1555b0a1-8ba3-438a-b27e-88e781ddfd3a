<?php
session_start();
?>
<?php
include_once 'connect.php';
$ipchaxun = "select * from typecho_invitation";
$ipres = mysqli_query($connect, $ipchaxun);
$IPinfo = mysqli_fetch_array($ipres);
?>

<?php
include_once 'Nav.php';
?>
<div class="row">

    <div class="col-lg-12">
        <div class="card">
            <div class="card-body">
                <h4 class="header-title mb-3">注册码生成</h4>

                <form class="needs-validation" action="codeAddPost.php" method="post" novalidate onsubmit="return check()">
                    <div class="form-group mb-3">
                        <label for="validationCustom05">生成数量</label> <span class="badge badge-success-lighten" style="font-size: 0.8rem;">输入范围1~100</span>
                        <input type="number" class="form-control" id="validationCustom05" placeholder="批量生成注册码的数量"
                               name="code" value="" required>
                    </div>
                    <div class="form-group mb-3 text_right">
                        <button class="btn btn-success"  type="submit" id="codeAddPost">批量生成</button>
                    </div>
                </form>

            </div>  
        </div> 
    </div>  
</div>
<script>
     function check() {
       
        let code = document.getElementsByName('code')[0].value.trim();
        
        if (code.length == 0) {
            alert("请输入生成数量");
            return false;
        }
        
        if (code < 1 || code > 100) {
            alert("生成数量范围应在 1 到 100 之间");
            return false;
        }
    }
</script>
<?php
include_once 'Footer.php';
?>

</body>
</html>