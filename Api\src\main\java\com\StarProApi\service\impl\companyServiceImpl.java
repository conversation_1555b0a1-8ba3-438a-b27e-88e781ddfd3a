package com.StarProApi.service.impl;

import com.StarProApi.common.PageList;
import com.StarProApi.entity.identifyCompany;
import com.StarProApi.entity.identifyConsumer;
import com.StarProApi.service.companyService;
import org.springframework.beans.factory.annotation.Autowired;
import com.StarProApi.dao.companyDao;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class companyServiceImpl implements companyService {

    @Autowired
    private companyDao companyDao;
    @Override
    public PageList<identifyCompany> queryInfo(identifyCompany dto, int page, int pageSize) {
        PageList<identifyCompany> pageList = new PageList<>();
        page = (page - 1) * pageSize;

        List<identifyCompany> list = companyDao.queryInfo(dto,page,pageSize);
        pageList.setList(list);
        pageList.setStartPageNo(page);
        pageList.setPageSize(pageSize);
        return pageList;
    }

    @Override
    public PageList<identifyCompany> queryInfo(identifyCompany dto) {
        PageList<identifyCompany> pageList = new PageList<>();

        List<identifyCompany> list = companyDao.queryInfo(dto,1,10);
        pageList.setList(list);

        return pageList;
    }

    @Override
    public int insert(identifyCompany dto) {
        return companyDao.insert(dto);
    }

    @Override
    public Integer remove(String key) {
        return companyDao.remove(key);
    }

    @Override
    public Integer update(identifyCompany dto) {
        return companyDao.update(dto);
    }

    @Override
    public identifyCompany selectByKey(Object uid) {
        return companyDao.selectByKey(uid);
    }

    @Override
    public PageList<identifyCompany> queryAll() {
        PageList<identifyCompany> pageList = new PageList<>();
//        page = (page - 1) * pageSize;

        List<identifyCompany> list = companyDao.queryAll();
        pageList.setList(list);
//        pageList.setStartPageNo(page);
//        pageList.setPageSize(pageSize);
        return pageList;
    }

}
