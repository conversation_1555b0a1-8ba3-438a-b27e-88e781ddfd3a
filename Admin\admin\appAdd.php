<?php
session_start();
?>


<?php
include_once 'Nav.php';

?>
<div class="row">
    <div class="col-lg-12">
        <div class="card">
            <div class="card-body">
                <h4 class="header-title mb-3">添加应用</h4>
                <form class="needs-validation" action="appAddPost.php" method="post"
                      novalidate>
                    <div class="form-group mb-3">
                          <label for="name">应用名称</label>
                          <input name="name" class="form-control" type="text" id="name" placeholder="请输入应用名称" required="">
                    </div>
                    <div class="form-group col-sm-4">
                        <label for="validationCustom01">应用类型</label>
                            <select class="form-control" id="example-select" name="type" required>
                                <option value="app" selected>APP</option>
                                <option value="web">Web</option>
                                
                            </select>
                    </div>
                    <div class="form-group mb-3">
                          <label for="adpid">广告联盟ID
                          <span style="font-size: 0.7rem;color:#acacac;margin-left:10px">
                              可留空，用于APP内激励视频等广告，目前仅支持uniapp官方
                          </span></label>
                          <input name="adpid" class="form-control" type="text" id="adpid" placeholder="请输入广告联盟ID">
                    </div>
                    <div class="form-group mb-3 text_right">
                        <button class="btn btn-success" type="submit" id="appAddPost">添加应用</button>
                    </div>
                </form>

            </div>  
        </div> 
    </div>  
</div>


<!--<script src="https://cdn.bootcss.com/jquery/1.11.3/jquery.min.js"></script>-->


<?php
include_once 'Footer.php';
?>

</body>
</html>