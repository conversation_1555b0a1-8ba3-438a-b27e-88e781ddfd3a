package com.StarProApi.service.impl;

import com.StarProApi.entity.*;
import com.StarProApi.common.PageList;
import com.StarProApi.dao.*;
import com.StarProApi.service.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 业务层实现类
 * TypechoForumSectionServiceImpl
 * <AUTHOR>
 * @date 2023/02/20
 */
@Service
public class TypechoForumSectionServiceImpl implements TypechoForumSectionService {

    @Autowired
	TypechoForumSectionDao dao;

    @Override
    public int insert(TypechoForumSection typechoForumSection) {
        return dao.insert(typechoForumSection);
    }

    @Override
    public int batchInsert(List<TypechoForumSection> list) {
    	return dao.batchInsert(list);
    }

    @Override
    public int update(TypechoForumSection typechoForumSection) {
    	return dao.update(typechoForumSection);
    }

    @Override
    public int delete(Object key) {
    	return dao.delete(key);
    }

    @Override
    public int batchDelete(List<Object> keys) {
        return dao.batchDelete(keys);
    }

	@Override
	public TypechoForumSection selectByKey(Object key) {
		return dao.selectByKey(key);
	}

	@Override
	public List<TypechoForumSection> selectList(TypechoForumSection typechoForumSection) {
		return dao.selectList(typechoForumSection);
	}

	@Override
	public PageList<TypechoForumSection> selectPage(TypechoForumSection typechoForumSection, Integer offset, Integer pageSize,String order,String searchKey) {
		PageList<TypechoForumSection> pageList = new PageList<>();

		int total = this.total(typechoForumSection);

		Integer totalPage;
		if (total % pageSize != 0) {
			totalPage = (total /pageSize) + 1;
		} else {
			totalPage = total /pageSize;
		}

		int page = (offset - 1) * pageSize;

		List<TypechoForumSection> list = dao.selectPage(typechoForumSection, page, pageSize,order,searchKey);

		pageList.setList(list);
		pageList.setStartPageNo(offset);
		pageList.setPageSize(pageSize);
		pageList.setTotalCount(total);
		pageList.setTotalPageCount(totalPage);
		return pageList;
	}

	@Override
	public int total(TypechoForumSection typechoForumSection) {
		return dao.total(typechoForumSection);
	}
}