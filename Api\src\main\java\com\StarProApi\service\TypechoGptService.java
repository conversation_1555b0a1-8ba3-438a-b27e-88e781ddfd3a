package com.StarProApi.service;

import java.util.Map;
import java.util.List;
import com.StarProApi.entity.*;
import com.StarProApi.common.PageList;

/**
 * 业务层
 * TypechoGptService
 * <AUTHOR>
 * @date 2024/06/17
 */
public interface TypechoGptService {

    /**
     * [新增]
     **/
    int insert(TypechoGpt typechoGpt);

    /**
     * [批量新增]
     **/
    int batchInsert(List<TypechoGpt> list);

    /**
     * [更新]
     **/
    int update(TypechoGpt typechoGpt);

    /**
     * [删除]
     **/
    int delete(Object key);

    /**
     * [批量删除]
     **/
    int batchDelete(List<Object> keys);

    /**
     * [主键查询]
     **/
    TypechoGpt selectByKey(Object key);

    /**
     * [条件查询]
     **/
    List<TypechoGpt> selectList (TypechoGpt typechoGpt);

    /**
     * [分页条件查询]
     **/
    PageList<TypechoGpt> selectPage (TypechoGpt typechoGpt, Integer page, Integer pageSize,String searchKey,String order);

    /**
     * [总量查询]
     **/
    int total(TypechoGpt typechoGpt,String searchKey);
}
