<?php
session_start();
?>
<?php
include_once 'connect.php';
$withdrawals1 = "SELECT cid FROM typecho_contents ORDER BY cid DESC LIMIT 1";
$withdrawalsResult1 = mysqli_query($connect, $withdrawals1);
$withdrawal1 = mysqli_fetch_array($withdrawalsResult1);
$cid = $withdrawal1['cid'] + 1;
$title = htmlspecialchars(trim($_POST['articletitle']),ENT_QUOTES);
$text = $_POST['articletext'];

$uid = trim($_POST['uid']);
$category = $_POST['category'];
$time = time();
$file = $_SERVER['PHP_SELF'];


if (isset($_SESSION['loginadmin']) && $_SESSION['loginadmin'] <> '') {
    $charu = "insert into typecho_contents (title,slug,created,modified,text,authorId) values ('$title','$cid','$time','$time','$text','$uid')";
    $result = mysqli_query($connect, $charu);
    $charu2 = "insert into typecho_relationships (cid,mid) values ('$cid','$category')";
    $result2 = mysqli_query($connect, $charu2);
    $redisKeys = $connectRedis->keys('starapi_*');
        foreach ($redisKeys as $redisKey) {
            $connectRedis->del($redisKey);
        }
    if ($result&&$result2) {
            echo "<script>alert('发布成功');location.href = 'articleAdmin.php';</script>";
        } else {
            echo "<script>alert('发布失败');location.href = 'articleAdmin.php';</script>";   
        }
} else {
    echo "<script>alert('非法操作，行为已记录');location.href = 'warning.php?route=$file';</script>";
}
