package com.StarProApi.dao;

import com.StarProApi.entity.*;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * dao层接口
 * TypechoGptChatDao
 * <AUTHOR>
 * @date 2024/06/17
 */
@Mapper
public interface TypechoGptChatDao {

    /**
     * [新增]
     **/
    int insert(TypechoGptChat typechoGptChat);

    /**
     * [批量新增]
     **/
    int batchInsert(List<TypechoGptChat> list);

    /**
     * [更新]
     **/
    int update(TypechoGptChat typechoGptChat);

    /**
     * [删除]
     **/
    int delete(Object key);

    /**
     * [批量删除]
     **/
    int batchDelete(List<Object> list);

    /**
     * [主键查询]
     **/
    TypechoGptChat selectByKey(Object key);

    /**
     * [条件查询]
     **/
    List<TypechoGptChat> selectList (TypechoGptChat typechoGptChat);

    /**
     * [分页条件查询]
     **/
    List<TypechoGptChat> selectPage (@Param("typechoGptChat") TypechoGptChat typechoGptChat, @Param("page") Integer page, @Param("pageSize") Integer pageSize, @Param("order") String order);

    /**
     * [总量查询]
     **/
    int total(TypechoGptChat typechoGptChat);
}
