package com.StarProApi.service.impl;

import com.StarProApi.entity.*;
import com.StarProApi.common.PageList;
import com.StarProApi.dao.*;
import com.StarProApi.service.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 业务层实现类
 * TypechoGptMsgServiceImpl
 * <AUTHOR>
 * @date 2024/06/17
 */
@Service
public class TypechoGptMsgServiceImpl implements TypechoGptMsgService {

    @Autowired
	TypechoGptMsgDao dao;

    @Override
    public int insert(TypechoGptMsg typechoGptMsg) {
        return dao.insert(typechoGptMsg);
    }

    @Override
    public int batchInsert(List<TypechoGptMsg> list) {
    	return dao.batchInsert(list);
    }

    @Override
    public int update(TypechoGptMsg typechoGptMsg) {
    	return dao.update(typechoGptMsg);
    }

    @Override
    public int delete(Object key) {
    	return dao.delete(key);
    }

    @Override
    public int batchDelete(List<Object> keys) {
        return dao.batchDelete(keys);
    }

	@Override
	public TypechoGptMsg selectByKey(Object key) {
		return dao.selectByKey(key);
	}

	@Override
	public List<TypechoGptMsg> selectList(TypechoGptMsg typechoGptMsg) {
		return dao.selectList(typechoGptMsg);
	}

	@Override
	public PageList<TypechoGptMsg> selectPage(TypechoGptMsg typechoGptMsg, Integer offset, Integer pageSize,String searchKey) {
		PageList<TypechoGptMsg> pageList = new PageList<>();

		int total = this.total(typechoGptMsg,searchKey);

		Integer totalPage;
		if (total % pageSize != 0) {
			totalPage = (total /pageSize) + 1;
		} else {
			totalPage = total /pageSize;
		}

		int page = (offset - 1) * pageSize;

		List<TypechoGptMsg> list = dao.selectPage(typechoGptMsg, page, pageSize,searchKey);

		pageList.setList(list);
		pageList.setStartPageNo(offset);
		pageList.setPageSize(pageSize);
		pageList.setTotalCount(total);
		pageList.setTotalPageCount(totalPage);
		return pageList;
	}

	@Override
	public int total(TypechoGptMsg typechoGptMsg,String searchKey) {
		return dao.total(typechoGptMsg,searchKey);
	}
}