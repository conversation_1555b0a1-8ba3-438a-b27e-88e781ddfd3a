package com.StarProApi.dao;

import com.StarProApi.entity.*;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * dao层接口
 * TypechoGptMsgDao
 * <AUTHOR>
 * @date 2024/06/17
 */
@Mapper
public interface TypechoGptMsgDao {

    /**
     * [新增]
     **/
    int insert(TypechoGptMsg typechoGptMsg);

    /**
     * [批量新增]
     **/
    int batchInsert(List<TypechoGptMsg> list);

    /**
     * [更新]
     **/
    int update(TypechoGptMsg typechoGptMsg);

    /**
     * [删除]
     **/
    int delete(Object key);

    /**
     * [批量删除]
     **/
    int batchDelete(List<Object> list);

    /**
     * [主键查询]
     **/
    TypechoGptMsg selectByKey(Object key);

    /**
     * [条件查询]
     **/
    List<TypechoGptMsg> selectList (TypechoGptMsg typechoGptMsg);

    /**
     * [分页条件查询]
     **/
    List<TypechoGptMsg> selectPage (@Param("typechoGptMsg") TypechoGptMsg typechoGptMsg, @Param("page") Integer page, @Param("pageSize") Integer pageSize, @Param("searchKey") String searchKey);

    /**
     * [总量查询]
     **/
    int total( @Param("typechoGptMsg") TypechoGptMsg typechoGptMsg, @Param("searchKey") String searchKey);
}
