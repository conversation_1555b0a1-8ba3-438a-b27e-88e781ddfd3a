package com.StarProApi.web;

import com.StarProApi.annotation.LoginRequired;
import com.StarProApi.common.*;
import com.StarProApi.entity.TypechoApiconfig;
import com.StarProApi.service.SecurityService;
import com.StarProApi.service.TypechoApiconfigService;
import com.StarProApi.service.UploadService;
import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import com.google.gson.Gson;
import com.qiniu.common.QiniuException;
import com.qiniu.common.Zone;
import com.qiniu.http.Response;
import com.qiniu.storage.Configuration;
import com.qiniu.storage.UploadManager;
import com.qiniu.storage.model.DefaultPutRet;
import com.qiniu.util.Auth;
import org.apache.commons.net.ftp.FTP;
import org.apache.commons.net.ftp.FTPClient;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.system.ApplicationHome;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Controller;
import org.springframework.util.ClassUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import com.qcloud.cos.COSClient;
import com.qcloud.cos.ClientConfig;
import com.qcloud.cos.auth.BasicCOSCredentials;
import com.qcloud.cos.auth.COSCredentials;
import com.qcloud.cos.model.PutObjectRequest;
import com.qcloud.cos.model.PutObjectResult;
import com.qcloud.cos.region.Region;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.*;
import java.net.URLDecoder;
import java.util.Calendar;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;
import java.util.Base64;

/**
 * 文件上传
 *
 * 提供本地和cos上传，之后的接口支持都加在这里
 * */

@Controller
@RequestMapping(value = "/upload/")
public class UploadController {

    @Value("${web.prefix}")
    private String dataprefix;



    @Autowired
    private TypechoApiconfigService apiconfigService;

    @Autowired
    private SecurityService securityService;

    @Autowired
    private UploadService uploadService;

    @Autowired
    private RedisTemplate redisTemplate;

    @Value("${webinfo.key}")
    private String key;
    EditFile editFile = new EditFile();
    RedisHelp redisHelp =new RedisHelp();
    ResultAll Result = new ResultAll();
    baseFull baseFull = new baseFull();
    UserStatus UStatus = new UserStatus();

    /**
     * 通用上传接口
     * 除这个接口外，其它接口都是为了兼容旧版
     */
    @RequestMapping(value = "/full",method = RequestMethod.POST)
    @ResponseBody
    public Object full(@RequestParam(value = "file") MultipartFile file,
                       @RequestParam(value = "token", required = false) String  token,
                       @RequestParam(value = "webkey", required = false) String  webkey) throws IOException {
        Integer uStatus = UStatus.getStatus(token,this.dataprefix,redisTemplate);

        Integer isStarAdminUpload;
        if(uStatus==0){
            if(webkey == null){
                return Result.getResultJson(0,"用户未登录或Token验证失败",null);
            }
            if(!webkey.equals(this.key)){
                return Result.getResultWeUp(1,"请输入正确的访问key",null);
            }
            if(webkey.length()<1){
                return Result.getResultJson(0,"用户未登录或Token验证失败",null);
            }
            isStarAdminUpload = 1;
        }else{
            isStarAdminUpload = 0;
        }
        Map map =redisHelp.getMapValue(this.dataprefix+"_"+"userInfo"+token,redisTemplate);
        Integer uid;
        if (isStarAdminUpload==1){
            uid = 0;
        }else{
            uid = Integer.parseInt(map.get("uid").toString());
        }
        if(file == null){
            return new UploadMsg(0,"文件为空",null);
        }
        String result;
        if (isStarAdminUpload == 1){
            result = Result.getResultWeUp(1,"未开启任何上传通道，请检查配置",null);
        }else{
            result = Result.getResultJson(0,"未开启任何上传通道，请检查配置",null);
        }

        TypechoApiconfig apiconfig = UStatus.getConfig(this.dataprefix,apiconfigService,redisTemplate);
        //验证上传大小

        Integer flieUploadType = 0;  //0为普通文件，1为图片，2为媒体
        String oldFileName = file.getOriginalFilename();
        String eName = "";
        try{
            eName = oldFileName.substring(oldFileName.lastIndexOf("."));
        }catch (Exception e){
            oldFileName = oldFileName +".png";
            eName = oldFileName.substring(oldFileName.lastIndexOf("."));
        }
        BufferedImage bi = null;
        try {
            bi = ImageIO.read(file.getInputStream());
        } catch (IOException e) {
            e.printStackTrace();
        }

        if(bi != null||eName.equals(".WEBP")||eName.equals(".webp")){
            flieUploadType = 1;
        }

        Integer isMedia = baseFull.isMedia(eName);
        if(isMedia.equals(1)){
            flieUploadType = 2;
        }
        Integer uploadPicMax = apiconfig.getUploadPicMax();
        Integer uploadMediaMax = apiconfig.getUploadMediaMax();
        Integer uploadFilesMax = apiconfig.getUploadFilesMax();
        if(flieUploadType.equals(0)){
            long filesMax = uploadFilesMax * 1024 * 1024;
            if (file.getSize() > filesMax) {
                // 文件大小超过限制，返回错误消息或进行其他处理
                if (isStarAdminUpload == 1){
                    return Result.getResultWeUp(1,"文件大小不能超过"+filesMax+"M",null);
                }else{
                    return Result.getResultJson(0,"文件大小不能超过"+filesMax+"M",null);
                }

            }
        }
        if(flieUploadType.equals(1)){
            long picMax = uploadPicMax * 1024 * 1024;
            if (file.getSize() > picMax) {
                // 文件大小超过限制，返回错误消息或进行其他处理
                if (isStarAdminUpload == 1){//getResultWeUp
                    return Result.getResultWeUp(1,"图片大小不能超过"+picMax+"M",null);
                }else{
                    return Result.getResultJson(0,"图片大小不能超过"+picMax+"M",null);
                }

            }
        }

        if(flieUploadType.equals(2)){
            long mediaMax = uploadMediaMax * 1024 * 1024;
            if (file.getSize() > mediaMax) {
                // 文件大小超过限制，返回错误消息或进行其他处理
                if (isStarAdminUpload == 1){//getResultWeUp
                    return Result.getResultWeUp(1,"媒体大小不能超过"+mediaMax+"M",null);
                }else{
                    return Result.getResultJson(0,"媒体大小不能超过"+mediaMax+"M",null);
                }

            }
        }
        //如果为图片，则开始内容检测
        if(flieUploadType.equals(1)){
            if(apiconfig.getCmsSwitch()>1){
                try {
                    // 读取文件内容为字节数组
                    byte[] fileContent = file.getBytes();

                    // 将字节数组进行Base64编码
                    String fileBase64 =  Base64.getEncoder().encodeToString(fileContent);
                    Map violationData = securityService.picViolation(fileBase64,0);

                    if(violationData.get("Suggestion")!=null){
                        String Suggestion = violationData.get("Suggestion").toString();
                        if(Suggestion.equals("Block")){
                            if (isStarAdminUpload == 1){//getResultWeUp
                                return Result.getResultWeUp(1,"图片内容违规，请检查后重新提交！",null);
                            }else{
                                return Result.getResultJson(0,"图片内容违规，请检查后重新提交！",null);
                            }
                        }
                    }
                    if(violationData.get("Suggestion")!=null){
                        String Suggestion = violationData.get("Suggestion").toString();
                        if(Suggestion.equals("Block")){
                            if (isStarAdminUpload == 1){//getResultWeUp
                                return Result.getResultWeUp(1,"图片内容违规，请检查后重新提交！",null);
                            }else{
                                return Result.getResultJson(0,"图片内容违规，请检查后重新提交！",null);
                            }
                        }
                    }
                } catch (IOException e) {
                    // 处理异常
                    e.printStackTrace();
                }
            }
        }
        //验证上传大小结束


        if(apiconfig.getUploadType().equals("cos")){
            result = uploadService.cosUpload(file,this.dataprefix,apiconfig,uid);
        }
        if(apiconfig.getUploadType().equals("local")){
            result = uploadService.localUpload(file,this.dataprefix,apiconfig,uid);
        }
        if(apiconfig.getUploadType().equals("oss")){
            result = uploadService.ossUpload(file,this.dataprefix,apiconfig,uid);
        }
        if(apiconfig.getUploadType().equals("ftp")){
            result = uploadService.ftpUpload(file,this.dataprefix,apiconfig,uid);
        }
        if(apiconfig.getUploadType().equals("qiniu")){
            result = uploadService.qiniuUpload(file,this.dataprefix,apiconfig,uid);
        }
        return result;
    }
    private class UploadMsg {
        public int status;
        public String msg;
        public String path;

        public UploadMsg() {
            super();
        }

        public UploadMsg(int status, String msg, String path) {
            this.status = status;
            this.msg = msg;
            this.path = path;
        }
    }
    //文件转base64
    public static String convertToBase64(MultipartFile file) {
        try {
            // 读取文件内容为字节数组
            byte[] fileContent = file.getBytes();

            // 将字节数组进行Base64编码
            return Base64.getEncoder().encodeToString(fileContent);
        } catch (IOException e) {
            // 处理异常
            e.printStackTrace();
            return null;
        }
    }

    /**
     * Base64图片上传接口
     */
    @RequestMapping(value = "/base64",method = RequestMethod.POST)
    @ResponseBody
    public Object base64Upload(@RequestParam(value = "base64", required = true) String base64Img,
                               @RequestParam(value = "token", required = false) String token,
                               @RequestParam(value = "webkey", required = false) String webkey) {

        Integer uStatus = UStatus.getStatus(token,this.dataprefix,redisTemplate);

        // 验证登录状态和权限
        Integer isStarAdminUpload;
        if(uStatus==0){
            if(webkey == null){
                return Result.getResultJson(0,"用户未登录或Token验证失败",null);
            }
            if(!webkey.equals(this.key)){
                return Result.getResultWeUp(1,"请输入正确的访问key",null);
            }
            if(webkey.length()<1){
                return Result.getResultJson(0,"用户未登录或Token验证失败",null);
            }
            isStarAdminUpload = 1;
        }else{
            isStarAdminUpload = 0;
        }

        // 获取用户ID
        Integer uid;
        if (isStarAdminUpload==1){
            uid = 0;
        }else{
            Map map = redisHelp.getMapValue(this.dataprefix+"_"+"userInfo"+token,redisTemplate);
            uid = Integer.parseInt(map.get("uid").toString());
        }

        // 参数验证
        if(base64Img == null || base64Img.isEmpty()){
            return isStarAdminUpload == 1 ?
                    Result.getResultWeUp(1,"base64数据不能为空",null) :
                    Result.getResultJson(0,"base64数据不能为空",null);
        }

        // 获取配置
        TypechoApiconfig apiconfig = UStatus.getConfig(this.dataprefix,apiconfigService,redisTemplate);
        if(apiconfig == null){
            return isStarAdminUpload == 1 ?
                    Result.getResultWeUp(1,"上传配置错误",null) :
                    Result.getResultJson(0,"上传配置错误",null);
        }

        // 如果未开启任何上传通道
        if(apiconfig.getUploadType() == null || apiconfig.getUploadType().isEmpty()){
            return isStarAdminUpload == 1 ?
                    Result.getResultWeUp(1,"未开启任何上传通道，请检查配置",null) :
                    Result.getResultJson(0,"未开启任何上传通道，请检查配置",null);
        }

        // 验证base64格式
        if (!base64Img.contains(",")) {
            return isStarAdminUpload == 1 ?
                    Result.getResultWeUp(1,"无效的base64图片格式",null) :
                    Result.getResultJson(0,"无效的base64图片格式",null);
        }

        String[] parts = base64Img.split(",");

        // 验证base64头格式
        if (!parts[0].matches("^data:image/[a-zA-Z0-9+-]+;base64$")) {
            return isStarAdminUpload == 1 ?
                    Result.getResultWeUp(1,"无效的文件格式",null) :
                    Result.getResultJson(0,"无效的文件格式",null);
        }

        // 验证MIME类型
        String mimeType = parts[0].split(";")[0].split(":")[1];
        if (!mimeType.startsWith("image/")) {
            return isStarAdminUpload == 1 ?
                    Result.getResultWeUp(1,"仅支持图片格式的base64数据",null) :
                    Result.getResultJson(0,"仅支持图片格式的base64数据",null);
        }

        // 验证base64内容的合法性
        String base64Image = parts[1];
        if (!base64Image.matches("^[A-Za-z0-9+/]+={0,2}$")) {
            return isStarAdminUpload == 1 ?
                    Result.getResultWeUp(1,"无效的base64编码",null) :
                    Result.getResultJson(0,"无效的base64编码",null);
        }

        // 解码base64数据进行大小检查
        byte[] imageBytes;
        try {
            imageBytes = Base64.getDecoder().decode(base64Image);
        } catch (IllegalArgumentException e) {
            return isStarAdminUpload == 1 ?
                    Result.getResultWeUp(1,"base64解码失败",null) :
                    Result.getResultJson(0,"base64解码失败",null);
        }

        // 检查图片大小
        long picMax = apiconfig.getUploadPicMax() * 1024 * 1024;  // 转换为字节
        if (imageBytes.length > picMax) {
            return isStarAdminUpload == 1 ?
                    Result.getResultWeUp(1,"图片大小不能超过" + apiconfig.getUploadPicMax() + "MB",null) :
                    Result.getResultJson(0,"图片大小不能超过" + apiconfig.getUploadPicMax() + "MB",null);
        }

        // 验证图片内容
        try (InputStream is = new ByteArrayInputStream(imageBytes)) {
            BufferedImage bi = ImageIO.read(is);
            if (bi == null && !mimeType.equals("image/svg+xml")) {
                return isStarAdminUpload == 1 ?
                        Result.getResultWeUp(1,"无效的图片文件",null) :
                        Result.getResultJson(0,"无效的图片文件",null);
            }
        } catch (IOException e) {
            return isStarAdminUpload == 1 ?
                    Result.getResultWeUp(1,"图片验证失败",null) :
                    Result.getResultJson(0,"图片验证失败",null);
        }

        // 内容安全检测
        if(apiconfig.getCmsSwitch()>1){
            try {
                // 使用完整的base64字符串进行检测
                Map violationData = securityService.picViolation(base64Img,0);
                if(violationData.get("Suggestion")!=null){
                    String Suggestion = violationData.get("Suggestion").toString();
                    if(Suggestion.equals("Block")){
                        return isStarAdminUpload == 1 ?
                                Result.getResultWeUp(1,"图片内容违规，请检查后重新提交！",null) :
                                Result.getResultJson(0,"图片内容违规，请检查后重新提交！",null);
                    }
                }
            } catch (Exception e) {
                e.printStackTrace();
                // 可以考虑是否需要在内容检测失败时阻止上传
                // return isStarAdminUpload == 1 ?
                //     Result.getResultWeUp(1,"内容检测失败",null) :
                //     Result.getResultJson(0,"内容检测失败",null);
            }
        }

        // 调用base64上传服务
        String result = uploadService.base64Upload(base64Img, this.dataprefix, apiconfig, uid);
        return result;
    }
}
