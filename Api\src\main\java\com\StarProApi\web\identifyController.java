package com.StarProApi.web;


import com.StarProApi.annotation.LoginRequired;
import com.StarProApi.common.*;
import com.StarProApi.entity.TypechoInbox;
import com.StarProApi.entity.TypechoUserlog;
import com.StarProApi.service.TypechoApiconfigService;
import com.StarProApi.service.TypechoInboxService;
import com.StarProApi.service.TypechoUsersService;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.StarProApi.entity.identifyCompany;
import com.StarProApi.entity.identifyConsumer;
import com.StarProApi.service.impl.companyServiceImpl;
import com.StarProApi.service.impl.consumerServiceImpl;
import com.StarProApi.service.impl.identifyServiceImpl;
import org.apache.commons.lang3.StringUtils;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 认证模块
 * TypechoUserIdentify
 * <AUTHOR>
 * @date 2021/11/29
 */


@Component
@Controller
@RequestMapping(value = "/identify")
public class identifyController {


    @Autowired
    private identifyServiceImpl identifyService;

    @Autowired
    private companyServiceImpl companyService;

    @Autowired
    private consumerServiceImpl consumerService;

    @Autowired
    private TypechoApiconfigService apiconfigService;

    @Autowired
    private TypechoUsersService usersService;

    @Autowired
    private TypechoInboxService inboxService;

    @Autowired
    private RedisTemplate redisTemplate;

    @Value("${web.prefix}")
    private String dataprefix;

    @Value("${mybatis.configuration.variables.prefix}")
    private String prefix;

    @Autowired
    private JdbcTemplate jdbcTemplate;

    RedisHelp redisHelp = new RedisHelp();

    UserStatus UStatus = new UserStatus();

    ResultAll Result = new ResultAll();

    EditFile editFile = new EditFile();
    /**查询是否认证**/
    @RequestMapping(value = "/identifyStatus")
    @ResponseBody
    @LoginRequired(purview = "-1")
    public String identifyStatus( @RequestParam(value = "uid", required = false) Integer  uid) {
        try{
            if(uid==null){
                return Result.getResultJson(0,"参数错误",null);
            }
            List<identifyCompany> companyList = jdbcTemplate.query("SELECT * FROM `" + prefix + "_company` WHERE uid = " + uid + ";", new BeanPropertyRowMapper<>(identifyCompany.class));
            List<identifyConsumer> consumerList = jdbcTemplate.query("SELECT * FROM `" + prefix + "_consumer` WHERE uid = " + uid + ";", new BeanPropertyRowMapper<>(identifyConsumer.class));

            JSONObject data = new JSONObject();
            if(companyList.size()>0){
                identifyCompany company = companyList.get(0);
                if(company.getIdentifyStatus().equals("1")){
                    data.put("identifyCompany",1);
                }else{
                    data.put("identifyCompany",-1);
                }

            }else{
                data.put("identifyCompany",0);
            }
            if(consumerList.size()>0){
                identifyConsumer consumer = consumerList.get(0);
                if(consumer.getIdentifyStatus().equals("1")){
                    data.put("identifyConsumer",1);
                }else{
                    data.put("identifyConsumer",-1);
                }
            }else{
                data.put("identifyConsumer",0);
            }
            JSONObject response = new JSONObject();
            response.put("code" , 1);
            response.put("msg"  , "");
            response.put("data" , data);

            return response.toString();
        }catch (Exception e){
            e.printStackTrace();
            return Result.getResultJson(0,"接口请求异常，请联系管理员",null);
        }
    }
    /**登录后查询认证信息**/
    @RequestMapping(value = "/identifyInfo")
    @ResponseBody
    @LoginRequired(purview = "0")
    public String identifyInfo( @RequestParam(value = "token", required = false) String  token) {
        try{
            Map map = redisHelp.getMapValue(this.dataprefix + "_" + "userInfo" + token, redisTemplate);
            Integer uid =Integer.parseInt(map.get("uid").toString());
            JSONObject data = new JSONObject();
            List<TypechoUserlog> companyList = jdbcTemplate.queryForObject("SELECT * FROM `"+prefix+"_company` WHERE uid = "+uid+";", List.class);
            List<TypechoUserlog> consumerList = jdbcTemplate.queryForObject("SELECT * FROM `"+prefix+"_consumer` WHERE uid = "+uid+";", List.class);
            if(companyList.size()>0){
                Map companyJson = JSONObject.parseObject(JSONObject.toJSONString(companyList.get(0)), Map.class);
                data.put("companyJson",companyJson);
            }
            if(consumerList.size()>0){
                Map consumerJson = JSONObject.parseObject(JSONObject.toJSONString(consumerList.get(0)), Map.class);
                data.put("consumerJson",consumerJson);
            }
            JSONObject response = new JSONObject();
            response.put("code" , 1);
            response.put("msg"  , "");
            response.put("data" , data);

            return response.toString();
        }catch (Exception e){
            e.printStackTrace();
            return Result.getResultJson(0,"接口请求异常，请联系管理员",null);
        }

    }
    /**个人认证请求**/
    @RequestMapping(value = "/identifyConsumer")
    @ResponseBody
    @LoginRequired(purview = "0")
    public String identifyConsumer(@RequestParam(value = "uid", required = false) String uid,
                                   @RequestParam(value = "token", required = false) String  token) {
        try{
            Map map =redisHelp.getMapValue(this.dataprefix+"_"+"userInfo"+token,redisTemplate);
            String group = map.get("group").toString();
            Integer Loguid =Integer.parseInt(map.get("uid").toString());
            if(!group.equals("administrator")){
                return Result.getResultJson(0,"你没有操作权限",null);
            }

            identifyConsumer consumer = consumerService.selectByKey(uid);
            if(consumer==null){
                return Result.getResultJson(0,"数据不存在",null);
            }
            PageList<identifyConsumer> list = identifyService.identifyConsumer(consumer);
            editFile.setLog("管理员"+Loguid+"审核了用户UID"+uid+"的个人认证");
            if(list.getMsg().equals(1)){
                Long date = System.currentTimeMillis();
                String created = String.valueOf(date).substring(0,10);
                TypechoInbox insert = new TypechoInbox();
                insert.setUid(Loguid);
                insert.setTouid(Integer.parseInt(uid));
                insert.setType("system");
                insert.setText("你的个人认证已审核通过。");
                insert.setCreated(Integer.parseInt(created));
                inboxService.insert(insert);
            }
            JSONObject response = new JSONObject();
            response.put("code", 1);
            response.put("msg", list.getMsg().equals(1)?"认证审核成功" : list.getMsg());
            response.put("data",  list.getMsg().equals(1)? list.getList() : new JSONArray());
            return response.toString();
        }catch (Exception e){
            e.printStackTrace();
            return Result.getResultJson(0,"接口请求异常，请联系管理员",null);
        }

    }

    /**个人认证请求(手动)**/
    @RequestMapping(value = "/systemIdentifyConsumer")
    @ResponseBody
    @LoginRequired(purview = "0")
    public String systemIdentifyConsumer(@RequestParam(value = "uid", required = false) String uid,
                                   @RequestParam(value = "token", required = false) String  token) {
        try{
            Map map =redisHelp.getMapValue(this.dataprefix+"_"+"userInfo"+token,redisTemplate);
            String group = map.get("group").toString();
            Integer Loguid =Integer.parseInt(map.get("uid").toString());
            if(!group.equals("administrator")){
                return Result.getResultJson(0,"你没有操作权限",null);
            }

            identifyConsumer consumer = consumerService.selectByKey(uid);
            if(consumer==null){
                return Result.getResultJson(0,"数据不存在",null);
            }
            consumer.setIdentifyStatus("1");
            int rows = consumerService.update(consumer);
            if(rows > 0){
                Long date = System.currentTimeMillis();
                String created = String.valueOf(date).substring(0,10);
                TypechoInbox insert = new TypechoInbox();
                insert.setUid(Loguid);
                insert.setTouid(Integer.parseInt(uid));
                insert.setType("system");
                insert.setText("你的个人认证已审核通过。");
                insert.setCreated(Integer.parseInt(created));
                inboxService.insert(insert);
            }
            editFile.setLog("管理员"+Loguid+"人工审核了用户UID"+uid+"的个人认证");
            JSONObject response = new JSONObject();
            response.put("code", rows);
            response.put("msg", rows > 0 ? "审核完成" : "审核失败");
            return response.toString();
        }catch (Exception e){
            e.printStackTrace();
            return Result.getResultJson(0,"接口请求异常，请联系管理员",null);
        }

    }

    @RequestMapping(value = "/consumerList")
    @ResponseBody
    @LoginRequired(purview = "2")
    public String consumerList(@RequestParam(value = "searchParams", required = false) String searchParams,
                                @RequestParam(value = "page", required = false, defaultValue = "1") Integer page,
                                @RequestParam(value = "pageSize", required = false, defaultValue = "10") Integer pageSize,
                                @RequestParam(value = "token", required = false) String  token) {
        try{
            Integer uStatus = UStatus.getStatus(token,this.dataprefix,redisTemplate);
            if(uStatus==0){
                return Result.getResultJson(0,"用户未登录或Token验证失败",null);
            }
            Map map =redisHelp.getMapValue(this.dataprefix+"_"+"userInfo"+token,redisTemplate);
            String group = map.get("group").toString();
            identifyConsumer dto = new identifyConsumer();
            if (StringUtils.isNotBlank(searchParams)) {
                JSONObject object = JSON.parseObject(searchParams);
                dto = object.toJavaObject(identifyConsumer.class);
            }
            PageList<identifyConsumer> list = consumerService.queryInfo(dto, page, pageSize);
            List<identifyConsumer> consumers = list.getList();
            List consumerList = new ArrayList();
            for (int i = 0; i < consumers.size(); i++) {
                Map json = JSONObject.parseObject(JSONObject.toJSONString(consumers.get(i)), Map.class);
                Integer userid = consumers.get(i).getUid();
                Map userJson = UserStatus.getUserInfo(userid,apiconfigService,usersService);
                json.put("userJson",userJson);
                consumerList.add(json);

            }
            JSONObject response = new JSONObject();
            response.put("code" , 1);
            response.put("msg"  , "");
            response.put("data" , consumerList);
            response.put("count", consumerList.size());
            return response.toString();
        }catch (Exception e){
            e.printStackTrace();
            return Result.getResultJson(0,"接口请求异常，请联系管理员",null);
        }

    }

    /**提交个人认证**/
    @RequestMapping(value = "/addConsumer")
    @ResponseBody
    @LoginRequired(purview = "0")
    public String addConsumer(@RequestParam(value = "params", required = false) String params,
                                 @RequestParam(value = "token", required = false) String  token){
        try{
            Map map = redisHelp.getMapValue(this.dataprefix + "_" + "userInfo" + token, redisTemplate);
            Integer uid =Integer.parseInt(map.get("uid").toString());

            identifyConsumer dto = new identifyConsumer();
            JSONObject response = new JSONObject();
            if (StringUtils.isNotBlank(params)) {
                JSONObject object = JSON.parseObject(params);
                dto = object.toJavaObject(identifyConsumer.class);
                if(dto.getIdCard()==null||dto.getName()==null){
                    return Result.getResultJson(0,"请完整提交所需信息",null);
                }
                dto.setUid(uid);

            }
            //插入数据前判断是否存在该记录
            List<identifyConsumer> consumerList = jdbcTemplate.query("SELECT * FROM `" + prefix + "_consumer` WHERE uid = ?", new Object[]{uid}, new BeanPropertyRowMapper<>(identifyConsumer.class));
            if (consumerList.size()>0) {
                return Result.getResultJson(0,"请不要多次提交身份信息",null);
            }
            Integer rows = consumerService.insert(dto);

            editFile.setLog("用户UID"+uid+"提交了个人认证");
            response.put("code", rows);
            response.put("msg", rows > 0 ? "信息提交成功" : "信息提交失败");
            return response.toString();
        }catch (Exception  e){
            e.printStackTrace();
            return Result.getResultJson(0,"接口请求异常，请联系管理员",null);
        }

    }
    /**删除个人认证**/
    @RequestMapping(value = "/removeConsumer")
    @ResponseBody
    @LoginRequired(purview = "2")
    public String removeConsumer(@RequestParam(value = "uid", required = false) String  uid,
                                 @RequestParam(value = "token", required = false) String  token){
        try{
            Map map =redisHelp.getMapValue(this.dataprefix+"_"+"userInfo"+token,redisTemplate);
            Integer Loguid =Integer.parseInt(map.get("uid").toString());
            identifyConsumer consumer = consumerService.selectByKey(uid);
            if(consumer==null){
                return Result.getResultJson(0,"数据不存在",null);
            }
            Integer rows = consumerService.remove(uid);
            Long date = System.currentTimeMillis();
            String created = String.valueOf(date).substring(0,10);
            TypechoInbox insert = new TypechoInbox();
            insert.setUid(Loguid);
            insert.setTouid(Integer.parseInt(uid));
            insert.setType("system");
            insert.setText("你的个人认证未审核通过，请重新提交。");
            insert.setCreated(Integer.parseInt(created));
            inboxService.insert(insert);
            editFile.setLog("管理员"+Loguid+"删除了用户UID"+uid+"的个人认证");
            JSONObject response = new JSONObject();
            response.put("code" , rows);
            response.put("msg"  , rows > 0 ? "操作成功" : "数据不存在");
            return response.toString();
        }catch (Exception e){
            e.printStackTrace();
            return Result.getResultJson(0,"接口请求异常，请联系管理员",null);
        }

    }

    /**机构认证请求-已关闭 蓝V接管**/
    @RequestMapping(value = "/identifyCompany")
    @ResponseBody
    @LoginRequired(purview = "2")
    public String identifyCompany(@RequestParam(value = "uid", required = false) String uid,
                                  @RequestParam(value = "token", required = false) String  token) {
        try{
            return Result.getResultJson(0,"接口已关闭",null);
            // Map map =redisHelp.getMapValue(this.dataprefix+"_"+"userInfo"+token,redisTemplate);
            // Integer Loguid =Integer.parseInt(map.get("uid").toString());
            // identifyCompany company = companyService.selectByKey(uid);
            // if(company==null){
            //     return Result.getResultJson(0,"数据不存在",null);
            // }
            // PageList<identifyCompany> list = identifyService.identifyCompany(company);
            // editFile.setLog("管理员"+Loguid+"审核了用户UID"+uid+"的机构认证");
            // if(list.getMsg().equals(1)){
            //     Long date = System.currentTimeMillis();
            //     String created = String.valueOf(date).substring(0,10);
            //     TypechoInbox insert = new TypechoInbox();
            //     insert.setUid(Loguid);
            //     insert.setTouid(Integer.parseInt(uid));
            //     insert.setType("system");
            //     insert.setText("你的机构认证已审核通过。");
            //     insert.setCreated(Integer.parseInt(created));
            //     inboxService.insert(insert);
            // }
            // JSONObject response = new JSONObject();
            // response.put("code", 1);
            // response.put("msg", list.getMsg().equals(1)?"认证审核成功" : list.getMsg());
            // response.put("data",  list.getMsg().equals(1)? list.getList() : new JSONArray());
            // return response.toString();
        }catch (Exception e){
            e.printStackTrace();
            return Result.getResultJson(0,"接口请求异常，请联系管理员",null);
        }

    }
    /**个人认证请求(手动)**/
    @RequestMapping(value = "/systemIdentifyCompany")
    @ResponseBody
    @LoginRequired(purview = "2")
    public String systemIdentifyCompany(@RequestParam(value = "uid", required = false) String uid,
                                         @RequestParam(value = "name", required = false) String name,
                                         @RequestParam(value = "type", required = false, defaultValue = "approve") String type,
                                         @RequestParam(value = "token", required = false) String  token) {
        try{
            Map map =redisHelp.getMapValue(this.dataprefix+"_"+"userInfo"+token,redisTemplate);
            String group = map.get("group").toString();
            Integer Loguid =Integer.parseInt(map.get("uid").toString());
            if(!group.equals("administrator")){
                return Result.getResultJson(0,"你没有操作权限",null);
            }

            identifyCompany company = companyService.selectByKey(uid);
            if(company==null){
                return Result.getResultJson(0,"数据不存在",null);
            }
            
            String logMessage = "";
            String notificationMessage = "";
            
            // 根据type类型执行不同操作
            if("approve".equals(type)){
                // 审核通过
                company.setIdentifyStatus("1");
                logMessage = "管理员"+Loguid+"人工审核了用户UID"+uid+"的蓝V认证";
                notificationMessage = "你的蓝V认证已审核通过。";
                
                // 如果同时提供了name参数，则更新蓝V称号
                if(name != null && !name.trim().isEmpty()){
                    company.setName(name.trim());
                    logMessage += "，并设置称号为："+name.trim();
                    notificationMessage += "被管理员赋予了：" + name.trim() + "称号";
                }
            } else if("modify".equals(type)){
                // 仅修改称号
                if(name != null && !name.trim().isEmpty()){
                    company.setName(name.trim());
                    logMessage = "管理员"+Loguid+"修改了用户UID"+uid+"的蓝V称号为："+name.trim();
                    notificationMessage = "你的蓝V称号已被管理员修改为：" + name.trim();
                } else {
                    return Result.getResultJson(0,"修改称号时必须提供name参数",null);
                }
            } else {
                return Result.getResultJson(0,"type参数错误，只支持approve或modify",null);
            }
            
            int rows = companyService.update(company);
            editFile.setLog(logMessage);
            
            if(rows>0){
                Long date = System.currentTimeMillis();
                String created = String.valueOf(date).substring(0,10);
                TypechoInbox insert = new TypechoInbox();
                insert.setUid(Loguid);
                insert.setTouid(Integer.parseInt(uid));
                insert.setType("system");
                insert.setText(notificationMessage);
                insert.setCreated(Integer.parseInt(created));
                inboxService.insert(insert);
            }

            JSONObject response = new JSONObject();
            response.put("code", rows);
            response.put("msg", rows > 0 ? ("approve".equals(type) ? "审核完成" : "称号修改完成") : ("approve".equals(type) ? "审核失败" : "称号修改失败"));
            return response.toString();
        }catch (Exception e){
            e.printStackTrace();
            return Result.getResultJson(0,"接口请求异常，请联系管理员",null);
        }

    }

    @RequestMapping(value = "/companyList")
    @ResponseBody
    @LoginRequired(purview = "2")
    public String companyList(@RequestParam(value = "searchParams", required = false) String searchParams,
                               @RequestParam(value = "page", required = false, defaultValue = "1") Integer page,
                               @RequestParam(value = "pageSize", required = false, defaultValue = "10") Integer pageSize,
                               @RequestParam(value = "token", required = false) String  token) {
        try{
            identifyCompany dto = new identifyCompany();
            if (StringUtils.isNotBlank(searchParams)) {
                JSONObject object = JSON.parseObject(searchParams);
                dto = object.toJavaObject(identifyCompany.class);
            }
            PageList<identifyCompany> list = companyService.queryInfo(dto, page, pageSize);
            List<identifyCompany> companys = list.getList();
            List companyList = new ArrayList();
            for (int i = 0; i < companys.size(); i++) {
                Map json = JSONObject.parseObject(JSONObject.toJSONString(companys.get(i)), Map.class);
                Integer userid = companys.get(i).getUid();
                Map userJson = UserStatus.getUserInfo(userid,apiconfigService,usersService);
                json.put("userJson",userJson);
                companyList.add(json);
            }
            JSONObject response = new JSONObject();
            response.put("code" , 1);
            response.put("msg"  , "");
            response.put("data" , companyList);
            response.put("count", companyList.size());
            return response.toString();
        }catch (Exception e){
            e.printStackTrace();
            return Result.getResultJson(0,"接口请求异常，请联系管理员",null);
        }

    }
    /**提交机构认证**/
    @RequestMapping(value = "/addCompany")
    @ResponseBody
    @LoginRequired(purview = "0")
    public String addCompany(@RequestParam(value = "params", required = false) String params,
                              @RequestParam(value = "token", required = false) String  token){
        try{
            Map map = redisHelp.getMapValue(this.dataprefix + "_" + "userInfo" + token, redisTemplate);
            Integer uid =Integer.parseInt(map.get("uid").toString());

            identifyCompany dto = new identifyCompany();
            JSONObject response = new JSONObject();
            if (StringUtils.isNotBlank(params)) {
                JSONObject object = JSON.parseObject(params);
                dto = object.toJavaObject(identifyCompany.class);
                if(dto.getRegno()==null||dto.getEntname()==null||dto.getIdcard()==null||dto.getName()==null){
                    return Result.getResultJson(0,"请完整提交所需信息",null);
                }
                dto.setUid(uid);
            }
            //插入数据前判断是否存在该记录
            List<identifyCompany> companyList = jdbcTemplate.query("SELECT * FROM `" + prefix + "_company` WHERE uid = " + uid + ";", new BeanPropertyRowMapper<>(identifyCompany.class));
            if (companyList.size()>0) {
                return Result.getResultJson(0,"请不要多次提交身份信息",null);
            }
            Integer rows = companyService.insert(dto);
            editFile.setLog("用户UID"+uid+"提交了蓝V认证");
            response.put("code", rows);
            response.put("msg", rows > 0 ? "信息提交成功" : "信息提交失败");
            return response.toString();
        }catch (Exception  e){
            e.printStackTrace();
            return Result.getResultJson(0,"接口请求异常，请联系管理员",null);
        }

    }
    /**删除机构认证**/
    @RequestMapping(value = "/removeCompany")
    @ResponseBody
    @LoginRequired(purview = "2")
    public String removeCompany(@RequestParam(value = "uid", required = false) String  uid,
                                 @RequestParam(value = "token", required = false) String  token){
        try{
            Map map =redisHelp.getMapValue(this.dataprefix+"_"+"userInfo"+token,redisTemplate);
            Integer Loguid =Integer.parseInt(map.get("uid").toString());
            identifyCompany company = companyService.selectByKey(uid);
            if(company==null){
                return Result.getResultJson(0,"数据不存在",null);
            }
            Integer rows = companyService.remove(uid);
            Long date = System.currentTimeMillis();
            String created = String.valueOf(date).substring(0,10);
            TypechoInbox insert = new TypechoInbox();
            insert.setUid(Loguid);
            insert.setTouid(Integer.parseInt(uid));
            insert.setType("system");
            insert.setText("你的蓝V认证未审核通过，请重新提交。");
            insert.setCreated(Integer.parseInt(created));
            inboxService.insert(insert);
            editFile.setLog("管理员"+Loguid+"删除了用户UID"+uid+"的蓝V认证");
            JSONObject response = new JSONObject();
            response.put("code" , rows);
            response.put("msg"  , rows > 0 ? "操作成功" : "数据不存在");
            return response.toString();
        }catch (Exception e){
            e.printStackTrace();
            return Result.getResultJson(0,"接口请求异常，请联系管理员",null);
        }

    }

    @RequestMapping(value = "/identifyHand")
    @ResponseBody
    @LoginRequired(purview = "0")
    public String identifyByHand(@RequestParam(value = "params", required = false) String params,
                                 @RequestParam(value = "token", required = false) String  token) {
        try{
            JSONObject response = new JSONObject();
            Integer rows = 0;
            Map<String, String> object = new HashMap<>();
            if (StringUtils.isNotBlank(params)) {
                object = JSON.parseObject(params, Map.class);
            }
            //企业认证
            if (StringUtils.equals("company",object.get("identifyType"))){
                ObjectMapper objectMapper = new ObjectMapper();
                identifyCompany entity = objectMapper.convertValue(object, identifyCompany.class);
                rows = companyService.update(entity);
            } else if(StringUtils.equals("consumer",object.get("identifyType"))){
                ObjectMapper objectMapper = new ObjectMapper();
                identifyConsumer entity = objectMapper.convertValue(object, identifyConsumer.class);
                rows = consumerService.update(entity);
            }
            response.put("code" , rows);
            response.put("msg"  , rows > 0 ? "操作成功" : "操作失败");
            return response.toString();
        }catch (Exception e){
            e.printStackTrace();
            return Result.getResultJson(0,"接口请求异常，请联系管理员",null);
        }

    }

    /**管理员主动授予蓝V认证**/
    @RequestMapping(value = "/adminAddCompany")
    @ResponseBody
    @LoginRequired(purview = "2")
    public String adminAddCompany(@RequestParam(value = "targetUid", required = true) String targetUid,
                                  @RequestParam(value = "params", required = false) String params,
                                  @RequestParam(value = "token", required = false) String  token){
        try{
            Map map = redisHelp.getMapValue(this.dataprefix + "_" + "userInfo" + token, redisTemplate);
            String group = map.get("group").toString();
            Integer adminUid = Integer.parseInt(map.get("uid").toString());
            
            if(!group.equals("administrator")){
                return Result.getResultJson(0,"你没有操作权限",null);
            }

            identifyCompany dto = new identifyCompany();
            JSONObject response = new JSONObject();
            
            if (StringUtils.isNotBlank(params)) {
                JSONObject object = JSON.parseObject(params);
                dto = object.toJavaObject(identifyCompany.class);
                
                // 验证必填字段（管理员添加时可以简化验证）
                if(dto.getName() == null || dto.getName().trim().isEmpty()){
                    return Result.getResultJson(0,"请至少提供蓝V称号信息",null);
                }
                
                // 设置目标用户ID
                dto.setUid(Integer.parseInt(targetUid));
                // 直接设置为已认证状态
                dto.setIdentifyStatus("1");
                // 如果没有提供其他信息，设置默认值
                if(dto.getRegno() == null || dto.getRegno().trim().isEmpty()) dto.setRegno("admin_added");
                if(dto.getEntname() == null || dto.getEntname().trim().isEmpty()) dto.setEntname("管理员添加");
                if(dto.getIdcard() == null || dto.getIdcard().trim().isEmpty()) dto.setIdcard("admin_added");
                
            } else {
                return Result.getResultJson(0,"请提供蓝V认证信息",null);
            }
            
            //检查是否存在该记录
            List<identifyCompany> companyList = jdbcTemplate.query("SELECT * FROM `" + prefix + "_company` WHERE uid = ?", new Object[]{Integer.parseInt(targetUid)}, new BeanPropertyRowMapper<>(identifyCompany.class));
            Integer rows = 0;
            String operation = "";
            
            if (companyList.size() > 0) {
                // 记录已存在，执行更新操作
                operation = "更新";
                rows = companyService.update(dto);
                editFile.setLog("管理员" + adminUid + "更新了用户UID" + targetUid + "的蓝V认证，称号：" + dto.getName() + "，更新结果：" + rows);
            } else {
                // 记录不存在，执行插入操作
                operation = "添加";
                rows = companyService.insert(dto);
                editFile.setLog("管理员" + adminUid + "主动为用户UID" + targetUid + "添加了蓝V认证，称号：" + dto.getName() + "，插入结果：" + rows);
            }
            
            // 发送通知给目标用户
            if(rows > 0){
                Long date = System.currentTimeMillis();
                String created = String.valueOf(date).substring(0,10);
                TypechoInbox insert = new TypechoInbox();
                insert.setUid(adminUid);
                insert.setTouid(Integer.parseInt(targetUid));
                insert.setType("system");
                insert.setText("恭喜！你被管理员授予了蓝V认证，称号：" + dto.getName());
                insert.setCreated(Integer.parseInt(created));
                inboxService.insert(insert);
            }
            
            response.put("code", rows);
            response.put("msg", rows > 0 ? ("蓝V认证" + operation + "成功") : ("蓝V认证" + operation + "失败"));
            return response.toString();
        }catch (Exception  e){
            e.printStackTrace();
            return Result.getResultJson(0,"接口请求异常，请联系管理员",null);
        }

    }
}

