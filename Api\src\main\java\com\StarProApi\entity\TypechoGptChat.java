package com.StarProApi.entity;

import java.io.Serializable;
import lombok.Data;
import java.util.Date;
import java.util.List;

/**
 * TypechoGptChat
 * <AUTHOR> 2024-06-17
 */
@Data
public class TypechoGptChat implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id  
     */
    private Integer id;

    /**
     * gptid  大模型id
     */
    private Integer gptid;

    /**
     * uid  用户ID
     */
    private Integer uid;

    /**
     * sessionId  聊天sessionId
     */
    private String sessionId;

    /**
     * created  创建时间
     */
    private Integer created;

    /**
     * replyTime  创建时间
     */
    private Integer replyTime;
}