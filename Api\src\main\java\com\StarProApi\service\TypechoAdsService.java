package com.StarProApi.service;

import java.util.Map;
import java.util.List;
import com.StarProApi.entity.*;
import com.StarProApi.common.PageList;

/**
 * 业务层
 * TypechoAdsService
 * <AUTHOR>
 * @date 2022/09/06
 */
public interface TypechoAdsService {

    /**
     * [新增]
     **/
    int insert(TypechoAds typechoAds);
    /**
     * [更新]
     **/
    int update(TypechoAds typechoAds);

    /**
     * [删除]
     **/
    int delete(Object key);

    /**
     * [主键查询]
     **/
    TypechoAds selectByKey(Object key);

    /**
     * [条件查询]
     **/
    List<TypechoAds> selectList (TypechoAds typechoAds);

    /**
     * [分页条件查询]
     **/
    PageList<TypechoAds> selectPage (TypechoAds typechoAds, Integer page, Integer pageSize,String searchKey);

    /**
     * [总量查询]
     **/
    int total(TypechoAds typechoAds);
}
