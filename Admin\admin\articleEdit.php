<?php
session_start();
?>



<?php
include_once 'Nav.php';
$cid = $_GET['cid'];
$withdrawals = "SELECT * FROM typecho_metas WHERE type = 'category' ORDER BY mid DESC";
$withdrawalsResult = mysqli_query($connect, $withdrawals);
$article = "SELECT * FROM typecho_contents WHERE cid='$cid' limit 1";
$resarticle = mysqli_query($connect, $article);
$mod = mysqli_fetch_array($resarticle);

$article2 = "SELECT * FROM typecho_relationships WHERE cid='$cid' limit 1";
$resarticle2 = mysqli_query($connect, $article2);
$mod2 = mysqli_fetch_array($resarticle2);
?>

<div class="row">

    <div class="col-lg-12">
        <div class="card">
            <div class="card-body">
                <h4 class="header-title mb-3 size_18">编辑文章</h4>

                <form class="needs-validation" action="articleEditPost.php" method="post" onsubmit="return check()"
                      novalidate>
                    <div class="form-group col-sm-4">
                        <label for="validationCustom01">文章cid</label>
                        <input type="text" class="form-control" id="validationCustom01"
                               name="cid" value="<?php echo $cid ?>" readonly>
                    </div>
                    <div class="form-group col-sm-4">
                        <label for="validationCustom01">发布者UID</label>
                        <input type="text" class="form-control" id="validationCustom01" placeholder="请输入发布者UID"
                               name="uid" value="<?php echo $mod['authorId'] ?>" required>
                    </div>
                    <div class="form-group col-sm-4">
                        <label for="validationCustom01">分类</label>
                            <select class="form-control" id="example-select" name="category">
                                <?php
                                while ($withdrawal = mysqli_fetch_array($withdrawalsResult)) {
                                    ?>
                                    <option value="<?php echo $withdrawal['mid']; ?>" <?php if($withdrawal['mid']==$mod2['mid']){echo "selected";} ?>><?php echo $withdrawal['name'] ?></option>
                                <?php
                                }
                                ?>
                            </select>
                    </div>
                    <div class="form-group mb-3">
                        <label for="validationCustom01">文章标题</label>
                        <input type="text" class="form-control" id="validationCustom01" placeholder="请输入标题"
                               name="articletitle" value="<?php echo $mod['title'] ?>" required>
                    </div>
                    <label for="validationCustom01">文章内容</label>
                    <div id="editor—wrapper">
                        <div id="toolbar-container"></div>
                            <div id="editor-container"></div>
                             <textarea id="editorContent" name="articletext" style="display: none;"><?php echo $mod['text'] ?></textarea>  
                        </div>
                    <div class="form-group mb-3 text_right">
                        <button class="btn btn-primary" type="submit" id="articleEditPost">修改文章</button>
                    </div>
                </form>

            </div> <!-- end card-body-->
        </div> <!-- end card-->
    </div> <!-- end col-->
</div>


<!--<script src="https://cdn.bootcss.com/jquery/1.11.3/jquery.min.js"></script>-->

<link href="https://unpkg.com/@wangeditor/editor@latest/dist/css/style.css" rel="stylesheet">
<script src="https://unpkg.com/@wangeditor/editor@latest/dist/index.js"></script>
<script>
const { createEditor, createToolbar } = window.wangEditor
const editorConfig = {
    placeholder: '请输入内容...',
    onChange(editor) {
      const html = editor.getHtml();  
    document.getElementById('editorContent').value = html;  
    },  
}
<?php 
$edithOf = true; 
include_once 'editor.php';
?>

<script>
    function check() {
        let title = document.getElementsByName('articletitle')[0].value.trim();
        let text = document.getElementsByName('articletext')[0].value.trim();
        if (title.length == 0) {
            alert("文章标题不能为空");
            return false;
        } else if (text.length == 0) {
            alert("文章内容不能为空");
            return false;
        }

    }

</script>

<?php
include_once 'Footer.php';
?>

</body>
</html>