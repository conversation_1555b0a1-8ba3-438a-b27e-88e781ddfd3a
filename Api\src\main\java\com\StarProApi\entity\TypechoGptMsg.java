package com.StarProApi.entity;

import java.io.Serializable;
import lombok.Data;
import java.util.Date;
import java.util.List;

/**
 * TypechoGptMsg
 * <AUTHOR> 2024-06-17
 */
@Data
public class TypechoGptMsg implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id  
     */
    private Integer id;

    /**
     * uid  发送人
     */
    private Integer uid;

    /**
     * gptid  所选GPT
     */
    private Integer gptid;

    /**
     * text  消息内容
     */
    private String text;

    /**
     * created  创建时间
     */
    private Integer created;

    /**
     * isAI  是否为AI回复
     */
    private Integer isAI;

}