package com.StarProApi.common;
import com.alibaba.fastjson.JSONObject;

import java.util.Map;

public class ResultAll {
    private JSONObject ResultJson = new JSONObject();
    private JSONObject ResultWeUp = new JSONObject();
    public String getResultJson(Integer code, String descr, Map data){
        this.ResultJson.put("code" , code);
        this.ResultJson.put("msg"  , descr);
        this.ResultJson.put("data"  , data);
        return ResultJson.toString();
    }
    public String getResultWeUp(Integer code, String descr, Map data){
        this.ResultWeUp.put("errno" , code);
        this.ResultWeUp.put("message"  , descr);
        this.ResultWeUp.put("data"  , data);
        return ResultWeUp.toString();
    }
}
