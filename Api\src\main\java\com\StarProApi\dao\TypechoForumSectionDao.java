package com.StarProApi.dao;

import com.StarProApi.entity.*;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * dao层接口
 * TypechoForumSectionDao
 * <AUTHOR>
 * @date 2023/02/20
 */
@Mapper
public interface TypechoForumSectionDao {

    /**
     * [新增]
     **/
    int insert(TypechoForumSection typechoForumSection);

    /**
     * [批量新增]
     **/
    int batchInsert(List<TypechoForumSection> list);

    /**
     * [更新]
     **/
    int update(TypechoForumSection typechoForumSection);

    /**
     * [删除]
     **/
    int delete(Object key);

    /**
     * [批量删除]
     **/
    int batchDelete(List<Object> list);

    /**
     * [主键查询]
     **/
    TypechoForumSection selectByKey(Object key);

    /**
     * [条件查询]
     **/
    List<TypechoForumSection> selectList (TypechoForumSection typechoForumSection);

    /**
     * [分页条件查询]
     **/
    List<TypechoForumSection> selectPage (@Param("typechoForumSection") TypechoForumSection typechoForumSection, @Param("page") Integer page, @Param("pageSize") Integer pageSize,@Param("order") String order,@Param("searchKey") String searchKey);

    /**
     * [总量查询]
     **/
    int total(TypechoForumSection typechoForumSection);
}
