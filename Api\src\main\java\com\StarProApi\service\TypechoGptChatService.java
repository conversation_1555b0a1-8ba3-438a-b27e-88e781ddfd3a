package com.StarProApi.service;

import java.util.Map;
import java.util.List;
import com.StarProApi.entity.*;
import com.StarProApi.common.PageList;

/**
 * 业务层
 * TypechoGptChatService
 * <AUTHOR>
 * @date 2024/06/17
 */
public interface TypechoGptChatService {

    /**
     * [新增]
     **/
    int insert(TypechoGptChat typechoGptChat);

    /**
     * [批量新增]
     **/
    int batchInsert(List<TypechoGptChat> list);

    /**
     * [更新]
     **/
    int update(TypechoGptChat typechoGptChat);

    /**
     * [删除]
     **/
    int delete(Object key);

    /**
     * [批量删除]
     **/
    int batchDelete(List<Object> keys);

    /**
     * [主键查询]
     **/
    TypechoGptChat selectByKey(Object key);

    /**
     * [条件查询]
     **/
    List<TypechoGptChat> selectList (TypechoGptChat typechoGptChat);

    /**
     * [分页条件查询]
     **/
    PageList<TypechoGptChat> selectPage (TypechoGptChat typechoGptChat, Integer page, Integer pageSize,String order);

    /**
     * [总量查询]
     **/
    int total(TypechoGptChat typechoGptChat);
}
