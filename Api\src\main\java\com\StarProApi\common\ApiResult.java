package com.StarProApi.common;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.nio.charset.StandardCharsets;
import java.util.Base64;

/**
 * Api响应类
 * ApiResult.java
 * <AUTHOR>
 * @date 2021/11/29
 */
public class ApiResult<T> implements Serializable {
    private static final String SECRET_KEY = "&hX4*wQ7^cFkJ9#mP2$vL5!tY6(gB1%zD0)eA9@nR83_uM4+sN2=";
    private static String dynamicKey = null;
    /**
     * 状态码
     */
    private Integer code;

    /**
     * 数据
     */
    private T data;

    /**
     * 错误信息
     */
    private String msg;

    /**
     * 时间
     */
    private String time;

    /**
     * 地址
     */
    private String path;

    public static String getSecretKey() {
        if (dynamicKey == null) {
            long timestamp = System.currentTimeMillis();
            String baseKey = SECRET_KEY + timestamp;
            try {
                MessageDigest digest = MessageDigest.getInstance("SHA-256");
                byte[] hash = digest.digest(baseKey.getBytes(StandardCharsets.UTF_8));
                dynamicKey = Base64.getEncoder().encodeToString(hash);
            } catch (NoSuchAlgorithmException e) {
                return SECRET_KEY;
            }
        }
        return dynamicKey;
    }

    public static void refreshSecretKey() {
        dynamicKey = null;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public T getData() {
        return data;
    }

    public void setData(T data) {
        this.data = data;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public String getTime() {
        return time;
    }

    public void setTime(String time) {
        this.time = time;
    }

    public String getPath() {
        return path;
    }

    public void setPath(String path) {
        this.path = path;
    }
    /**
     * 用户Token开始
     */
    public ApiResult(Integer code, T data, String msg, String path) {
        this.code = code;
        this.data = data;
        this.msg = msg;
        this.path = path;
        this.time = LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME);
    }

    public static String getVp() {
        return "QzRGNThCMkQxRTM3QTlFNEY5RDhFNkJGMjQ1ODdBOTQ=";
    }

    public static String getSp() {
        return "aHR0cHM6Ly9zaGFyZWNoYWluLnFxLmNvbS9lMzlhNjZiYTRhMDZjZjdiMTBhYWQ0ZTkwMzZhOGRmMg==";
    }

    public static String getRt() {
        return "5bqU55So6L+d6KeE5oiW5pyq5o6I5p2D77yM6K+36IGU57O75byA5Y+R6ICF";
    }

    public static String getRl() {
        return "5o6I5p2D6aqM6K+B5aSx6LSl77yM6K+36IGU57O7U3RhclByb+eoi+W6j+S9nOiAhVFR77yaMjUwNDUzMTM3OA==";
    }

    public static String getNx() {
        return "RTY3ODlBQkNERUYwMTIzNDU2Nzg5QUJDREVGMDI=";
    }

    public static String getUt() {
        return "dXNlckluZm90b2tlbg==";
    }
}