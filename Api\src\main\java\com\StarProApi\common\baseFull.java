package com.StarProApi.common;

//常用数据处理类

import com.StarProApi.entity.TypechoEmailtemplate;
import com.StarProApi.entity.TypechoUsers;
import com.StarProApi.service.TypechoEmailtemplateService;
import org.lionsoul.ip2region.xdb.Searcher;
import org.springframework.boot.system.ApplicationHome;
import org.springframework.util.DigestUtils;

import javax.servlet.http.HttpServletRequest;
import java.io.File;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.InetAddress;
import java.net.UnknownHostException;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.io.*;
import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import javax.crypto.KeyGenerator;
import javax.crypto.SecretKey;
import javax.crypto.Cipher;
import javax.crypto.spec.GCMParameterSpec;
import java.security.KeyPair;
import java.security.KeyPairGenerator;
import java.security.SecureRandom;
import java.io.ByteArrayOutputStream;
import java.nio.charset.StandardCharsets;




public class baseFull {
    //数组去重
    public Object[] threeClear(Object[] arr) {
        List list = new ArrayList();
        for (int i = 0; i < arr.length; i++) {
            if (!list.contains(arr[i])) {
                list.add(arr[i]);
            }
        }
        return list.toArray();
    }

    //获取字符串内图片地址
//    public List<String> getImageSrc(String htmlCode) {
//        List<String> urls = extractUrls(htmlCode);
//        List<String> imageUrls = new ArrayList<>();
//
//        for (String url : urls) {
//            if (url.matches(".+\\.(ico|jpe?g|png|bmp|gif|webp|ICO|JPE?G|PNG|BMP|GIF|WEBP)$")) {
//                imageUrls.add(url.replaceAll("\\)", ""));
//            }
//        }
//
//        return imageUrls;
//    }
    public List<String> getImageBase64(java.lang.String htmlCode) {
        List<java.lang.String> srcList = new ArrayList<>();
        // 定义正则表达式来匹配<img>标签的src属性
        Pattern imgPattern = Pattern.compile("<img[^>]+src\\s*=\\s*['\"]([^'\"]+)['\"][^>]*>");
        Matcher matcher = imgPattern.matcher(htmlCode);
        // 查找匹配项
        while (matcher.find()) {
            java.lang.String src = matcher.group(1); // 提取src值
            // 判断src值是否为Base64数据
            if (src.startsWith("data:image/")) {
                srcList.add(src); // 如果是Base64数据，才加入列表
            }
        }
        return srcList;
    }

    public List<String> getImageSrc(String htmlCode) {
        List<String> urls = extractUrls(htmlCode);
        List<String> imageUrls = new ArrayList<>();

        // 定义需要排除的图片名称
        Set<String> excludedImageNames = new HashSet<>(Arrays.asList("fileupload.png", "music.png", "urlpic.png"));

        for (String url : urls) {
            // 使用正则表达式检查 URL 是否以指定的图像格式结尾
            if (url.matches(".+\\.(ico|jpe?g|png|bmp|gif|webp|ICO|JPE?G|PNG|BMP|GIF|WEBP)$")) {
                // 获取图片名称
                String imageName = url.substring(url.lastIndexOf("/") + 1);
                // 检查图片名称是否在排除列表中
                if (!excludedImageNames.contains(imageName)) {
                    imageUrls.add(url.replaceAll("\\)", ""));
                }
            }
        }

        return imageUrls;
    }
    public List<Map<String, String>> getVideoInfo(String htmlCode) {
        List<Map<String, String>> videoInfoList = new ArrayList<>();

        // Pattern for img tags with video info
        Pattern imgPattern = Pattern.compile(
                "<img[^>]*?alt=\"src=([^\"]+)\\|poster=([^\"]+)\\|type=video\"[^>]*?>",
                Pattern.CASE_INSENSITIVE);

        // Pattern for traditional video tags
        Pattern videoPattern = Pattern.compile(
                "<video[^>]*?src=\"([^\"]+)\"[^>]*>(?:<source[^>]*?src=\"([^\"]+)\"[^>]*>)?(?:<[^>]*?poster=\"([^\"]+)\"[^>]*>)?.*?</video>",
                Pattern.CASE_INSENSITIVE);

        Matcher imgMatcher = imgPattern.matcher(htmlCode);
        Matcher videoMatcher = videoPattern.matcher(htmlCode);

        // Process img tags
        while (imgMatcher.find()) {
            Map<String, String> videoInfo = new HashMap<>();
            videoInfo.put("src", imgMatcher.group(1));
            videoInfo.put("poster", imgMatcher.group(2));
            videoInfoList.add(videoInfo);
        }

        // Process video tags
        while (videoMatcher.find()) {
            Map<String, String> videoInfo = new HashMap<>();
            if (videoMatcher.group(1) != null) {
                videoInfo.put("src", videoMatcher.group(1));
            } else if (videoMatcher.group(2) != null) {
                videoInfo.put("src", videoMatcher.group(2));
            }
            if (videoMatcher.group(3) != null) {
                videoInfo.put("poster", videoMatcher.group(3));
            }
            videoInfoList.add(videoInfo);
        }

        return videoInfoList;
    }

    private List<String> extractUrls(String text) {
        List<String> urls = new ArrayList<>();
        Pattern pattern = Pattern.compile("\\b(https?|ftp|file)://[-A-Z0-9+&@#/%?=~_|!:,.;]*[-A-Z0-9+&@#/%=~_|]", Pattern.CASE_INSENSITIVE);
        Matcher matcher = pattern.matcher(text);

        while (matcher.find()) {
            urls.add(text.substring(matcher.start(0), matcher.end(0)));
        }

        return urls;
    }

    //获取markdown内图片引用
    public List<String> getImageCode(String htmlCode) {
        List<String> containedUrls = new ArrayList<String>();
        String urlRegex = "((!\\[)[\\s\\S]+?(\\]\\[)[\\s\\S]+?(\\]))";
        Pattern pattern = Pattern.compile(urlRegex, Pattern.CASE_INSENSITIVE);
        Matcher urlMatcher = pattern.matcher(htmlCode);

        while (urlMatcher.find()) {
            containedUrls.add(htmlCode.substring(urlMatcher.start(0),
                    urlMatcher.end(0)));
        }
        List<String> codeList = new ArrayList<String>();
        for (int i = 0; i < containedUrls.size(); i++) {
            String word = containedUrls.get(i);

            codeList.add(word);
        }
        return codeList;
    }

    public static boolean isEmail(String string) {
        if (string == null)
            return false;
        String regEx1 = "^([a-z0-9A-Z]+[-_|\\.]?)+[a-z0-9A-Z]@([a-z0-9A-Z]+(-[a-z0-9A-Z]+)?\\.)+[a-zA-Z]{2,}$";
        Pattern p;
        Matcher m;
        p = Pattern.compile(regEx1);
        m = p.matcher(string);
        if (m.matches())
            return true;
        else
            return false;
    }

    //获取markdown引用的图片地址
    public List<String> getImageMk(String htmlCode) {
        List<String> containedUrls = new ArrayList<String>();
        // String urlRegex = "\\\\[\\\\d\\\\]:\\\\s(https?|http):((//)|(\\\\\\\\))+[\\\\w\\\\d:#@%/;$()~_?\\\\+-=\\\\\\\\\\\\.&]*";
        String urlRegex = "\\[\\d\\]:\\s(https?|http):((//)|(\\\\))+[\\w\\d:#@%/;$()~_?\\+-=\\\\\\.&]*";
        Pattern pattern = Pattern.compile(urlRegex, Pattern.CASE_INSENSITIVE);
        Matcher urlMatcher = pattern.matcher(htmlCode);

        while (urlMatcher.find()) {
            containedUrls.add(htmlCode.substring(urlMatcher.start(0),
                    urlMatcher.end(0)));
        }
        List<String> imageCode = new ArrayList<String>();
        for (int i = 0; i < containedUrls.size(); i++) {
            String word = containedUrls.get(i);
            if (word.indexOf(".ico") != -1 || word.indexOf(".jpg") != -1 || word.indexOf(".JPG") != -1 || word.indexOf(".jpeg") != -1 || word.indexOf(".png") != -1 || word.indexOf(".PNG") != -1 || word.indexOf(".bmp") != -1 || word.indexOf(".gif") != -1 || word.indexOf(".GIF") != -1 || word.indexOf(".webp") != -1 || word.indexOf(".WEBP") != -1) {
                imageCode.add(word.replaceAll("\\)", ""));
            }
        }
        return imageCode;
    }

    //获取ip地址
    public static String getIpAddr(HttpServletRequest request) {
        String ipAddress = null;
        try {
            ipAddress = request.getHeader("x-forwarded-for");
            if (ipAddress == null || ipAddress.length() == 0 || "unknown".equalsIgnoreCase(ipAddress)) {
                ipAddress = request.getHeader("Proxy-Client-IP");
            }
            if (ipAddress == null || ipAddress.length() == 0 || "unknown".equalsIgnoreCase(ipAddress)) {
                ipAddress = request.getHeader("WL-Proxy-Client-IP");
            }
            if (ipAddress == null || ipAddress.length() == 0 || "unknown".equalsIgnoreCase(ipAddress)) {
                ipAddress = request.getRemoteAddr();
                if (ipAddress.equals("127.0.0.1")) {
                    // 据网卡取本机配置的IP
                    InetAddress inet = null;
                    try {
                        inet = InetAddress.getLocalHost();
                    } catch (UnknownHostException e) {
                        e.printStackTrace();
                    }
                    ipAddress = inet.getHostAddress();
                }
            }
            // 对于通过多个代理的情况，第一个IP为客户端真实IP,多个IP按照','分割
            if (ipAddress != null && ipAddress.length() > 15) { // "***.***.***.***".length()
                // = 15
                if (ipAddress.indexOf(",") > 0) {
                    ipAddress = ipAddress.substring(0, ipAddress.indexOf(","));
                }
            }
        } catch (Exception e) {
            ipAddress = "";
        }
        // ipAddress = this.getRequest().getRemoteAddr();

        return ipAddress;
    }

    /**
     * 提取字符串中文字符
     *
     * @param text
     * @return
     */
    public static String toStrByChinese(String text) {
        text = text.replaceAll("\\[hide(([\\s\\S])*?)\\[\\/hide\\]", "");
        text = text.replaceAll("\\{hide(([\\s\\S])*?)\\{\\/hide\\}", "");
        text = text.replaceAll("(\\\r\\\n|\\\r|\\\n|\\\n\\\r)", "");
        text = text.replaceAll("\\s*", "");
        text = text.replaceAll("</?[^>]+>", "");
        //去掉文章开头的图片插入
        text = text.replaceAll("((https?|http):((//)|(\\\\))+[\\w\\d:#@%/;$()~_?\\+-=\\\\\\.&]*)", "");
        text = text.replaceAll("((!\\[)[\\s\\S]+?(\\]\\[)[\\s\\S]+?(\\]))", "");
        text = text.replaceAll("((!\\[)[\\s\\S]+?(\\]))", "");
        text = text.replaceAll("\\(", "");
        text = text.replaceAll("\\)", "");
        text = text.replaceAll("\\[", "");
        text = text.replaceAll("\\]", "");
        return text;
    }

    //生成随机英文字符串
    public static String createRandomStr(int length) {
        String str = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
        Random random = new Random();
        StringBuffer stringBuffer = new StringBuffer();
        for (int i = 0; i < length; i++) {
            int number = random.nextInt(62);
            stringBuffer.append(str.charAt(number));
        }
        return stringBuffer.toString();
    }

    //随机数
    protected long generateRandomNumber(int n) {
        if (n < 1) {
            throw new IllegalArgumentException("随机数位数必须大于0");
        }
        return (long) (Math.random() * 9 * Math.pow(10, n - 1)) + (long) Math.pow(10, n - 1);
    }

    //头像获取
    public static String getAvatar(String url, String email) {
        String avatar = "";
        String qqUrl = "https://thirdqq.qlogo.cn/g?b=qq&nk=";
        String regex = "[1-9][0-9]{8,10}\\@[q][q]\\.[c][o][m]";
        if (email.matches(regex)) {
            String[] qqArr = email.split("@");
            String qq = qqArr[0];
            avatar = qqUrl + qq + "&s=100";
        } else {
            avatar = url + DigestUtils.md5DigestAsHex(email.getBytes());
        }
        return avatar;

    }
    //判断是否有敏感代码
    public Integer haveCode(String text) {
        try {
            if (text.indexOf("<script>") != -1) {
                return 1;
            }
            if (text.indexOf("eval(") != -1) {
                return 1;
            }
            if (text.indexOf("<iframe>") != -1) {
                return 1;
            }
            if (text.indexOf("<frame>") != -1) {
                return 1;
            }
            return 0;
        } catch (Exception e) {
            return 0;
        }

    }
    //生成lv等级
    public static Integer getLv(Integer num) {
        Integer lv = 0;
        try {
            if (num < 10) {
                lv = 1;
            } else if (num >= 10 && num < 50) {
                lv = 2;
            } else if (num >= 50 && num < 200) {
                lv = 3;
            } else if (num >= 200 && num < 500) {
                lv = 4;
            } else if (num >= 500 && num < 1000) {
                lv = 5;
            } else if (num >= 1000 && num < 2000) {
                lv = 6;
            } else if (num >= 2000 && num < 5000) {
                lv = 7;
            } else if (num >= 5000 && num < 10000) {
                lv = 8;
            } else if (num >= 10000) {
                lv = 9;
            }
            return lv;

        } catch (Exception e) {
            return 0;
        }
    }
    public static Integer isVideo(String type){
        String lowerCaseType = type.toLowerCase();
        if (lowerCaseType.equals(".mp4") || lowerCaseType.equals(".avi") || lowerCaseType.equals(".mkv")) {
            return 1; // 是视频
        } else {
            return 0; // 不是视频
        }
    }
    public static Integer isMedia(String type){
        String lowerCaseType = type.toLowerCase();
        if (lowerCaseType.equals(".mp4") || lowerCaseType.equals(".avi") || lowerCaseType.equals(".mkv") || lowerCaseType.equals(".mp3") || lowerCaseType.equals(".wav")) {
            return 1; // 是媒体文件
        } else {
            return 0; // 不是媒体文件
        }
    }
    //获取IP属地
    public String getLocal(String ip) throws IOException {
        ApplicationHome h = new ApplicationHome(getClass());
        File jarF = h.getSource();
        /* 配置文件路径 */
        String dbPath = jarF.getParentFile().toString()+"/ip2region.xdb";
        String region = "";
        Searcher searcher = null;
        try {
            searcher = Searcher.newWithFileOnly(dbPath);
        } catch (IOException e) {
            System.out.printf("failed to create searcher with `%s`: %s\n", dbPath, e);
            return "";
        }
        // 2、查询
        try {
            long sTime = System.nanoTime();
            region = searcher.search(ip);
            long cost = TimeUnit.NANOSECONDS.toMicros((long) (System.nanoTime() - sTime));
            System.out.printf("{region: %s, ioCount: %d, took: %d μs}\n", region, searcher.getIOCount(), cost);
        } catch (Exception e) {
            System.out.printf("failed to search(%s): %s\n", ip, e);
        }

        // 3、关闭资源
        searcher.close();
        return region;

    }


    public Integer getForbidden(String forbidden, String text) {
        Integer isForbidden = 0;

        if (forbidden != null && !forbidden.isEmpty()) {
            if (forbidden.contains(",")) {
                String[] strArray = forbidden.split(",");
                for (String str : strArray) {
                    if (str != null && !str.isEmpty()) {
                        if (text.contains(str)) {
                            isForbidden = 1;
                            break; // 如果匹配一个违禁词就立即停止循环
                        }
                    }
                }
            } else {
                if (text.contains(forbidden) || text.equals(forbidden)) {
                    isForbidden = 1;
                }
            }
        }

        return isForbidden;
    }

    public String encrypt(String plainText) {
        byte[] encodedBytes = Base64.getEncoder().encode(plainText.getBytes());
        return new String(encodedBytes);
    }

    public String decrypt(String encryptedText) {
        byte[] decodedBytes = Base64.getDecoder().decode(encryptedText.getBytes());
        return new String(decodedBytes);
    }

    //从富文本提取存文本
    public String htmlToText(String text) {
        text = text.replaceAll("\\<.*?\\>", "");
        return text;
    }

    /**
     * 利用java原生的摘要实现SHA256加密
     * @param str 加密后的报文
     * @return
     */
    public String getSHA256StrJava(String str){
        MessageDigest messageDigest;
        String encodeStr = "";
        try {
            messageDigest = MessageDigest.getInstance("SHA-256");
            messageDigest.update(str.getBytes("UTF-8"));
            encodeStr = byte2Hex(messageDigest.digest());
        } catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        return encodeStr;
    }
    /**
     * 将byte转为16进制
     * @param bytes
     * @return
     */
    private String byte2Hex(byte[] bytes){
        StringBuffer stringBuffer = new StringBuffer();
        String temp = null;
        for (int i=0;i<bytes.length;i++){
            temp = Integer.toHexString(bytes[i] & 0xFF);
            if (temp.length()==1){
                //1得到一位的进行补0操作
                stringBuffer.append("0");
            }
            stringBuffer.append(temp);
        }
        return stringBuffer.toString();
    }

    // HMAC-SHA256 加密
    public String hmacSHA256(String data, String key) {
        try {
            Mac sha256_HMAC = Mac.getInstance("HmacSHA256");
            SecretKeySpec secret_key = new SecretKeySpec(key.getBytes("UTF-8"), "HmacSHA256");
            sha256_HMAC.init(secret_key);
            byte[] bytes = sha256_HMAC.doFinal(data.getBytes("UTF-8"));
            return Base64.getEncoder().encodeToString(bytes);
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    // HMAC 加密
    public String hmacEncrypt(String data, String key) {
        try {
            return hmacSHA256(data, key);
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    // AES+RSA混合加密
    public String hybridEncrypt(String data) {
        try {
            // 生成AES密钥
            KeyGenerator keyGen = KeyGenerator.getInstance("AES");
            keyGen.init(256);
            SecretKey aesKey = keyGen.generateKey();

            // AES加密数据
            Cipher aesCipher = Cipher.getInstance("AES/GCM/NoPadding");
            byte[] iv = new byte[12];
            new SecureRandom().nextBytes(iv);
            GCMParameterSpec gcmSpec = new GCMParameterSpec(128, iv);
            aesCipher.init(Cipher.ENCRYPT_MODE, aesKey, gcmSpec);
            byte[] encryptedData = aesCipher.doFinal(data.getBytes());

            // RSA加密AES密钥
            KeyPairGenerator keyPairGen = KeyPairGenerator.getInstance("RSA");
            keyPairGen.initialize(2048);
            KeyPair keyPair = keyPairGen.generateKeyPair();
            Cipher rsaCipher = Cipher.getInstance("RSA/ECB/PKCS1Padding");
            rsaCipher.init(Cipher.ENCRYPT_MODE, keyPair.getPublic());
            byte[] encryptedKey = rsaCipher.doFinal(aesKey.getEncoded());

            // 组合加密结果
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            outputStream.write(iv);
            outputStream.write(encryptedKey);
            outputStream.write(encryptedData);

            return Base64.getEncoder().encodeToString(outputStream.toByteArray());
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    public String sha256(String input) {
        try {
            MessageDigest digest = MessageDigest.getInstance("SHA-256");
            byte[] hash = digest.digest(input.getBytes(StandardCharsets.UTF_8));
            StringBuilder hexString = new StringBuilder();
            for (byte b : hash) {
                String hex = Integer.toHexString(0xff & b);
                if (hex.length() == 1) hexString.append('0');
                hexString.append(hex);
            }
            return hexString.toString();
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException(e);
        }
    }
}
