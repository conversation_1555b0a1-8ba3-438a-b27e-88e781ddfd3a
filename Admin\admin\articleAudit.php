<?php
session_start();
?>



<?php
include_once 'Nav.php';
$cid = $_GET['cid'];

$article = "SELECT * FROM typecho_contents WHERE cid='$cid' limit 1";
$resarticle = mysqli_query($connect, $article);
$mod = mysqli_fetch_array($resarticle);

$article2 = "SELECT * FROM typecho_relationships WHERE cid='$cid' limit 1";
$resarticle2 = mysqli_query($connect, $article2);
$mod2 = mysqli_fetch_array($resarticle2);
$mid = $mod2['mid'];

$withdrawals = "SELECT * FROM typecho_metas WHERE mid='$mid' limit 1";
$withdrawalsResult = mysqli_query($connect, $withdrawals);
$mod3 = mysqli_fetch_array($withdrawalsResult);
$category = $mod3['name'];
?>
<div class="row">

    <div class="col-lg-12">
        <div class="card">
            <div class="card-body">
                <h4 class="header-title mb-3 size_18">审核文章</h4>
                    <div class="form-group col-sm-4">
                        <label for="validationCustom01">文章cid</label>
                        <input type="text" class="form-control" id="validationCustom01"
                               name="cid" value="<?php echo $cid ?>" readonly>
                    </div>
                    <div class="form-group col-sm-4">
                        <label for="validationCustom01">发布者UID</label>
                        <input type="text" class="form-control" id="validationCustom01" placeholder="请输入发布者UID"
                               name="uid" value="<?php echo $mod['authorId'] ?>" readonly>
                    </div>
                    <div class="form-group col-sm-4">
                        <label for="validationCustom01">分类</label>
                            <select class="form-control" id="example-select" name="category" readonly>
                                    <option value="<?php echo $mod2['mid']; ?>" selected><?php echo $category ?></option>
                            </select>
                    </div>
                    <div class="form-group mb-3">
                        <label for="validationCustom01">文章标题</label>
                        <input type="text" class="form-control" id="validationCustom01" placeholder="请输入标题"
                               name="articletitle" value="<?php echo $mod['title'] ?>" readonly>
                    </div>
                    <label for="validationCustom01">文章内容 （当前不支持修改）</label>
                    <div id="editor—wrapper">
                            <div id="editor-container"></div>
                             <textarea id="editorContent" name="articletext" style="display: none;"><?php echo $mod['text'] ?></textarea>  
                        </div>
                    <div class="form-group mb-3 text_right">
                        <?php if($mod['isrecommend']=='0'){?>
                            <a class="fabu" href="articleAuditPost.php?cid=<?php echo $cid ?>&status=isrecommend">
                            <button class="btn btn-warning" id="payoutPost" style="margin-right:10px">推荐</button>
                            </a>
                        <?php } else { ?>
                        <a class="fabu" href="articleAuditPost.php?cid=<?php echo $cid ?>&status=unrecommend">
                            <button class="btn btn-danger" id="payoutPost" style="margin-right:10px">取消推荐</button>
                        </a>
                        <?php } ?>
                        <?php if($mod['isswiper']=='0'){?>
                                <a class="fabu" href="articleAuditPost.php?cid=<?php echo $cid ?>&status=isswiper">
                            <button class="btn btn-warning" id="payoutPost" style="margin-right:10px">轮播</button>
                            </a>
                        <?php } else { ?>
                                <a class="fabu" href="articleAuditPost.php?cid=<?php echo $cid ?>&status=unswiper">
                            <button class="btn btn-danger" id="payoutPost" style="margin-right:10px">取消轮播</button>
                        </a>
                        <?php } ?>
                        <?php if($mod['istop']=='0'){?>
                            <a class="fabu" href="articleAuditPost.php?cid=<?php echo $cid ?>&status=istop">
                            <button class="btn btn-warning" id="payoutPost" style="margin-right:10px">置顶</button>
                            </a>
                        <?php } else { ?>
                         <a class="fabu" href="articleAuditPost.php?cid=<?php echo $cid ?>&status=untop">
                            <button class="btn btn-danger" id="payoutPost" style="margin-right:40px">取消置顶</button>
                        </a>
                        <?php } ?>
                        
                        <a class="fabu" onclick="Pass('<?php echo $cid ;?>')">
                            <button class="btn btn-primary" id="payoutPost" style="margin-right:10px">通过</button>
                        </a>
                        <a class="fabu" onclick="Refuse('<?php echo $cid ;?>')">
                            <button class="btn btn-danger" id="payoutPost">驳回</button>
                        </a>
                    </div>

            </div> <!-- end card-body-->
        </div> <!-- end card-->
    </div> <!-- end col-->
</div>


<!--<script src="https://cdn.bootcss.com/jquery/1.11.3/jquery.min.js"></script>-->
<link href="https://unpkg.com/@wangeditor/editor@latest/dist/css/style.css" rel="stylesheet">
<script src="https://unpkg.com/@wangeditor/editor@latest/dist/index.js"></script>
<script>
const { createEditor, createToolbar } = window.wangEditor
const editorConfig = {
    placeholder: '请输入内容...',
    onChange(editor) {
      const html = editor.getHtml();  
    document.getElementById('editorContent').value = html;  
    },  
}
<?php 
$edithOf = true; 
include_once 'editor.php';
?>

<script>
    function Pass(cid) {
        if (confirm('您确认要通过该文章吗？')) {
            location.href = 'articleAuditPost.php?cid=' + cid +'&status=Pass';
        }
    }
    function Refuse(cid) {
        if (confirm('您确认要拒绝该文章吗？')) {
            location.href = 'articleAuditPost.php?cid=' + cid +'&status=Refuse';
        }
    }
    
</script>

<?php
include_once 'Footer.php';
?>

</body>
</html>