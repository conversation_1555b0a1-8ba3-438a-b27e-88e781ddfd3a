package com.StarProApi.dao;

import com.StarProApi.entity.*;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * dao层接口
 * TypechoGptDao
 * <AUTHOR>
 * @date 2024/06/17
 */
@Mapper
public interface TypechoGptDao {

    /**
     * [新增]
     **/
    int insert(TypechoGpt typechoGpt);

    /**
     * [批量新增]
     **/
    int batchInsert(List<TypechoGpt> list);

    /**
     * [更新]
     **/
    int update(TypechoGpt typechoGpt);

    /**
     * [删除]
     **/
    int delete(Object key);

    /**
     * [批量删除]
     **/
    int batchDelete(List<Object> list);

    /**
     * [主键查询]
     **/
    TypechoGpt selectByKey(Object key);

    /**
     * [条件查询]
     **/
    List<TypechoGpt> selectList (TypechoGpt typechoGpt);

    /**
     * [分页条件查询]
     **/
    List<TypechoGpt> selectPage (@Param("typechoGpt") TypechoGpt typechoGpt, @Param("page") Integer page, @Param("pageSize") Integer pageSize, @Param("searchKey") String searchKey, @Param("order") String order);

    /**
     * [总量查询]
     **/
    int total(@Param("typechoGpt") TypechoGpt typechoGpt, @Param("searchKey") String searchKey);
}
