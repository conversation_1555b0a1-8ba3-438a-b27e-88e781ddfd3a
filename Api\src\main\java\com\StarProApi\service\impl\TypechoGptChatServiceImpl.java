package com.StarProApi.service.impl;

import com.StarProApi.entity.*;
import com.StarProApi.common.PageList;
import com.StarProApi.dao.*;
import com.StarProApi.service.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 业务层实现类
 * TypechoGptChatServiceImpl
 * <AUTHOR>
 * @date 2024/06/17
 */
@Service
public class TypechoGptChatServiceImpl implements TypechoGptChatService {

    @Autowired
	TypechoGptChatDao dao;

    @Override
    public int insert(TypechoGptChat typechoGptChat) {
        return dao.insert(typechoGptChat);
    }

    @Override
    public int batchInsert(List<TypechoGptChat> list) {
    	return dao.batchInsert(list);
    }

    @Override
    public int update(TypechoGptChat typechoGptChat) {
    	return dao.update(typechoGptChat);
    }

    @Override
    public int delete(Object key) {
    	return dao.delete(key);
    }

    @Override
    public int batchDelete(List<Object> keys) {
        return dao.batchDelete(keys);
    }

	@Override
	public TypechoGptChat selectByKey(Object key) {
		return dao.selectByKey(key);
	}

	@Override
	public List<TypechoGptChat> selectList(TypechoGptChat typechoGptChat) {
		return dao.selectList(typechoGptChat);
	}

	@Override
	public PageList<TypechoGptChat> selectPage(TypechoGptChat typechoGptChat, Integer offset, Integer pageSize,String order) {
		PageList<TypechoGptChat> pageList = new PageList<>();

		int total = this.total(typechoGptChat);

		Integer totalPage;
		if (total % pageSize != 0) {
			totalPage = (total /pageSize) + 1;
		} else {
			totalPage = total /pageSize;
		}

		int page = (offset - 1) * pageSize;

		List<TypechoGptChat> list = dao.selectPage(typechoGptChat, page, pageSize,order);

		pageList.setList(list);
		pageList.setStartPageNo(offset);
		pageList.setPageSize(pageSize);
		pageList.setTotalCount(total);
		pageList.setTotalPageCount(totalPage);
		return pageList;
	}

	@Override
	public int total(TypechoGptChat typechoGptChat) {
		return dao.total(typechoGptChat);
	}
}