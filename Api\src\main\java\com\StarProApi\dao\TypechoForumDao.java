package com.StarProApi.dao;

import com.StarProApi.entity.*;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * dao层接口
 * TypechoForumDao
 * <AUTHOR>
 * @date 2023/02/20
 */
@Mapper
public interface TypechoForumDao {

    /**
     * [新增]
     **/
    int insert(TypechoForum typechoForum);

    /**
     * [批量新增]
     **/
    int batchInsert(List<TypechoForum> list);

    /**
     * [更新]
     **/
    int update(TypechoForum typechoForum);

    /**
     * [删除]
     **/
    int delete(Object key);

    /**
     * [批量删除]
     **/
    int batchDelete(List<Object> list);

    /**
     * [主键查询]
     **/
    TypechoForum selectByKey(Object key);

    /**
     * [条件查询]
     **/
    List<TypechoForum> selectList (TypechoForum typechoForum);

    /**
     * [分页条件查询]
     **/
    List<TypechoForum> selectPage (@Param("typechoForum") TypechoForum typechoForum, @Param("page") Integer page, @Param("pageSize") Integer pageSize,@Param("order") String order,@Param("searchKey") String searchKey);

    /**
     * [总量查询]
     **/
    int total(@Param("typechoForum") TypechoForum typechoForum, @Param("searchKey") String searchKey);
}
