# MCP Feedback Enhanced 部署指南

## 📋 项目概述

**MCP Feedback Enhanced** 是一个增强版的 MCP（Model Context Protocol）服务器，提供交互式用户反馈功能，支持 Web UI 和桌面应用程序双重界面。

### 🌟 主要特性

- 🌐 **双重界面支持**：Web UI 和桌面应用程序
- 🔧 **智能环境检测**：自动适配 SSH Remote、WSL 环境  
- 📝 **智能工作流程**：提示词管理、自动提交、会话追踪
- 🖼️ **多媒体支持**：支持图片上传和处理
- 🌏 **多语言支持**：中文、英文界面
- 🎯 **AI 集成**：支持 Cursor、Cline、Windsurf、Augment、Trae 等平台

## 🚀 快速部署

### 1. 环境要求

- **Python**: >= 3.11
- **操作系统**: Windows、macOS、Linux
- **包管理器**: uv（推荐）

### 2. 安装步骤

```bash
# 1. 安装 uv 包管理器（如果尚未安装）
pip install uv

# 2. 安装 mcp-feedback-enhanced
uvx mcp-feedback-enhanced@latest version

# 3. 测试安装
uvx mcp-feedback-enhanced@latest test --web    # 测试 Web UI
uvx mcp-feedback-enhanced@latest test --desktop # 测试桌面应用
```

### 3. 配置选择

根据您的使用环境选择合适的配置：

#### 🖥️ 桌面应用模式（推荐）
- **适用场景**: 本地开发环境
- **配置文件**: `mcp-config-desktop.json`
- **特点**: 原生桌面体验，跨平台支持

#### 🌐 Web UI 模式
- **适用场景**: 远程开发、WSL 环境
- **配置文件**: `mcp-config-web.json`  
- **特点**: 轻量级浏览器界面

#### 🔗 SSH Remote 模式
- **适用场景**: SSH 远程开发环境
- **配置文件**: `mcp-config-ssh-remote.json`
- **特点**: 允许远程访问（MCP_WEB_HOST: "0.0.0.0"）

### 4. MCP 服务器配置

将选择的配置文件内容复制到您的 AI 助手的 MCP 配置中：

**Cursor/VS Code**: 
- 打开设置 → 搜索 "MCP" → 编辑配置文件

**其他平台**: 
- 参考各平台的 MCP 配置文档

### 5. 环境变量说明

| 变量 | 用途 | 值 | 默认 |
|------|------|-----|------|
| `MCP_DESKTOP_MODE` | 桌面应用模式 | `true`/`false` | `false` |
| `MCP_WEB_HOST` | Web UI 主机绑定 | IP 地址 | `127.0.0.1` |
| `MCP_WEB_PORT` | Web UI 端口 | `1024-65535` | `8765` |
| `MCP_DEBUG` | 调试模式 | `true`/`false` | `false` |
| `MCP_LANGUAGE` | 界面语言 | `zh-CN`/`zh-TW`/`en` | 自动检测 |

## 🔧 使用指南

### 基本工作流程

1. **AI 调用** → `mcp-feedback-enhanced` 工具
2. **界面启动** → 自动打开桌面应用或浏览器界面
3. **智能交互** → 提示词选择、文字输入、图片上传
4. **即时反馈** → WebSocket 连接即时传递信息给 AI
5. **会话追踪** → 自动记录会话历史与统计

### 快捷键支持

- `Ctrl+Enter`（Windows/Linux）/ `Cmd+Enter`（macOS）：提交反馈
- `Ctrl+V`（Windows/Linux）/ `Cmd+V`（macOS）：粘贴剪贴板图片
- `Ctrl+I`（Windows/Linux）/ `Cmd+I`（macOS）：快速聚焦输入框

### 提示工程设置

为获得最佳效果，在 AI 助手中添加以下规则：

```
# MCP Interactive Feedback 规则
注意在任务完成前根据mcp-feedback-enhanced工具的要求进行工具调用
```

## 🐛 常见问题

### SSH Remote 环境问题

**Q: SSH Remote 环境下浏览器无法访问**
**A**: 使用 SSH Remote 配置（`MCP_WEB_HOST: "0.0.0.0"`），然后在本地浏览器打开：`http://[远程主机IP]:8765`

### 连接问题

**Q: 没有接收到 MCP 反馈**
**A**: WebSocket 连接问题，直接刷新浏览器页面

**Q: MCP 工具无法调用**
**A**: 确认 MCP 工具状态为绿灯，反复开关 MCP 工具等待重连

### 缓存清理

**Q: UV Cache 占用过多磁盘空间**
**A**: 定期清理缓存：`uv cache clean`

## 📞 技术支持

- **GitHub Issues**: https://github.com/Minidoracat/mcp-feedback-enhanced/issues
- **Discord**: https://discord.gg/Gur2V67
- **文档**: https://github.com/Minidoracat/mcp-feedback-enhanced

## 📄 许可证

MIT License - 详见项目 LICENSE 文件

---

**🌟 部署完成后，您就可以在 AI 助手中使用 MCP Feedback Enhanced 进行交互式开发了！**
