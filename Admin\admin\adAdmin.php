<?php
session_start();
?>

<?php
include_once 'connect.php';
$sql = "select * from typecho_ads order by aid desc";
$contents = mysqli_query($connect, $sql);
?>

<?php
include_once 'Nav.php';
?>

<link href="/admin/assets/css/vendor/dataTables.bootstrap4.css" rel="stylesheet" type="text/css"/>
<link href="/admin/assets/css/vendor/responsive.bootstrap4.css" rel="stylesheet" type="text/css"/>
<link href="/admin/assets/css/vendor/buttons.bootstrap4.css" rel="stylesheet" type="text/css"/>
<link href="/admin/assets/css/vendor/select.bootstrap4.css" rel="stylesheet" type="text/css"/>
<!-- third party css end -->


<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <h4 class="header-title mb-3">广告管理<a class="fabu" href="adAdd.php">
                        <button type="button" class="btn btn-success2 btn-sm btn-rounded right_10">
                            <i class="dripicons-upload"></i> 发布广告
                        </button>
                    </a></h4>
                <table id="basic-textlong" class="table dt-responsive nowrap" width="100%">
                    <thead>
                    <tr>
                        <th>aid</th>
                        <th>标题</th>
                        <th>类型</th>
                        <th>发布时间</th>
                        <th>过期时间</th>
                        <th>发布人</th>
                        <th>状态</th>
                        <th style="width: 125px;">操作</th>
                    </tr>
                    </thead>

                    <tbody>
                    <?php
                    while ($articledata = mysqli_fetch_array($contents)) {
                        ?>
                        <tr>
                            <td><?php echo $articledata['aid'] ?></td>
                            <td>
                                <?php echo $articledata['name'] ?>
                            </td>
                             <td>	
                                <h6>
                                    <?php if ($articledata['type']== '0') { ?><span class="badge badge-info-lighten">推流</span><?php } else if ($articledata['type']== '1') { ?><span class="badge badge-info-lighten">横幅</span><?php } else if ($articledata['type']== '2') { ?><span class="badge badge-info-lighten">启动图</span><?php } else { ?><span class="badge badge-info-lighten">轮播图</span><?php } ?>
                                </h6>
                            </td>
                            <td>
                                <small class="text-muted"><?php 
                                $timestamp = $articledata['created'];
                                $date = date("Y-m-d H:i:s", $timestamp);
                                echo $date?></small>
                                
                            </td>
                            <td>
                                <small class="text-muted"><?php 
                                $timestamp = $articledata['close'];
                                $date = date("Y-m-d H:i:s", $timestamp);
                                echo $date?></small>
                                
                            </td>
                            <td><small class="text-muted">UID:<?php echo $articledata['uid'] ?></small></td>
                           <td>
                                <h5>
                                    <?php if ($articledata['status']== '1') { ?><span class="badge badge-success-lighten">已公开</span><?php } else if ($articledata['status']== '0') { ?><span class="badge badge-warning-lighten">待审核</span><?php } else { ?><span class="badge badge-warning-lighten">已到期</span><?php }?>
                                </h5>
                            </td>
                            <td>
                                 <?php if ($articledata['status']== '0') { ?>
                                <a href="adAudit.php?aid=<?php echo $articledata['aid']; ?>">
                                <button style="white-space: nowrap;" type="button"
                                        class="btn btn-warning btn-rounded">
                                    <i class="dripicons-inbox"></i> 审核
                                </button>
                                </a>
                                <?php } ?>
                                
                                <a href="adEdit.php?aid=<?php echo $articledata['aid']; ?>">
                                <button style="white-space: nowrap;" type="button"
                                        class="btn btn-info btn-rounded">
                                    <i class="dripicons-document-edit"></i> 编辑
                                </button>
                                </a>
                                <a href="javascript:del(<?php echo $articledata['aid']; ?>);">
                                    <button style="white-space: nowrap;" type="button"
                                            class="btn btn-danger btn-rounded">
                                        <i class="mdi mdi-delete-empty mr-1"></i>删除
                                    </button>
                                </a>
                            </td>
                        </tr>
                        <?php
                    }
                    ?>
                    </tbody>
                </table>

            </div>  
        </div>  
    </div> 
</div>


<script>
    function del(aid) {
        if (confirm('您确认要删除aid为' + aid + '的广告吗？')) {
            location.href = 'adAuditPost.php?aid=' + aid +'&status=Del';
        }
    }
</script>


<?php
include_once 'Footer.php';
?>

<!-- third party js -->
<script src="/admin/assets/js/vendor/jquery.dataTables.min.js"></script>
<script src="/admin/assets/js/vendor/dataTables.bootstrap4.js"></script>
<script src="/admin/assets/js/vendor/dataTables.responsive.min.js"></script>
<script src="/admin/assets/js/vendor/responsive.bootstrap4.min.js"></script>
<script src="/admin/assets/js/vendor/dataTables.buttons.min.js"></script>
<script src="/admin/assets/js/vendor/buttons.bootstrap4.min.js"></script>
<script src="/admin/assets/js/vendor/buttons.html5.min.js"></script>
<script src="/admin/assets/js/vendor/buttons.flash.min.js"></script>
<script src="/admin/assets/js/vendor/buttons.print.min.js"></script>
<script src="/admin/assets/js/vendor/dataTables.keyTable.min.js"></script>
<script src="/admin/assets/js/vendor/dataTables.select.min.js"></script>
<!-- third party js ends -->
<!-- demo app -->
<script src="/admin/assets/js/pages/demo.datatable-init.js"></script>
<!-- end demo js-->


</body>
</html>