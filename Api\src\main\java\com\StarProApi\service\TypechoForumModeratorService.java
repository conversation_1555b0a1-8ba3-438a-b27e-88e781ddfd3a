package com.StarProApi.service;

import java.util.Map;
import java.util.List;
import com.StarProApi.entity.*;
import com.StarProApi.common.PageList;

/**
 * 业务层
 * TypechoForumModeratorService
 * <AUTHOR>
 * @date 2023/02/20
 */
public interface TypechoForumModeratorService {

    /**
     * [新增]
     **/
    int insert(TypechoForumModerator typechoForumModerator);

    /**
     * [批量新增]
     **/
    int batchInsert(List<TypechoForumModerator> list);

    /**
     * [更新]
     **/
    int update(TypechoForumModerator typechoForumModerator);

    /**
     * [删除]
     **/
    int delete(Object key);

    /**
     * [批量删除]
     **/
    int batchDelete(List<Object> keys);

    /**
     * [主键查询]
     **/
    TypechoForumModerator selectByKey(Object key);

    /**
     * [条件查询]
     **/
    List<TypechoForumModerator> selectList (TypechoForumModerator typechoForumModerator);

    /**
     * [分页条件查询]
     **/
    PageList<TypechoForumModerator> selectPage (TypechoForumModerator typechoForumModerator, Integer page, Integer pageSize);

    /**
     * [总量查询]
     **/
    int total(TypechoForumModerator typechoForumModerator);
}
