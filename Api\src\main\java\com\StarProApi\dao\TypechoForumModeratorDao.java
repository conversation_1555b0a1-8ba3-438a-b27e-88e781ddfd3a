package com.StarProApi.dao;

import com.StarProApi.entity.*;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * dao层接口
 * TypechoForumModeratorDao
 * <AUTHOR>
 * @date 2023/02/20
 */
@Mapper
public interface TypechoForumModeratorDao {

    /**
     * [新增]
     **/
    int insert(TypechoForumModerator typechoForumModerator);

    /**
     * [批量新增]
     **/
    int batchInsert(List<TypechoForumModerator> list);

    /**
     * [更新]
     **/
    int update(TypechoForumModerator typechoForumModerator);

    /**
     * [删除]
     **/
    int delete(Object key);

    /**
     * [批量删除]
     **/
    int batchDelete(List<Object> list);

    /**
     * [主键查询]
     **/
    TypechoForumModerator selectByKey(Object key);

    /**
     * [条件查询]
     **/
    List<TypechoForumModerator> selectList (TypechoForumModerator typechoForumModerator);

    /**
     * [分页条件查询]
     **/
    List<TypechoForumModerator> selectPage (@Param("typechoForumModerator") TypechoForumModerator typechoForumModerator, @Param("page") Integer page, @Param("pageSize") Integer pageSize);

    /**
     * [总量查询]
     **/
    int total(TypechoForumModerator typechoForumModerator);
}
