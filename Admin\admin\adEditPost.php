<?php
session_start();
?>
<?php
include_once 'connect.php';
$aid = $_POST['aid'];
$uid = $_POST['uid'];
$type = $_POST['type'];
$name = $_POST['name'];
$intro = $_POST['intro'];
$urltype = $_POST['urltype'];
$url = $_POST['url'];
$img = $_POST['img'];
$close = strtotime($_POST['close']);

$file = $_SERVER['PHP_SELF'];


if (isset($_SESSION['loginadmin']) && $_SESSION['loginadmin'] <> '') {
    $charu = "UPDATE typecho_ads SET type = '$type' , close = '$close' , img = '$img', url = '$url', urltype = '$urltype' , intro = '$intro', name = '$name', uid = '$uid' WHERE aid = '$aid'";
    $result = mysqli_query($connect, $charu);
    $redisKeys = $connectRedis->keys('starapi_*');
        foreach ($redisKeys as $redisKey) {
            $connectRedis->del($redisKey);
        }
    if ($result) {
            echo "<script>alert('修改成功');location.href = 'adAdmin.php';</script>";
        } else {
            echo "<script>alert('修改失败');location.href = 'adAdmin.php';</script>";   
        }
} else {
    echo "<script>alert('非法操作，行为已记录');location.href = 'warning.php?route=$file';</script>";
}
