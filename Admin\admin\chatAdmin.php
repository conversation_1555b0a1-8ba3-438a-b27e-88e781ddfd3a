<?php
session_start();
?>

<?php
include_once 'connect.php';
$sql = "SELECT * FROM typecho_chat WHERE type = 0 ORDER BY id DESC;";
$contents = mysqli_query($connect, $sql);
?>

<?php
include_once 'Nav.php';
?>

<link href="/admin/assets/css/vendor/dataTables.bootstrap4.css" rel="stylesheet" type="text/css"/>
<link href="/admin/assets/css/vendor/responsive.bootstrap4.css" rel="stylesheet" type="text/css"/>
<link href="/admin/assets/css/vendor/buttons.bootstrap4.css" rel="stylesheet" type="text/css"/>
<link href="/admin/assets/css/vendor/select.bootstrap4.css" rel="stylesheet" type="text/css"/>
<!-- third party css end -->


<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <h4 class="header-title mb-3">私聊管理</h4>
                <table id="basic-chat" class="table dt-responsive nowrap" width="100%">
                    <thead>
                    <tr>
                        <th>id</th>
                        <th>UID</th>
                        <th>发起时间</th>
                        <th>最后聊天</th>
                        <th>状态</th>
                        <th style="width: 125px;">操作</th>
                    </tr>
                    </thead>

                    <tbody>
                    <?php
                    while ($articledata = mysqli_fetch_array($contents)) {
                        ?>
                        <tr>
                            <td><?php echo $articledata['id'] ?></td>
                            <td>
                                <?php echo $articledata['uid'] ?>和<?php echo $articledata['toid'] ?>
                            </td>
                            <td>
                                <small class="text-muted"><?php 
                                $timestamp = $articledata['created'];
                                $date = date("Y-m-d H:i:s", $timestamp);
                                echo $date?></small>
                                
                            </td>
                            <td>
                                <small class="text-muted"><?php 
                                $timestamp = $articledata['lastTime'];
                                $date = date("Y-m-d H:i:s", $timestamp);
                                echo $date?></small>
                                
                            </td>
                           <td>
                                <h5>
                                    <?php if ($articledata['ban']!= '0') { ?><span class="badge badge-warning-lighten">禁言</span><?php } else { ?><span class="badge badge-success-lighten">正常</span><?php }?>
                                </h5>
                            </td>
                            <td>
                                <?php if ($articledata['ban']!= '0') { ?>
                                <a href="chatPost.php?id=<?php echo $articledata['id']; ?>&status=Noban&uid=<?php echo $articledata['uid']; ?>">
                                <button style="white-space: nowrap;" type="button"
                                        class="btn btn-warning btn-rounded">
                                    <i class="dripicons-lock-open"></i> 解除
                                </button>
                                </a>
                                <?php } else {?>
                                <a href="chatPost.php?id=<?php echo $articledata['id']; ?>&status=Ban&uid=<?php echo $articledata['uid']; ?>">
                                <button style="white-space: nowrap;" type="button"
                                        class="btn btn-warning btn-rounded">
                                    <i class="dripicons-lock"></i> 禁言
                                </button>
                                </a>
                                  <?php }?>
                                <a href="msgAdmin.php?id=<?php echo $articledata['id']; ?>&type=0&uid=<?php echo $articledata['uid']; ?>&toid=<?php echo $articledata['toid']; ?>">
                                <button style="white-space: nowrap;" type="button"
                                        class="btn btn-info btn-rounded">
                                    <i class="dripicons-network-3"></i> 管理
                                </button>
                                </a>
                                <a href="javascript:del(<?php echo $articledata['id']; ?>);">
                                    <button style="white-space: nowrap;" type="button"
                                            class="btn btn-danger btn-rounded">
                                        <i class="mdi mdi-delete-empty mr-1"></i>删除
                                    </button>
                                </a>
                            </td>
                        </tr>
                        <?php
                    }
                    ?>
                    </tbody>
                </table>

            </div>  
        </div>  
    </div> 
</div>


<script>
    function del(id) {
        if (confirm('您确认要删除id为' + id + '的私聊吗？')) {
            location.href = 'chatPost.php?id=' + id +'&status=Del';
        }
    }
</script>


<?php
include_once 'Footer.php';
?>

<!-- third party js -->
<script src="/admin/assets/js/vendor/jquery.dataTables.min.js"></script>
<script src="/admin/assets/js/vendor/dataTables.bootstrap4.js"></script>
<script src="/admin/assets/js/vendor/dataTables.responsive.min.js"></script>
<script src="/admin/assets/js/vendor/responsive.bootstrap4.min.js"></script>
<script src="/admin/assets/js/vendor/dataTables.buttons.min.js"></script>
<script src="/admin/assets/js/vendor/buttons.bootstrap4.min.js"></script>
<script src="/admin/assets/js/vendor/buttons.html5.min.js"></script>
<script src="/admin/assets/js/vendor/buttons.flash.min.js"></script>
<script src="/admin/assets/js/vendor/buttons.print.min.js"></script>
<script src="/admin/assets/js/vendor/dataTables.keyTable.min.js"></script>
<script src="/admin/assets/js/vendor/dataTables.select.min.js"></script>
<!-- third party js ends -->
<!-- demo app -->
<script src="/admin/assets/js/pages/demo.datatable-init.js"></script>
<!-- end demo js-->


</body>
</html>