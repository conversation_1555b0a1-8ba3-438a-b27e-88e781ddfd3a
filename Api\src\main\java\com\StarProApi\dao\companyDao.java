package com.StarProApi.dao;

import com.StarProApi.entity.identifyCompany;
import com.StarProApi.entity.identifyConsumer;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface companyDao {

    List<identifyCompany> queryInfo(@Param("identifyCompany")identifyCompany dto,@Param("page") int page,@Param("pageSize") int pageSize);

    int insert(identifyCompany dto);

    Integer remove(String key);

    Integer update(identifyCompany dto);

    List<identifyCompany> queryAll();

    /**
     * [主键查询]
     **/
    identifyCompany selectByKey(Object uid);
}
