<?php
session_start();
$file = $_SERVER['PHP_SELF'];
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    include_once 'connect.php';
    
    $kuaijie = isset($_POST['kuaijie']) ? $_POST['kuaijie'] : 0;
    $radiusBoxStyle = $_POST['radiusBoxStyle'];
    $radiusStyle = $_POST['radiusStyle'];
    $fatherTitle = isset($_POST['fatherTitle']) ? $_POST['fatherTitle'] : 0;
    $swiperStyle2 = $_POST['swiperStyle2'];
    $swiperType = $_POST['swiperType'];
    $recommendOf = isset($_POST['recommendOf']) ? $_POST['recommendOf'] : 0;
    if (isset($_SESSION['loginadmin']) && $_SESSION['loginadmin'] <> '') {
        // 写入数据库
        $query = "UPDATE Sy_pages SET kuaijie=?, radiusBoxStyle=?, radiusStyle=?, fatherTitle=?, swiperStyle2=?, swiperType=?, recommendOf=?";
        $stmt = $connect->prepare($query);
        $redisKeys = $connectRedis->keys('starapi_*');
            foreach ($redisKeys as $redisKey) {
                $connectRedis->del($redisKey);
            }
        if ($stmt) {
            $stmt->bind_param("ississi", $kuaijie, $radiusBoxStyle, $radiusStyle, $fatherTitle, $swiperStyle2, $swiperType, $recommendOf);

            if ($stmt->execute()) {
                echo "<script>alert('更改成功');location.href = 'circlePage.php';</script>";
            } else {
                echo "<script>alert('更改失败');location.href = 'circlePage.php';</script>";
            }
            $stmt->close();
        } else {
            echo "<script>alert('无法连接数据库');location.href = 'circlePage.php';</script>";
        }
    } else {
        echo "<script>alert('非法操作，行为已记录');location.href = 'warning.php?route=$file';</script>";
    }
} else {
    
    echo "<script>alert('非法操作，行为已记录');location.href = 'warning.php?route=$file';</script>";
}
?>
