package com.StarProApi.web;

import com.StarProApi.annotation.LoginRequired;
import com.StarProApi.common.*;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.StarProApi.entity.*;
import com.StarProApi.service.*;
import net.dreamlu.mica.xss.core.XssCleanIgnore;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * 圈子模块
 * TypechoMetasController
 * <AUTHOR>
 * @date 2021/11/29
 */

@Controller
@RequestMapping(value = "/SProForum")
public class TypechoForumController {
    @Autowired
    TypechoForumService service;

    @Autowired
    private SecurityService securityService;

    @Autowired
    private TypechoApiconfigService apiconfigService;

    @Autowired
    private TypechoCommentsService commentsService;

    @Autowired
    private TypechoViolationService violationService;

    @Autowired
    private TypechoUserlogService userlogService;

    @Autowired
    private TypechoUsersService usersService;

    @Autowired
    private TypechoSpaceService spaceService;

    @Autowired
    private TypechoFanService fanService;

    @Autowired
    private TypechoForumCommentService forumCommentService;


    @Autowired
    private TypechoForumSectionService forumSectionService;

    @Autowired
    private TypechoForumModeratorService forumModeratorService;

    @Autowired
    private TypechoInboxService inboxService;

    @Autowired
    private TypechoUserlogService userlogservice;

    @Autowired
    private TypechoPaylogService paylogService;

    @Autowired
    private TypechoShopService shopService;

    @Autowired
    private PushService pushService;


    @Value("${web.prefix}")
    private String dataprefix;

    @Autowired
    private RedisTemplate redisTemplate;

    @Autowired
    private JdbcTemplate jdbcTemplate;

    RedisHelp redisHelp =new RedisHelp();
    ResultAll Result = new ResultAll();
    UserStatus UStatus = new UserStatus();
    baseFull baseFull = new baseFull();
    EditFile editFile = new EditFile();

    @Value("${mybatis.configuration.variables.prefix}")
    private String prefix;

    /**
     * 发布帖子
     */
    @RequestMapping(value = "/post")
    @XssCleanIgnore
    @ResponseBody
    @LoginRequired(purview = "0")
    public String post (@RequestParam(value = "title", required = false, defaultValue = "") String  title,
                            @RequestParam(value = "section", required = false, defaultValue = "0") Integer  section,
                            @RequestParam(value = "text", required = false, defaultValue = "") String  text,
                            @RequestParam(value = "token", required = false) String  token,
                            @RequestParam(value = "isSpace", required = false, defaultValue = "0") Integer  isSpace,
                            @RequestParam(value = "isMd", required = false, defaultValue = "1") Integer  isMd,
                            @RequestParam(value = "isDraft", required = false, defaultValue = "0") Integer  isDraft,
                            @RequestParam(value = "verifyCode", required = false) String verifyCode,
                            @RequestParam(value = "isPaid", required = false, defaultValue = "0") Integer  isPaid,
                            @RequestParam(value = "shopPice", required = false) Integer  shopPice,
                            @RequestParam(value = "shopText", required = false) String  shopText,
                            @RequestParam(value = "shopDiscount", required = false, defaultValue = "1.0") String  shopDiscount,
                            HttpServletRequest request) {
        try{
            String  ip = baseFull.getIpAddr(request);
            if(section < 1||text==""||title==""){
                return Result.getResultJson(0,"参数错误",null);
            }
            if(title.length()<4){
                return Result.getResultJson(0,"帖子标题长度不能少于5",null);
            }
            if(text.length()<20){
                return Result.getResultJson(0,"帖子内容长度不能少于20",null);
            }
            Map map =redisHelp.getMapValue(this.dataprefix+"_"+"userInfo"+token,redisTemplate);
            Integer uid =Integer.parseInt(map.get("uid").toString());

            String isSilence = redisHelp.getRedis(this.dataprefix+"_"+uid+"_silence",redisTemplate);
            if(isSilence!=null){
                return Result.getResultJson(0,"你的操作太频繁了，请稍后再试",null);
            }
            TypechoApiconfig apiconfig = UStatus.getConfig(this.dataprefix,apiconfigService,redisTemplate);
            if(apiconfig.getVerifyLevel()>1){
                if (StringUtils.isEmpty(verifyCode)) {
                    return Result.getResultJson(0,"图片验证码不能为空",null);
                }
                String kaptchaCode = redisHelp.getRedis(this.dataprefix+"_"+ip+"_verifyCode",redisTemplate);
                if (StringUtils.isEmpty(kaptchaCode) || !verifyCode.equals(kaptchaCode)) {
                    return Result.getResultJson(0,"图片验证码错误",null);
                }
            }
            if(apiconfig.getBanRobots().equals(1)) {
                //登录情况下，刷数据攻击拦截
                String isRepeated = redisHelp.getRedis(this.dataprefix+"_"+uid+"_isForumPost",redisTemplate);
                if(isRepeated==null){
                    redisHelp.setRedis(this.dataprefix+"_"+uid+"_isForumPost","1",4,redisTemplate);
                }else{
                    Integer frequency = Integer.parseInt(isRepeated) + 1;
                    if(frequency==4){
                        securityService.safetyMessage("用户ID："+uid+"，在帖子发布接口疑似存在攻击行为，请及时确认处理。","system");
                        redisHelp.setRedis(this.dataprefix+"_"+uid+"_silence","1",apiconfig.getSilenceTime(),redisTemplate);
                        return Result.getResultJson(0,"你的操作过于频繁，已被禁言十分钟！",null);
                    }else{
                        redisHelp.setRedis(this.dataprefix+"_"+uid+"_isForumPost",frequency.toString(),5,redisTemplate);
                    }
                    return Result.getResultJson(0,"你的操作太频繁了",null);
                }
            }


            //普通用户最大发贴限制
            Map userMap =redisHelp.getMapValue(this.dataprefix+"_"+"userInfo"+token,redisTemplate);
            String group = userMap.get("group").toString();
            if(!group.equals("administrator")&&!group.equals("editor")){
                String spaceNum = redisHelp.getRedis(this.dataprefix+"_"+uid+"_forumNum",redisTemplate);
                if(spaceNum==null){
                    redisHelp.setRedis(this.dataprefix+"_"+uid+"_forumNum","1",86400,redisTemplate);
                }else{
                    Integer space_Num = Integer.parseInt(spaceNum) + 1;
                    if(space_Num > apiconfig.getPostMax()){
                        return Result.getResultJson(0,"你已超过最大发布数量限制，请您24小时后再操作",null);
                    }else{
                        redisHelp.setRedis(this.dataprefix+"_"+uid+"_forumNum",space_Num.toString(),86400,redisTemplate);
                    }
                }
            }
            //限制结束



            //违禁词拦截
            String forbidden = apiconfig.getForbidden();
            Integer intercept = 0;
            Integer isForbidden = baseFull.getForbidden(forbidden,text);
            if(isForbidden.equals(1)){
                intercept = 1;
            }
            if(intercept.equals(1)){
                //以十分钟为检测周期，违禁一次刷新一次，等于4次则禁言
                String isIntercept = redisHelp.getRedis(this.dataprefix+"_"+uid+"_isIntercept",redisTemplate);
                if(isIntercept==null){
                    redisHelp.setRedis(this.dataprefix+"_"+uid+"_isIntercept","1",600,redisTemplate);
                }else{
                    Integer frequency = Integer.parseInt(isIntercept) + 1;
                    if(frequency==4){
                        securityService.safetyMessage("用户ID："+uid+"，在帖子发布接口多次触发违禁，请及时确认处理。","system");
                        redisHelp.setRedis(this.dataprefix+"_"+uid+"_silence","1",apiconfig.getInterceptTime(),redisTemplate);
                        return Result.getResultJson(0,"你已多次发送违禁词，被禁言一小时！",null);
                    }else{
                        redisHelp.setRedis(this.dataprefix+"_"+uid+"_isIntercept",frequency.toString(),600,redisTemplate);
                    }

                }
                return Result.getResultJson(0,"内容存在违禁词",null);
            }
            //违禁词拦截结束
            Long date = System.currentTimeMillis();
            String created = String.valueOf(date).substring(0,10);
            //检测用户是否被封禁
            TypechoViolation violation = new TypechoViolation();
            violation.setUid(uid);
            violation.setType("moderator");
            violation.setValue(section);
            List<TypechoViolation> violationList = violationService.selectList(violation);
            if(violationList.size()>0){
                violation = violationList.get(0);
                Integer bantime = violation.getHandler();
                Integer curtime = Integer.parseInt(String.valueOf(date).substring(0,10));
                if(bantime > curtime){
                    return Result.getResultJson(0, "你已被当前版块封禁，请耐心等待解封。", null);
                }
            }
            //检测版块发帖要求权限
            TypechoForumSection sectionInfo = forumSectionService.selectByKey(section);
            if(sectionInfo==null){
                return Result.getResultJson(0,"版块不存在",null);
            }
            Integer sectionPurview = sectionInfo.getRestrictKey();
            //获取用户权限
            Integer purview = 0;
            TypechoForumModerator isModerator = new TypechoForumModerator();
            isModerator.setSectionId(section);
            isModerator.setUid(uid);
            List<TypechoForumModerator> moderatorList = forumModeratorService.selectList(isModerator);
            if(moderatorList.size()>0){
                purview = moderatorList.get(0).getPurview();
            }
            if(!group.equals("administrator")&&!group.equals("editor")){
                if(sectionPurview > purview){
                    return Result.getResultJson(0,"您无权限在此版块发帖",null);
                }
            }
            //权限检查结束
            //实名认证拦截
            if(apiconfig.getIdentifysmPost().equals(1)){
                if(UStatus.isIdentifysm(uid,prefix,jdbcTemplate).equals(0)){
                    return Result.getResultJson(0,"请先完成实名认证",null);
                }
            }
            //蓝V认证拦截
            if(apiconfig.getIdentifylvPost().equals(1)){
                if(UStatus.isIdentifylv(uid,prefix,jdbcTemplate).equals(0)){
                    return Result.getResultJson(0,"请先完成蓝V认证",null);
                }
            }
            Integer typeid = sectionInfo.getParent();


            text = text.replace("||rn||","\r\n");

            TypechoForum forum = new TypechoForum();
            forum.setText(text);
            forum.setTitle(title);
            forum.setCreated(Integer.parseInt(created));
            forum.setModified(Integer.parseInt(created));
            forum.setReplyTime(Integer.parseInt(created));
            forum.setAuthorId(uid);
            forum.setSection(section);
            forum.setTypeid(typeid);
            forum.setIsMd(isMd);
            //腾讯云内容违规检测
            if(apiconfig.getCmsSwitch().equals(1)||apiconfig.getCmsSwitch().equals(3)){
                try{
                    String setTitle = baseFull.encrypt(title);
                    Map violationData = securityService.textViolation(setTitle);
                    String Suggestion = violationData.get("Suggestion").toString();
                    if(Suggestion.equals("Block")){
                        return Result.getResultJson(0,"内容涉及违规，请检查后重新提交！",null);
                    }
                }catch (Exception e){
                    e.printStackTrace();
                }

            }

            String addTips = "";
            if(isDraft.equals(1)){
                forum.setStatus(-1);
                addTips = "已保存为草稿";
            }else {
                if (apiconfig.getForumAudit().equals(1)) {
                    forum.setStatus(0);
                } else {
                    forum.setStatus(1);

                }
                //腾讯云内容违规检测
                if(apiconfig.getCmsSwitch().equals(1)||apiconfig.getCmsSwitch().equals(3)){
                    try{
                        String setText = baseFull.htmlToText(text);
                        setText = baseFull.encrypt(setText);
                        Map violationData = securityService.textViolation(setText);
                        String Suggestion = violationData.get("Suggestion").toString();
                        if(Suggestion.equals("Block")){
                            return Result.getResultJson(0,"内容涉及违规，请检查后重新提交！",null);
                        }
                        if(Suggestion.equals("Review")){
                            forum.setStatus(0);
                        }
                    }catch (Exception e){
                        e.printStackTrace();
                    }
                }
            }
            //添加付费阅读
            if (isPaid.equals(1)) {
                TypechoShop shop = new TypechoShop();
                shop.setValue(shopText);
                shop.setUid(uid);
                shop.setVipDiscount(shopDiscount);
                shop.setIsView(0);
                shop.setCreated(Integer.parseInt(created));
                shop.setNum(-1);
                shop.setStatus(1);
                shop.setPrice(shopPice);
                shop.setType(4);
                shop.setIsMd(isMd);
                shopService.insert(shop);
                forum.setSid(shop.getId());
            }
            int rows = service.insert(forum);
            if(isDraft.equals(0)){
                if(isSpace.equals(1)){
                    //判断用户经验值
                    Integer spaceMinExp = apiconfig.getSpaceMinExp();
                    TypechoUsers curUser = usersService.selectByKey(uid);
                    Integer Exp = curUser.getExperience();
                    if(Exp < spaceMinExp){
                        return Result.getResultJson(0,"发布动态最低要求经验值为"+spaceMinExp+",你当前经验值"+Exp,null);
                    }
                    TypechoSpace space = new TypechoSpace();
                    space.setType(6);
                    space.setText("在版块["+sectionInfo.getName()+"]发布了新帖子");
                    space.setCreated(Integer.parseInt(created));
                    space.setModified(Integer.parseInt(created));
                    space.setUid(uid);
                    space.setToid(forum.getId());
                    spaceService.insert(space);
                }
                //修改用户最新发布时间
                TypechoUsers updateUser = new TypechoUsers();
                updateUser.setUid(uid);
                updateUser.setIp(ip);
                updateUser.setPosttime(Integer.parseInt(created));
                updateUser.setLocal(baseFull.getLocal(ip));
                if(apiconfig.getForumAudit().equals(0)){
                    //如果无需审核，则立即增加经验
                    Integer postExp = apiconfig.getPostExp();
                    if(postExp>0){
                        //生成操作记录
                        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
                        String curtime = sdf.format(new Date(date));
                        TypechoUserlog userlog = new TypechoUserlog();
                        userlog.setUid(uid);
                        //cid用于存放真实时间
                        userlog.setCid(Integer.parseInt(curtime));
                        userlog.setType("postExp");
                        Integer size = userlogService.total(userlog);
                        //只有前三次发布内容获得经验
                        if(size < 3){
                            userlog.setNum(postExp);
                            userlog.setCreated(Integer.parseInt(created));
                            userlogService.insert(userlog);
                            //修改用户资产
                            TypechoUsers oldUser = usersService.selectByKey(uid);
                            Integer experience = oldUser.getExperience();
                            experience = experience + postExp;
                            updateUser.setExperience(experience);
                        }
                    }
                }
                usersService.update(updateUser);

                editFile.setLog("用户"+uid+"发布了新帖子。");

                if(apiconfig.getForumAudit().equals(1)){
                    addTips = "请等待管理员审核";
                }
            }

            JSONObject response = new JSONObject();
            response.put("code" , rows);
            response.put("msg"  , rows > 0 ? "发布成功"+addTips : "发布失败");
            //清理列表reids缓存
            redisHelp.deleteKeysWithPattern("*"+this.dataprefix+"_postList_1*",redisTemplate,this.dataprefix);
            return response.toString();
        }catch (Exception e){
            e.printStackTrace();
            return Result.getResultJson(0,"接口请求异常，请联系管理员",null);
        }
    }
    /**
     * 编辑帖子
     */
    @RequestMapping(value = "/edit")
    @XssCleanIgnore
    @ResponseBody
    @LoginRequired(purview = "0")
    public String edit (@RequestParam(value = "id", required = false, defaultValue = "") Integer  id,
                            @RequestParam(value = "title", required = false, defaultValue = "") String  title,
                            @RequestParam(value = "text", required = false, defaultValue = "") String  text,
                            @RequestParam(value = "token", required = false) String  token,
                            @RequestParam(value = "isDraft", required = false, defaultValue = "0") Integer  isDraft,
                            @RequestParam(value = "isMd", required = false, defaultValue = "0") Integer  isMd,
                            @RequestParam(value = "isPaid", required = false, defaultValue = "0") Integer  isPaid,
                            @RequestParam(value = "shopPice", required = false) Integer  shopPice,
                            @RequestParam(value = "shopText", required = false) String  shopText,
                            @RequestParam(value = "shopDiscount", required = false, defaultValue = "1.0") String  shopDiscount) {
        try{
            if(title.length()<4){
                return Result.getResultJson(0,"帖子标题长度不能少于5",null);
            }
            if(text.length()<20){
                return Result.getResultJson(0,"帖子内容长度不能少于20",null);
            }
            Map map =redisHelp.getMapValue(this.dataprefix+"_"+"userInfo"+token,redisTemplate);
            Integer uid  = Integer.parseInt(map.get("uid").toString());
            String group = map.get("group").toString();
            TypechoForum forum = service.selectByKey(id);
            if(forum==null){
                return Result.getResultJson(0,"帖子不存在",null);
            }
            Integer sectionId = forum.getSection();
            boolean isAudit = false;
            TypechoForumModerator moderator = new TypechoForumModerator();
            if(!group.equals("administrator")&&!group.equals("editor")){
                Integer authorId = forum.getAuthorId();
                if(authorId.equals(uid)){
                    moderator.setSectionId(sectionId);
                    moderator.setUid(uid);
                    List<TypechoForumModerator> moderatorList = forumModeratorService.selectList(moderator);
                    if(moderatorList.size() > 0){
                        moderator = moderatorList.get(0);
                        if(moderator.getPurview() < 1){
                            isAudit = true;
                        } else {
                            isAudit = false;
                        }
                    } else {
                        isAudit = true;
                    }
                } else {
                    moderator.setSectionId(sectionId);
                    moderator.setUid(uid);
                    List<TypechoForumModerator> moderatorList = forumModeratorService.selectList(moderator);
                    if(moderatorList.size() < 1){
                        return Result.getResultJson(0,"你没有操作权限",null);
                    } else {
                        moderator = moderatorList.get(0);
                        if(moderator.getPurview() < 3){
                            return Result.getResultJson(0,"你没有操作权限",null);
                        }
                    }
                }

            }

            String isSilence = redisHelp.getRedis(this.dataprefix+"_"+uid+"_silence",redisTemplate);
            if(isSilence!=null){
                return Result.getResultJson(0,"你的操作太频繁了，请稍后再试",null);
            }
            //违禁词拦截
            TypechoApiconfig apiconfig = UStatus.getConfig(this.dataprefix,apiconfigService,redisTemplate);
            String forbidden = apiconfig.getForbidden();
            Integer intercept = 0;
            Integer isForbidden = baseFull.getForbidden(forbidden,text);
            if(isForbidden.equals(1)){
                intercept = 1;
            }
            if(intercept.equals(1)){
                //以十分钟为检测周期，违禁一次刷新一次，等于4次则禁言
                String isIntercept = redisHelp.getRedis(this.dataprefix+"_"+uid+"_isIntercept",redisTemplate);
                if(isIntercept==null){
                    redisHelp.setRedis(this.dataprefix+"_"+uid+"_isIntercept","1",600,redisTemplate);
                }else{
                    Integer frequency = Integer.parseInt(isIntercept) + 1;
                    if(frequency==4){
                        securityService.safetyMessage("用户ID："+uid+"，在帖子编辑接口多次触发违禁，请及时确认处理。","system");
                        redisHelp.setRedis(this.dataprefix+"_"+uid+"_silence","1",apiconfig.getInterceptTime(),redisTemplate);
                        return Result.getResultJson(0,"你已多次发送违禁词，被禁言一小时！",null);
                    }else{
                        redisHelp.setRedis(this.dataprefix+"_"+uid+"_isIntercept",frequency.toString(),600,redisTemplate);
                    }

                }
                return Result.getResultJson(0,"内容存在违禁词",null);
            }
            //违禁词拦截结束
            text = text.replace("||rn||","\r\n");
            Long date = System.currentTimeMillis();
            String created = String.valueOf(date).substring(0,10);
            TypechoForum oldForum = service.selectByKey(id);
            if(oldForum==null){
                return Result.getResultJson(0,"帖子不存在",null);
            }

            TypechoForum newForum = new TypechoForum();
            newForum.setId(id);
            newForum.setText(text);
            newForum.setTitle(title);
            newForum.setModified(Integer.parseInt(created));
            newForum.setIsMd(isMd);
            //腾讯云内容违规检测
            if(apiconfig.getCmsSwitch().equals(1)||apiconfig.getCmsSwitch().equals(3)){
                try{
                    String setTitle = baseFull.encrypt(title);
                    Map violationData = securityService.textViolation(setTitle);
                    String Suggestion = violationData.get("Suggestion").toString();
                    if(Suggestion.equals("Block")){
                        return Result.getResultJson(0,"内容涉及违规，请检查后重新提交！",null);
                    }
                }catch (Exception e){
                    e.printStackTrace();
                }

            }
            //如果帖子之前是草稿，那么重新走发布流程，如果不是就不变
            if(forum.getStatus().equals(-1)){
                //如果选择不是草稿，就执行发布流程
                if(isDraft.equals(0)){
                    if (apiconfig.getForumAudit().equals(1)) {
                        newForum.setStatus(0);
                    } else {
                        newForum.setStatus(1);
                    }
                    //腾讯云内容违规检测
                    if(apiconfig.getCmsSwitch().equals(1)||apiconfig.getCmsSwitch().equals(3)){
                        try{
                            String setText = baseFull.htmlToText(text);
                            setText = baseFull.encrypt(setText);
                            Map violationData = securityService.textViolation(setText);
                            String Suggestion = violationData.get("Suggestion").toString();
                            if(Suggestion.equals("Block")){
                                return Result.getResultJson(0,"内容涉及违规，请检查后重新提交！",null);
                            }
                            //对帖子评论的审核更加严格
                            if(Suggestion.equals("Review")){
                                newForum.setStatus(0);
                            }
                        }catch (Exception e){
                            e.printStackTrace();
                        }
                    }
                }

            }else{
                if(isDraft.equals(1)){
                    return Result.getResultJson(0,"当前帖子状态无法转为草稿",null);
                }
            }
            //编辑付费阅读
            if (isPaid.equals(1)) {
                TypechoShop shop = new TypechoShop();
                if(!forum.getSid().equals(0)) {
                    shop.setId(forum.getSid());
                }
                shop.setValue(shopText);
                shop.setVipDiscount(shopDiscount);
                shop.setIsView(0);
                shop.setNum(-1);
                shop.setStatus(1);
                shop.setPrice(shopPice);
                shop.setType(4);
                shop.setUid(forum.getAuthorId());
                shop.setIsMd(isMd);
                if(forum.getSid().equals(0)){

                    shopService.insert(shop);
                    newForum.setSid(shop.getId());
                }else{
                    shopService.update(shop);
                }
            }
            if (isAudit) {
                if (apiconfig.getForumAudit().equals(1)) {
                    newForum.setStatus(0);
                } else {
                    newForum.setStatus(1);
                }
            }
            int rows = service.update(newForum);
            editFile.setLog("用户"+uid+"编辑了帖子"+id+"审核"+isAudit);
            JSONObject response = new JSONObject();
            response.put("code" , rows);
            if (isAudit) {
                if (apiconfig.getForumAudit().equals(1)) {
                    response.put("msg"  , rows > 0 ? "保存成功，等待审核" : "保存失败");
                } else {
                    response.put("msg"  , rows > 0 ? "保存成功" : "保存失败");
                }
            }else{
                response.put("msg"  , rows > 0 ? "保存成功" : "保存失败");
            }

            //清理列表reids缓存
            redisHelp.deleteKeysWithPattern("*"+this.dataprefix+"_"+"postInfo_"+id+"*",redisTemplate,this.dataprefix);
            redisHelp.deleteKeysWithPattern("*"+this.dataprefix+"_postList_1*",redisTemplate,this.dataprefix);
            return response.toString();
        }catch (Exception e){
            e.printStackTrace();
            return Result.getResultJson(0,"接口请求异常，请联系管理员",null);
        }

    }

    /**
     * 草稿列表
     */
    @RequestMapping(value = "/draftList")
    @ResponseBody
    @LoginRequired(purview = "0")
    public String draftList (@RequestParam(value = "page"        , required = false, defaultValue = "1") Integer page,
            @RequestParam(value = "limit", required = false, defaultValue = "15") Integer limit,
            @RequestParam(value = "order", required = false, defaultValue = "created") String  order,
            @RequestParam(value = "token", required = false) String  token) {

        if(limit>50){
            limit = 50;
        }
        Integer uStatus = UStatus.getStatus(token,this.dataprefix,redisTemplate);
        Map map = redisHelp.getMapValue(this.dataprefix + "_" + "userInfo" + token, redisTemplate);
        Integer uid = Integer.parseInt(map.get("uid").toString());
        Integer cacheTime = 5;
        List cacheList =  redisHelp.getList(this.dataprefix+"_"+"draftList_"+page+"_"+limit+"_"+uid+"_"+order,redisTemplate);
        List jsonList = new ArrayList();
        TypechoForum query = new TypechoForum();
        query.setStatus(-1);
        query.setAuthorId(uid);
        Integer total = service.total(query,null);
        try{
            if(cacheList.size()>0){
                jsonList = cacheList;
            }else{
                PageList<TypechoForum> pageList = service.selectPage(query, page, limit,order,null);
                List<TypechoForum> list = pageList.getList();
                if(list.size() < 1){
                    JSONObject noData = new JSONObject();
                    noData.put("code" , 1);
                    noData.put("msg"  , "");
                    noData.put("data" , new ArrayList());
                    noData.put("count", 0);
                    noData.put("total", total);
                    return noData.toString();
                }
                for (int i = 0; i < list.size(); i++) {
                    Map json = JSONObject.parseObject(JSONObject.toJSONString(list.get(i)), Map.class);
                    TypechoForum forum = list.get(i);
                    Integer userid = forum.getAuthorId();
                    //基本信息处理
                    List imgList = baseFull.getImageSrc(list.get(i).getText());
                    json.put("images",imgList);
                    String text = list.get(i).getText();
                    text = baseFull.toStrByChinese(text);
                    json.put("text",text.length()>200 ? text.substring(0,200)+"……" : text);
                    //获取用户信息
                    Map userJson = UserStatus.getUserInfo(userid,apiconfigService,usersService);
                    //获取用户等级
//                    TypechoComments comments = new TypechoComments();
//                    comments.setAuthorId(userid);
//                    Integer lv = commentsService.total(comments,null);
//                    userJson.put("lv", baseFull.getLv(lv));
                    json.put("userJson",userJson);
                    if (uStatus != 0) {
                        TypechoFan fan = new TypechoFan();
                        fan.setUid(uid);
                        fan.setTouid(forum.getAuthorId());
                        Integer isFollow = fanService.total(fan);
                        json.put("isFollow",isFollow);

                        TypechoUserlog userlog = new TypechoUserlog();
                        userlog.setCid(forum.getId());
                        userlog.setType("postLike");
                        userlog.setUid(uid);
                        Integer isLikes = userlogService.total(userlog);
                        if(isLikes > 0){
                            json.put("isLikes",1);
                        }else{
                            json.put("isLikes",0);
                        }

                    }else{
                        json.put("isLikes",0);
                        json.put("isFollow",0);
                    }
                    TypechoForumSection section = forumSectionService.selectByKey(forum.getSection());
                    if(section==null){
                        json.put("sectionJson",new HashMap<>());

                    }else{
                        Map sectionJson = JSONObject.parseObject(JSONObject.toJSONString(section), Map.class);
                        json.put("sectionJson",sectionJson);
                    }
                    jsonList.add(json);
                }
                redisHelp.delete(this.dataprefix+"_"+"draftList_"+page+"_"+limit+"_"+uid+"_"+order,redisTemplate);
                redisHelp.setList(this.dataprefix+"_"+"draftList_"+page+"_"+limit+"_"+uid+"_"+order,jsonList,cacheTime,redisTemplate);
            }
        }catch (Exception e){
            e.printStackTrace();
            if(cacheList.size()>0){
                jsonList = cacheList;
            }
        }
        JSONObject response = new JSONObject();
        response.put("code" , 1);
        response.put("msg"  , "");
        response.put("data" , jsonList);
        response.put("count", jsonList.size());
        response.put("total", total);
        return response.toString();
    }

    /**
     * 草稿删除
     */
    @RequestMapping(value = "/draftDelete")
    @ResponseBody
    @LoginRequired(purview = "0")
    public String draftDelete(@RequestParam(value = "id", required = false) Integer  id,
                             @RequestParam(value = "token", required = false) String  token) {
        try{
            Map map =redisHelp.getMapValue(this.dataprefix+"_"+"userInfo"+token,redisTemplate);
            Integer uid  = Integer.parseInt(map.get("uid").toString());
            TypechoForum forum = service.selectByKey(id);
            if(forum==null){
                return Result.getResultJson(0,"帖子不存在",null);
            }
            if(!forum.getAuthorId().equals(uid)){
                return Result.getResultJson(0,"你没有操作权限",null);
            }
            int rows = service.delete(id);
            JSONObject response = new JSONObject();
            response.put("code" ,rows > 0 ? 1: 0 );
            response.put("data" , rows);
            response.put("msg"  , rows > 0 ? "操作成功" : "操作失败");
            redisHelp.deleteKeysWithPattern("*"+this.dataprefix+"_draftList_1*",redisTemplate,this.dataprefix);
            return response.toString();
        }catch (Exception e){
            e.printStackTrace();
            return Result.getResultJson(0,"接口请求异常，请联系管理员",null);
        }

    }
    /**
     * 帖子列表
     */
    @RequestMapping(value = "/postList")
    @ResponseBody
    @LoginRequired(purview = "-1")
    public String postList (
            @RequestParam(value = "searchParams", required = false) String  searchParams,
            @RequestParam(value = "page"        , required = false, defaultValue = "1") Integer page,
            @RequestParam(value = "limit"       , required = false, defaultValue = "15") Integer limit,
            @RequestParam(value = "searchKey"        , required = false, defaultValue = "") String searchKey,
            @RequestParam(value = "order", required = false, defaultValue = "created") String  order,
            @RequestParam(value = "token", required = false) String  token) {

        if(limit>50){
            limit = 50;
        }
        String sqlParams = "null";
        //如果开启全局登录，则必须登录才能得到数据
        Integer uStatus = UStatus.getStatus(token,this.dataprefix,redisTemplate);
        TypechoApiconfig apiconfig = UStatus.getConfig(this.dataprefix,apiconfigService,redisTemplate);
        if(apiconfig.getIsLogin().equals(1)){
            if(uStatus==0){
                return Result.getResultJson(0,"用户未登录或Token验证失败",null);
            }
        }
        //验证结束

        TypechoForum query = new TypechoForum();
        query.setStatus(1);
        if (StringUtils.isNotBlank(searchParams)) {
            JSONObject object = JSON.parseObject(searchParams);
            query = object.toJavaObject(TypechoForum.class);
            Map paramsJson = JSONObject.parseObject(JSONObject.toJSONString(query), Map.class);
            sqlParams = paramsJson.toString();

        }
        Map map = new HashMap();
        Integer uid = 0;
        Integer cacheTime = 20;
        if (uStatus != 0) {
            map = redisHelp.getMapValue(this.dataprefix + "_" + "userInfo" + token, redisTemplate);
            uid =Integer.parseInt(map.get("uid").toString());
            cacheTime = 5;
        }else{
            //未登录只能看已发布帖子
            query.setStatus(1);
        }
        List cacheList =  redisHelp.getList(this.dataprefix+"_"+"postList_"+page+"_"+limit+"_"+searchKey+"_"+uid+"_"+order+"_"+sqlParams,redisTemplate);
        List jsonList = new ArrayList();

        Integer total = service.total(query,searchKey);
        try{
            if(cacheList.size()>0){
                jsonList = cacheList;
            }else{
                PageList<TypechoForum> pageList = service.selectPage(query, page, limit,order,searchKey);
                List<TypechoForum> list = pageList.getList();
                if(list.size() < 1){
                    JSONObject noData = new JSONObject();
                    noData.put("code" , 1);
                    noData.put("msg"  , "");
                    noData.put("data" , new ArrayList());
                    noData.put("count", 0);
                    noData.put("total", total);
                    return noData.toString();
                }
                for (int i = 0; i < list.size(); i++) {
                    Map json = JSONObject.parseObject(JSONObject.toJSONString(list.get(i)), Map.class);
                    TypechoForum forum = list.get(i);
                    Integer userid = forum.getAuthorId();
                    //基本信息处理
                    List<String> imgList = baseFull.getImageSrc(list.get(i).getText());
                    List<Map<String, String>> videoList = baseFull.getVideoInfo(list.get(i).getText());
                    json.put("images",imgList);
                    json.put("videos", videoList);
                    String text = list.get(i).getText();
                    text = baseFull.toStrByChinese(text);
                    json.put("text",text.length()>200 ? text.substring(0,200)+"……" : text);
                    //获取用户信息
                    Map userJson = UserStatus.getUserInfo(userid,apiconfigService,usersService);
                    //获取用户认证信息
                    List<identifyCompany> companyList = jdbcTemplate.query("SELECT * FROM `" + prefix + "_company` WHERE uid = " + userid + ";", new BeanPropertyRowMapper<>(identifyCompany.class));
                    List<identifyConsumer> consumerList = jdbcTemplate.query("SELECT * FROM `" + prefix + "_consumer` WHERE uid = " + userid + ";", new BeanPropertyRowMapper<>(identifyConsumer.class));
                    if (companyList.size() > 0) {
                        identifyCompany company = companyList.get(0);
                        if (company.getIdentifyStatus().equals("1")) {
                            userJson.put("lvrz", 1);
                            userJson.put("lvrzName", company.getName());
                        } else {
                            userJson.put("lvrz", -1);
                            userJson.put("lvrzName","");
                        }
                    } else {
                        userJson.put("lvrz", 0);
                        userJson.put("lvrzName","");
                    }
                    if (consumerList.size() > 0) {
                        identifyConsumer consumer = consumerList.get(0);
                        if (consumer.getIdentifyStatus().equals("1")) {
                            userJson.put("smrz", 1);
                        } else {
                            userJson.put("smrz", -1);
                        }
                    } else {
                        userJson.put("smrz", 0);
                    }
                    json.put("userJson",userJson);
                    if (uStatus != 0) {
                        TypechoFan fan = new TypechoFan();
                        fan.setUid(uid);
                        fan.setTouid(forum.getAuthorId());
                        Integer isFollow = fanService.total(fan);
                        json.put("isFollow",isFollow);
                        TypechoUserlog userlog = new TypechoUserlog();
                        userlog.setCid(forum.getId());
                        userlog.setType("postLike");
                        userlog.setUid(uid);
                        Integer isLikes = userlogService.total(userlog);
                        if(isLikes > 0){
                            json.put("isLikes",1);
                        }else{
                            json.put("isLikes",0);
                        }

                    }else{
                        json.put("isLikes",0);
                        json.put("isFollow",0);
                    }
                    TypechoForumSection section = forumSectionService.selectByKey(forum.getSection());
                    if(section==null){
                        json.put("sectionJson",new HashMap<>());

                    }else{
                        Map sectionJson = JSONObject.parseObject(JSONObject.toJSONString(section), Map.class);
                        json.put("sectionJson",sectionJson);
                    }
                    jsonList.add(json);
                }
                redisHelp.delete(this.dataprefix+"_"+"postList_"+page+"_"+limit+"_"+searchKey+"_"+uid+"_"+order+"_"+sqlParams,redisTemplate);
                redisHelp.setList(this.dataprefix+"_"+"postList_"+page+"_"+limit+"_"+searchKey+"_"+uid+"_"+order+"_"+sqlParams,jsonList,cacheTime,redisTemplate);
            }
        }catch (Exception e){
            e.printStackTrace();
            if(cacheList.size()>0){
                jsonList = cacheList;
            }
        }
        JSONObject response = new JSONObject();
        response.put("code" , 1);
        response.put("msg"  , "");
        response.put("data" , jsonList);
        response.put("count", jsonList.size());
        response.put("total", total);
        return response.toString();
    }
    /**
     * 获取帖子详情
     *
     */
    @RequestMapping(value = "/postInfo")
    @ResponseBody
    @LoginRequired(purview = "-1")
    public String spaceInfo (@RequestParam(value = "id", required = false) Integer  id,
                             @RequestParam(value = "token", required = false) String  token,
                             HttpServletRequest request) {
        try{
            Map postInfoJson = new HashMap();

            Map map = new HashMap();
            Integer uid = 0;
            Integer cacheTime = 20;
            //如果开启全局登录，则必须登录才能得到数据
            Integer uStatus = UStatus.getStatus(token,this.dataprefix,redisTemplate);
            TypechoApiconfig apiconfig = UStatus.getConfig(this.dataprefix,apiconfigService,redisTemplate);
            if(apiconfig.getIsLogin().equals(1)){
                if(uStatus==0){
                    return Result.getResultJson(0,"用户未登录或Token验证失败",null);
                }
            }
            //验证结束
            if (uStatus != 0) {
                map = redisHelp.getMapValue(this.dataprefix + "_" + "userInfo" + token, redisTemplate);
                uid =Integer.parseInt(map.get("uid").toString());
                cacheTime = 5;
            }
            Map cacheInfo = redisHelp.getMapValue(this.dataprefix+"_"+"postInfo_"+id+"_"+uid,redisTemplate);
            if(cacheInfo.size()>0){
                postInfoJson = cacheInfo;
            }else{
                TypechoForum forum = service.selectByKey(id);
                String forumText = forum.getText();
                String forbidden = apiconfig.getForbidden();
                Integer textForbidden = baseFull.getForbidden(forbidden,forumText);
//                if(textForbidden.equals(1)){
//                    forumText = "内容违规，无法展示";
//                    forum.setText(forumText);
//                }
                postInfoJson = JSONObject.parseObject(JSONObject.toJSONString(forum), Map.class);
                //基本信息
                Integer athorId = forum.getAuthorId();
                List<identifyCompany> companyList = jdbcTemplate.query("SELECT * FROM `" + prefix + "_company` WHERE uid = " + athorId + ";", new BeanPropertyRowMapper<>(identifyCompany.class));
                List<identifyConsumer> consumerList = jdbcTemplate.query("SELECT * FROM `" + prefix + "_consumer` WHERE uid = " + athorId + ";", new BeanPropertyRowMapper<>(identifyConsumer.class));
                if (companyList.size() > 0) {
                    identifyCompany company = companyList.get(0);
                    if (company.getIdentifyStatus().equals("1")) {
                        postInfoJson.put("lvrz", 1);
                    } else {
                        postInfoJson.put("lvrz", -1);
                    }
                } else {
                    postInfoJson.put("lvrz", 0);
                }
                if (consumerList.size() > 0) {
                    identifyConsumer consumer = consumerList.get(0);
                    if (consumer.getIdentifyStatus().equals("1")) {
                        postInfoJson.put("smrz", 1);
                    } else {
                        postInfoJson.put("smrz", -1);
                    }
                } else {
                    postInfoJson.put("smrz", 0);
                }
                List imgList = baseFull.getImageSrc(forum.getText());
                postInfoJson.put("images",imgList);
                //获取创建人信息
                Integer userid = forum.getAuthorId();
                Map userJson = UserStatus.getUserInfo(userid,apiconfigService,usersService);

                //获取用户等级
//                TypechoComments comments = new TypechoComments();
//                comments.setAuthorId(userid);
//                Integer lv = commentsService.total(comments,null);
//                userJson.put("lv", baseFull.getLv(lv));
                postInfoJson.put("userJson",userJson);
                if (uStatus != 0) {
                    TypechoFan fan = new TypechoFan();
                    fan.setUid(uid);
                    fan.setTouid(forum.getAuthorId());
                    Integer isFollow = fanService.total(fan);
                    postInfoJson.put("isFollow",isFollow);

                    TypechoUserlog userlog = new TypechoUserlog();
                    userlog.setCid(forum.getId());
                    userlog.setUid(uid);
                    userlog.setType("postLike");
                    Integer isLikes = userlogService.total(userlog);
                    if(isLikes > 0){
                        postInfoJson.put("isLikes",1);
                    }else{
                        postInfoJson.put("isLikes",0);
                    }

                    TypechoUserlog marklog = new TypechoUserlog();
                    marklog.setCid(forum.getId());
                    marklog.setUid(uid);
                    marklog.setType("postMark");
                    Integer isMark = userlogService.total(marklog);
                    if(isMark > 0){
                        postInfoJson.put("isMark",1);
                    }else{
                        postInfoJson.put("isMark",0);
                    }
                }else{
                    postInfoJson.put("isMark",0);
                    postInfoJson.put("isLikes",0);
                    postInfoJson.put("isFollow",0);
                }

                //帖子阅读量增加
                String  agent =  request.getHeader("User-Agent");
                String  ip = baseFull.getIpAddr(request);
                String isRead = redisHelp.getRedis(this.dataprefix+"_"+"isPostRead"+"_"+ip+"_"+agent+"_"+id,redisTemplate);
                if(isRead==null){
                    //添加阅读量
                    Integer views = forum.getViews();
                    views = views + 1;
                    TypechoForum toForum = new TypechoForum();
                    toForum.setId(id);
                    toForum.setViews(views);
                    service.update(toForum);

                }
                redisHelp.setRedis(this.dataprefix+"_"+"isPostRead"+"_"+ip+"_"+agent+"_"+id,"yes",1800,redisTemplate);


                redisHelp.delete(this.dataprefix+"_"+"postInfo_"+id+"_"+uid,redisTemplate);
                redisHelp.setKey(this.dataprefix+"_"+"postInfo_"+id+"_"+uid,postInfoJson,cacheTime,redisTemplate);
            }

            JSONObject response = new JSONObject();

            response.put("code", 1);
            response.put("msg", "");
            response.put("data", postInfoJson);

            return response.toString();
        }catch (Exception e){
            e.printStackTrace();
            JSONObject response = new JSONObject();
            response.put("code", 1);
            response.put("msg", "");
            response.put("data", null);

            return response.toString();
        }
    }
    /**
     * 帖子置顶
     *
     */
    @RequestMapping(value = "/postTop")
    @ResponseBody
    @LoginRequired(purview = "0")
    public String postTop (@RequestParam(value = "id", required = false) Integer  id,
                           @RequestParam(value = "type", required = false, defaultValue = "1") Integer  type,
                           @RequestParam(value = "token", required = false) String  token){
        if(!type.equals(1)&&!type.equals(2)&&!type.equals(0)){
            return Result.getResultJson(0,"参数错误",null);
        }
        Integer uStatus = UStatus.getStatus(token,this.dataprefix,redisTemplate);
        if(uStatus==0){
            return Result.getResultJson(0,"用户未登录或Token验证失败",null);
        }
        Map map =redisHelp.getMapValue(this.dataprefix+"_"+"userInfo"+token,redisTemplate);
        Integer uid  = Integer.parseInt(map.get("uid").toString());
        String group = map.get("group").toString();
        TypechoForum forum = service.selectByKey(id);
        if(forum==null){
            return Result.getResultJson(0,"帖子不存在",null);
        }
        Integer sectionId = forum.getSection();
        Integer forumPermissions = UStatus.forumPermissions(map,forumModeratorService,3,sectionId);
        if(forumPermissions.equals(0)){
            return Result.getResultJson(0,"你没有操作权限",null);
        }
        TypechoForum newForum = new TypechoForum();
        newForum.setId(id);
        newForum.setIsTop(type);
        int rows = service.update(newForum);

        Long date = System.currentTimeMillis();
        String created = String.valueOf(date).substring(0,10);
        TypechoInbox insert = new TypechoInbox();
        insert.setUid(uid);
        insert.setTouid(forum.getAuthorId());
        insert.setType("system");
        if(type.equals(1)) {
            insert.setText("你的帖子【" + forum.getTitle() + "】被管理员版块置顶");
        }
        if(type.equals(2)) {
            insert.setText("你的帖子【" + forum.getTitle() + "】被管理员全站置顶");
        }
        if(type.equals(0)) {
            insert.setText("你的帖子【" + forum.getTitle() + "】被管理员取消置顶");
        }
        insert.setCreated(Integer.parseInt(created));
        inboxService.insert(insert);

        JSONObject response = new JSONObject();
        response.put("code" ,rows > 0 ? 1: 0 );
        response.put("data" , rows);
        response.put("msg"  , rows > 0 ? "操作成功" : "操作失败");
        redisHelp.deleteKeysWithPattern("*"+this.dataprefix+"_postList_1*",redisTemplate,this.dataprefix);
        return response.toString();
    }
    /**
     * 帖子加精
     *
     */
    @RequestMapping(value = "/postRecommend")
    @ResponseBody
    @LoginRequired(purview = "0")
    public String postRecommend (@RequestParam(value = "id", required = false) Integer  id,
                                 @RequestParam(value = "type", required = false, defaultValue = "1") Integer  type,
                                @RequestParam(value = "token", required = false) String  token){
        if(!type.equals(1)&&!type.equals(0)){
            return Result.getResultJson(0,"参数错误",null);
        }
        Integer uStatus = UStatus.getStatus(token,this.dataprefix,redisTemplate);
        if(uStatus==0){
            return Result.getResultJson(0,"用户未登录或Token验证失败",null);
        }
        Map map =redisHelp.getMapValue(this.dataprefix+"_"+"userInfo"+token,redisTemplate);
        Integer uid  = Integer.parseInt(map.get("uid").toString());
        String group = map.get("group").toString();
        TypechoForum forum = service.selectByKey(id);
        if(forum==null){
            return Result.getResultJson(0,"帖子不存在",null);
        }
        Integer sectionId = forum.getSection();
        Integer forumPermissions = UStatus.forumPermissions(map,forumModeratorService,3,sectionId);
        if(forumPermissions.equals(0)){
            return Result.getResultJson(0,"你没有操作权限",null);
        }
        TypechoForum newForum = new TypechoForum();
        newForum.setId(id);
        newForum.setIsrecommend(type);
        int rows = service.update(newForum);

        Long date = System.currentTimeMillis();
        String created = String.valueOf(date).substring(0,10);
        TypechoInbox insert = new TypechoInbox();
        insert.setUid(uid);
        insert.setTouid(forum.getAuthorId());
        insert.setType("system");
        if(type.equals(1)){
            insert.setText("你的帖子【"+forum.getTitle()+"】被管理员加精");
        }
        if(!type.equals(1)){
            insert.setText("你的帖子【"+forum.getTitle()+"】被管理员取消加精");
        }
        insert.setCreated(Integer.parseInt(created));
        inboxService.insert(insert);

        JSONObject response = new JSONObject();
        response.put("code" ,rows > 0 ? 1: 0 );
        response.put("data" , rows);
        response.put("msg"  , rows > 0 ? "操作成功" : "操作失败");
        redisHelp.deleteKeysWithPattern("*"+this.dataprefix+"_postList_1*",redisTemplate,this.dataprefix);
        return response.toString();
    }
    /**
     * 帖子轮播
     *
     */
    @RequestMapping(value = "/postSwiper")
    @ResponseBody
    @LoginRequired(purview = "0")
    public String postSwiper (@RequestParam(value = "id", required = false) Integer  id,
                              @RequestParam(value = "type", required = false, defaultValue = "1") Integer  type,
                           @RequestParam(value = "token", required = false) String  token){
        if(!type.equals(1)&&!type.equals(0)){
            return Result.getResultJson(0,"参数错误",null);
        }
        Map map =redisHelp.getMapValue(this.dataprefix+"_"+"userInfo"+token,redisTemplate);
        Integer uid  = Integer.parseInt(map.get("uid").toString());
        String group = map.get("group").toString();
        TypechoForum forum = service.selectByKey(id);
        if(forum==null){
            return Result.getResultJson(0,"帖子不存在",null);
        }
        Integer sectionId = forum.getSection();
        Integer forumPermissions = UStatus.forumPermissions(map,forumModeratorService,3,sectionId);
        if(forumPermissions.equals(0)){
            return Result.getResultJson(0,"你没有操作权限",null);
        }
        TypechoForum newForum = new TypechoForum();
        newForum.setId(id);
        newForum.setIsswiper(type);
        int rows = service.update(newForum);

        Long date = System.currentTimeMillis();
        String created = String.valueOf(date).substring(0,10);
        TypechoInbox insert = new TypechoInbox();
        insert.setUid(uid);
        insert.setTouid(forum.getAuthorId());
        insert.setType("system");
        if(type.equals(1)) {
            insert.setText("你的帖子【" + forum.getTitle() + "】被管理员推送到轮播");
        }
        if(!type.equals(1)) {
            insert.setText("你的帖子【" + forum.getTitle() + "】被管理员取消轮播");
        }
        insert.setCreated(Integer.parseInt(created));
        inboxService.insert(insert);

        JSONObject response = new JSONObject();
        response.put("code" ,rows > 0 ? 1: 0 );
        response.put("data" , rows);
        response.put("msg"  , rows > 0 ? "操作成功" : "操作失败");
        return response.toString();
    }
    /**
     * 帖子删除
     */
    @RequestMapping(value = "/postDelete")
    @ResponseBody
    @LoginRequired(purview = "0")
    public String postDelete(@RequestParam(value = "id", required = false) Integer  id,
                             @RequestParam(value = "token", required = false) String  token) {
        try{
            Map map =redisHelp.getMapValue(this.dataprefix+"_"+"userInfo"+token,redisTemplate);
            Integer uid  = Integer.parseInt(map.get("uid").toString());
            String group = map.get("group").toString();
            TypechoForum forum = service.selectByKey(id);
            if(forum==null){
                return Result.getResultJson(0,"帖子不存在",null);
            }
            Integer forumAuthorId = forum.getAuthorId();
            if (!uid.equals(forumAuthorId)) {
                Integer sectionId = forum.getSection();
                Integer authorId = forum.getAuthorId();
                Integer isMyself = 0;
                if(uid.equals(authorId)){
                    isMyself = 1;
                }
                Integer forumPermissions = UStatus.forumPermissions(map, forumModeratorService, 2, sectionId);
                if(forumPermissions.equals(0) && isMyself.equals(0)){
                    return Result.getResultJson(0,"你没有操作权限",null);
                }
            }

            int rows = service.delete(id);

            Long date = System.currentTimeMillis();
            String created = String.valueOf(date).substring(0,10);
            TypechoInbox insert = new TypechoInbox();
            insert.setUid(uid);
            insert.setTouid(forum.getAuthorId());
            insert.setType("system");
            insert.setText("你的帖子【"+forum.getTitle()+"】已被删除");
            insert.setCreated(Integer.parseInt(created));
            inboxService.insert(insert);

            JSONObject response = new JSONObject();
            response.put("code" ,rows > 0 ? 1: 0 );
            response.put("data" , rows);
            response.put("msg"  , rows > 0 ? "操作成功" : "操作失败");
            redisHelp.deleteKeysWithPattern("*"+this.dataprefix+"_postList_1*",redisTemplate,this.dataprefix);
            return response.toString();
        }catch (Exception e){
            e.printStackTrace();
            return Result.getResultJson(0,"接口请求异常，请联系管理员",null);
        }

    }
    /**
     * 帖子移动
     */
    @RequestMapping(value = "/postTransfer")
    @ResponseBody
    @LoginRequired(purview = "0")
    public String postTransfer(@RequestParam(value = "id", required = false) Integer  id,
                               @RequestParam(value = "sectionid", required = false) Integer  sectionid,
                             @RequestParam(value = "token", required = false) String  token) {
        try{
            Map map =redisHelp.getMapValue(this.dataprefix+"_"+"userInfo"+token,redisTemplate);
            Integer uid  = Integer.parseInt(map.get("uid").toString());
            String group = map.get("group").toString();
            TypechoForum forum = service.selectByKey(id);
            if(forum==null){
                return Result.getResultJson(0,"帖子不存在",null);
            }
            TypechoForumSection section = forumSectionService.selectByKey(sectionid);
            if(section==null){
                return Result.getResultJson(0,"版块不存在",null);
            }
            Integer forumPermissions = UStatus.forumPermissions(map,forumModeratorService,5,0);
            if(forumPermissions.equals(0)){
                return Result.getResultJson(0,"你没有操作权限",null);
            }
            TypechoForum newForum = new TypechoForum();
            newForum.setId(id);
            newForum.setTypeid(section.getParent());
            newForum.setSection(sectionid);


            int rows = service.update(newForum);
            Long date = System.currentTimeMillis();
            String created = String.valueOf(date).substring(0,10);
            TypechoInbox insert = new TypechoInbox();
            insert.setUid(uid);
            insert.setTouid(forum.getAuthorId());
            insert.setType("system");
            insert.setText("你的帖子【"+forum.getTitle()+"】被转移到版块【"+section.getName()+"】");
            insert.setCreated(Integer.parseInt(created));
            inboxService.insert(insert);

            JSONObject response = new JSONObject();
            response.put("code" ,rows > 0 ? 1: 0 );
            response.put("data" , rows);
            response.put("msg"  , rows > 0 ? "帖子移动到"+section.getName() : "帖子移动失败");
            return response.toString();
        }catch (Exception e){
            e.printStackTrace();
            return Result.getResultJson(0,"接口请求异常，请联系管理员",null);
        }

    }
    /**
     * 帖子审核
     */
    @RequestMapping(value = "/postReview")
    @ResponseBody
    @LoginRequired(purview = "0")
    public String postReview(@RequestParam(value = "id", required = false) Integer  id,
                             @RequestParam(value = "type", required = false, defaultValue = "1") Integer  type,
                             @RequestParam(value = "token", required = false) String  token) {
        try{
            if(!type.equals(1)&&!type.equals(0)){
                return Result.getResultJson(0,"参数错误",null);
            }
            Map map =redisHelp.getMapValue(this.dataprefix+"_"+"userInfo"+token,redisTemplate);
            Integer uid  = Integer.parseInt(map.get("uid").toString());
            String group = map.get("group").toString();
            TypechoApiconfig apiconfig = UStatus.getConfig(this.dataprefix,apiconfigService,redisTemplate);
            TypechoForum forum = service.selectByKey(id);
            if(forum==null){
                return Result.getResultJson(0,"帖子不存在",null);
            }
            Integer sectionId = forum.getSection();
            Integer forumPermissions = UStatus.forumPermissions(map,forumModeratorService,1,sectionId);
            if(forumPermissions.equals(0)){
                return Result.getResultJson(0,"你没有操作权限",null);
            }
            Integer cUID = forum.getAuthorId();
            int rows = 0;
            if(type.equals(1)){
                TypechoForum newPost = new TypechoForum();
                newPost.setId(id);
                newPost.setStatus(type);
                rows = service.update(newPost);
            }else{
                rows = service.delete(id);
            }
            Long date = System.currentTimeMillis();
            String created = String.valueOf(date).substring(0,10);
            TypechoInbox insert = new TypechoInbox();
            insert.setUid(uid);
            insert.setTouid(forum.getAuthorId());
            insert.setType("system");
            if(type.equals(1)){
                insert.setText("你的帖子【"+forum.getTitle()+"】审核通过");
                //审核后增加经验
                Integer postExp = apiconfig.getPostExp();
                if(postExp > 0){
                    //生成操作记录
                    SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
                    String curtime = sdf.format(new Date(date));

                    TypechoUserlog userlog = new TypechoUserlog();
                    userlog.setUid(cUID);
                    //cid用于存放真实时间
                    userlog.setCid(Integer.parseInt(curtime));
                    userlog.setType("postExp");
                    Integer size = userlogService.total(userlog);
                    //只有前三次发布文章获得经验
                    if(size < 3){
                        userlog.setNum(postExp);
                        userlog.setCreated(Integer.parseInt(created));
                        userlogService.insert(userlog);
                        //修改用户资产
                        TypechoUsers oldUser = usersService.selectByKey(cUID);
                        Integer experience = oldUser.getExperience();
                        experience = experience + postExp;
                        TypechoUsers updateUser = new TypechoUsers();
                        updateUser.setUid(cUID);
                        updateUser.setExperience(experience);
                        usersService.update(updateUser);
                    }
                }
            }
            if(type.equals(0)){
                insert.setText("你的帖子【"+forum.getTitle()+"】未审核通过，已被删除");
            }
            insert.setCreated(Integer.parseInt(created));
            inboxService.insert(insert);

            JSONObject response = new JSONObject();
            response.put("code" ,rows > 0 ? 1: 0 );
            response.put("data" , rows);
            response.put("msg"  , rows > 0 ? "操作成功，请等待缓存刷新" : "操作失败");
            redisHelp.deleteKeysWithPattern("*"+this.dataprefix+"_postList_1*",redisTemplate,this.dataprefix);
            return response.toString();
        }catch (Exception e){
            e.printStackTrace();
            return Result.getResultJson(0,"接口请求异常，请联系管理员",null);
        }
    }
    /**
     * 帖子锁定&解锁
     */
    @RequestMapping(value = "/postLock")
    @ResponseBody
    @LoginRequired(purview = "0")
    public String postLock(@RequestParam(value = "id", required = false) Integer  id,
                             @RequestParam(value = "type", required = false, defaultValue = "1") Integer  type,
                             @RequestParam(value = "token", required = false) String  token) {
        try{
            if(!type.equals(1)&&!type.equals(2)){
                return Result.getResultJson(0,"参数错误",null);
            }
            Map map =redisHelp.getMapValue(this.dataprefix+"_"+"userInfo"+token,redisTemplate);
            Integer uid  = Integer.parseInt(map.get("uid").toString());
            TypechoForum forum = service.selectByKey(id);
            if(forum==null){
                return Result.getResultJson(0,"帖子不存在",null);
            }
            if(forum.getStatus().equals(0)){
                return Result.getResultJson(0,"帖子未过审，暂无法操作",null);
            }
            if(forum.getStatus().equals(type)){
                return Result.getResultJson(0,"帖子已被进行相同操作",null);
            }
            Integer sectionId = forum.getSection();
            Integer forumPermissions = UStatus.forumPermissions(map,forumModeratorService,1,sectionId);
            if(forumPermissions.equals(0)){
                return Result.getResultJson(0,"你没有操作权限",null);
            }

            TypechoForum newPost = new TypechoForum();
            newPost.setId(id);
            newPost.setStatus(type);
            int rows = service.update(newPost);

            Long date = System.currentTimeMillis();
            String created = String.valueOf(date).substring(0,10);
            TypechoInbox insert = new TypechoInbox();
            insert.setUid(uid);
            insert.setTouid(forum.getAuthorId());
            insert.setType("system");
            if(type.equals(1)){
                insert.setText("你的帖子【"+forum.getTitle()+"】已被解锁");
            }
            if(type.equals(2)){
                insert.setText("你的帖子【"+forum.getTitle()+"】已被锁定");
            }
            insert.setCreated(Integer.parseInt(created));
            inboxService.insert(insert);

            JSONObject response = new JSONObject();
            response.put("code" ,rows > 0 ? 1: 0 );
            response.put("data" , rows);
            response.put("msg"  , rows > 0 ? "操作成功" : "操作失败");
            return response.toString();
        }catch (Exception e){
            e.printStackTrace();
            return Result.getResultJson(0,"接口请求异常，请联系管理员",null);
        }
    }
    /**
     * 帖子点赞
     */
    @RequestMapping(value = "/postLikes")
    @ResponseBody
    @LoginRequired(purview = "0")
    public String spaceLikes(@RequestParam(value = "id", required = false) Integer  id,
                             @RequestParam(value = "token", required = false) String  token) {
        try{
            Map map =redisHelp.getMapValue(this.dataprefix+"_"+"userInfo"+token,redisTemplate);
            Integer uid  = Integer.parseInt(map.get("uid").toString());
            Long date = System.currentTimeMillis();
            String userTime = String.valueOf(date).substring(0,10);

            //生成操作日志
            TypechoUserlog userlog = new TypechoUserlog();
            userlog.setUid(uid);
            userlog.setCid(id);
            userlog.setType("postLike");
            Integer isLikes = userlogService.total(userlog);
            if(isLikes>0){
                return Result.getResultJson(0,"你已经点赞过了",null);
            }
            TypechoForum forum = service.selectByKey(id);
            if(forum==null){
                return Result.getResultJson(0,"该帖子不存在",null);
            }

            userlog.setCreated(Integer.parseInt(userTime));
            userlogService.insert(userlog);
            Integer likes = forum.getLikes();
            likes = likes + 1;
            TypechoForum newForum = new TypechoForum();
            newForum.setLikes(likes);
            newForum.setId(id);
            int rows = service.update(newForum);
            redisHelp.delete(this.dataprefix+"_"+"postInfo_"+id+"_"+uid,redisTemplate);
            JSONObject response = new JSONObject();
            response.put("code" ,rows > 0 ? 1: 0 );
            response.put("data" , rows);
            response.put("msg"  , rows > 0 ? "点赞成功" : "点赞失败");
            return response.toString();
        }catch (Exception e){
            e.printStackTrace();
            return Result.getResultJson(0,"接口请求异常，请联系管理员",null);
        }

    }
    /**
     * 帖子打赏
     */
    @RequestMapping(value = "/postReward")
    @ResponseBody
    @LoginRequired(purview = "0")
    public String postReward(@RequestParam(value = "id", required = false) Integer  id,
                             @RequestParam(value = "num", required = false ,defaultValue = "1") Integer  num,
                             @RequestParam(value = "token", required = false) String  token) {
        try{
            Map map =redisHelp.getMapValue(this.dataprefix+"_"+"userInfo"+token,redisTemplate);
            Integer uid  = Integer.parseInt(map.get("uid").toString());
            Long date = System.currentTimeMillis();
            String userTime = String.valueOf(date).substring(0,10);

            if(num < 1){
                return Result.getResultJson(0,"参数不正确",null);
            }
            TypechoUsers user = usersService.selectByKey(uid);
            Integer account = user.getAssets();
            if(num>account){
                return Result.getResultJson(0,"积分不足！",null);
            }
            Integer Assets = account - num;
            //获取作者信息
            TypechoForum forum = service.selectByKey(id);
            if(forum==null){
                return Result.getResultJson(0,"帖子不存在",null);
            }
            Integer authorid = forum.getAuthorId();
            //生成打赏者资产日志（如果是自己打赏自己，就不生成）
            if(!uid.equals(authorid)){
                TypechoPaylog paylog = new TypechoPaylog();
                paylog.setStatus(1);
                paylog.setCreated(Integer.parseInt(userTime));
                paylog.setUid(uid);
                paylog.setOutTradeNo(userTime+"toRewardPost");
                paylog.setTotalAmount("-"+num);
                paylog.setPaytype("toRewardPost");
                paylog.setSubject("打赏帖子");
                paylogService.insert(paylog);
            }else{
                return Result.getResultJson(0,"你不可以打赏自己的帖子！",null);
            }
            //扣除自己的积分
            TypechoUsers newUser = new TypechoUsers();
            newUser.setUid(uid);
            newUser.setAssets(Assets);
            usersService.update(newUser);

            //给文章的作者增加积分

            TypechoUsers toUser = usersService.selectByKey(authorid);
            Integer toAssets = toUser.getAssets();
            Integer curAssets = toAssets + num;
            toUser.setAssets(curAssets);
            usersService.update(toUser);

            if(!uid.equals(authorid)) {
                //生成作者资产日志
                TypechoPaylog paylogB = new TypechoPaylog();
                paylogB.setStatus(1);
                paylogB.setCreated(Integer.parseInt(userTime));
                paylogB.setUid(authorid);
                paylogB.setOutTradeNo(userTime + "reward");
                paylogB.setTotalAmount(num.toString());
                paylogB.setPaytype("reward");
                paylogB.setSubject("来自用户ID" + uid + "打赏");
                paylogService.insert(paylogB);
                //发送消息通知
                String created = String.valueOf(date).substring(0,10);
                TypechoInbox inbox = new TypechoInbox();
                inbox.setUid(uid);
                inbox.setTouid(authorid);
                inbox.setType("finance");
                inbox.setText("打赏了你的帖子【"+forum.getTitle()+"】");
                inbox.setValue(forum.getId());
                inbox.setCreated(Integer.parseInt(created));
                inboxService.insert(inbox);
            }
            //生成操作日志
            TypechoUserlog userlog = new TypechoUserlog();
            userlog.setUid(uid);
            userlog.setCid(id);
            userlog.setType("rewardPost");
            userlog.setToid(authorid);
            userlog.setNum(num);
            userlog.setCreated(Integer.parseInt(userTime));
            int rows = userlogService.insert(userlog);
            JSONObject response = new JSONObject();
            response.put("code" ,rows > 0 ? 1: 0 );
            response.put("data" , rows);
            response.put("msg"  , rows > 0 ? "操作成功" : "操作失败");
            return response.toString();
        }catch (Exception e){
            e.printStackTrace();
            return Result.getResultJson(0,"接口请求异常，请联系管理员",null);
        }

    }
    /**
     * 帖子打赏者列表
     */
    @RequestMapping(value = "/postRewardList")
    @ResponseBody
    @LoginRequired(purview = "-1")
    public String postRewardList(@RequestParam(value = "page"        , required = false, defaultValue = "1") Integer page,
                               @RequestParam(value = "limit"       , required = false, defaultValue = "15") Integer limit,
                               @RequestParam(value = "id", required = false) Integer  id) {
        if(limit>50){
            limit = 50;
        }
        Integer total = 0;

        TypechoUserlog query = new TypechoUserlog();
        query.setCid(id);
        query.setType("rewardPost");
        total = userlogService.total(query);

        List jsonList = new ArrayList();
        List cacheList = redisHelp.getList(this.dataprefix+"_"+"postRewardList_"+page+"_"+limit,redisTemplate);
        try{
            if(cacheList.size()>0){
                jsonList = cacheList;
            }else {
                PageList<TypechoUserlog> pageList = userlogService.selectPage(query, page, limit);
                List<TypechoUserlog> list = pageList.getList();
                if(list.size() < 1){
                    JSONObject noData = new JSONObject();
                    noData.put("code" , 1);
                    noData.put("msg"  , "");
                    noData.put("data" , new ArrayList());
                    noData.put("count", 0);
                    noData.put("total", total);
                    return noData.toString();
                }
                for (int i = 0; i < list.size(); i++) {
                    Integer userid = list.get(i).getUid();
                    Map json = JSONObject.parseObject(JSONObject.toJSONString(list.get(i)), Map.class);
                    //获取用户信息
                    Map userJson = UserStatus.getUserInfo(userid,apiconfigService,usersService);
                    //获取用户等级
//                    TypechoComments comments = new TypechoComments();
//                    comments.setAuthorId(userid);
//                    Integer lv = commentsService.total(comments,null);
//                    userJson.put("lv", baseFull.getLv(lv));
                    json.put("userJson",userJson);
                    jsonList.add(json);
                }
                redisHelp.delete(this.dataprefix+"_"+"postRewardList_"+page+"_"+limit, redisTemplate);
                redisHelp.setList(this.dataprefix+"_"+"postRewardList_"+page+"_"+limit, jsonList, 5, redisTemplate);
            }
        }catch (Exception e){
            if(cacheList.size()>0){
                jsonList = cacheList;
            }
        }
        JSONObject response = new JSONObject();
        response.put("code" , 1);
        response.put("msg"  , "");
        response.put("data" , null != jsonList ? jsonList : new JSONArray());
        response.put("count", jsonList.size());
        response.put("total", total);
        return response.toString();

    }
    /**
     * 帖子收藏
     */
    @RequestMapping(value = "/postMark")
    @ResponseBody
    @LoginRequired(purview = "0")
    public String postMark(@RequestParam(value = "id", required = false) Integer  id,
                             @RequestParam(value = "type", required = false ,defaultValue = "1") Integer  type,
                             @RequestParam(value = "token", required = false) String  token) {
        try{
            Map map =redisHelp.getMapValue(this.dataprefix+"_"+"userInfo"+token,redisTemplate);
            Integer uid  = Integer.parseInt(map.get("uid").toString());
            Long date = System.currentTimeMillis();
            String userTime = String.valueOf(date).substring(0,10);
            if(!type.equals(0)&&!type.equals(1)){
                return Result.getResultJson(0,"参数错误",null);
            }
            int rows = 0;
            TypechoForum forum = service.selectByKey(id);
            if(forum==null){
                return Result.getResultJson(0,"帖子不存在",null);
            }

            if(type.equals(1)){
                //生成操作日志
                TypechoUserlog userlog = new TypechoUserlog();
                userlog.setUid(uid);
                userlog.setCid(id);
                userlog.setType("postMark");
                Integer isMark = userlogService.total(userlog);
                if(isMark > 0){
                    return Result.getResultJson(0,"你已收藏该帖子",null);
                }
                userlog.setCreated(Integer.parseInt(userTime));
                rows = userlogService.insert(userlog);
            }else{
                TypechoUserlog userlog = new TypechoUserlog();
                userlog.setUid(uid);
                userlog.setCid(id);
                userlog.setType("postMark");
                List<TypechoUserlog> list = userlogService.selectList(userlog);
                if(list.size() < 1){
                    return Result.getResultJson(0,"你还未收藏该帖子",null);
                }
                Integer logid = list.get(0).getId();
                rows = userlogService.delete(logid);
            }
            redisHelp.delete(this.dataprefix+"_"+"postInfo_"+id+"_"+uid,redisTemplate);
            JSONObject response = new JSONObject();
            response.put("code" ,rows > 0 ? 1: 0 );
            response.put("data" , rows);
            response.put("msg"  , rows > 0 ? "操作成功" : "操作失败");
            return response.toString();
        }catch (Exception e){
            e.printStackTrace();
            return Result.getResultJson(0,"接口请求异常，请联系管理员",null);
        }

    }
    /**
     * 帖子收藏列表
     */
    @RequestMapping(value = "/postMarkList")
    @ResponseBody
    @LoginRequired(purview = "0")
    public String postMarkList(@RequestParam(value = "page"        , required = false, defaultValue = "1") Integer page,
                             @RequestParam(value = "limit"       , required = false, defaultValue = "15") Integer limit,
                             @RequestParam(value = "token", required = false) String  token) {
        if(limit>50){
            limit = 50;
        }
        Integer total = 0;
        Integer uStatus = UStatus.getStatus(token,this.dataprefix,redisTemplate);
        Map map =redisHelp.getMapValue(this.dataprefix+"_"+"userInfo"+token,redisTemplate);
        Integer uid =Integer.parseInt(map.get("uid").toString());

        TypechoUserlog query = new TypechoUserlog();
        query.setUid(uid);
        query.setType("postMark");
        total = userlogService.total(query);

        List jsonList = new ArrayList();
        List cacheList = redisHelp.getList(this.dataprefix+"_"+"postMarkList_"+page+"_"+limit+"_"+uid,redisTemplate);
        try{
            if(cacheList.size()>0){
                jsonList = cacheList;
            }else {
                PageList<TypechoUserlog> pageList = userlogService.selectPage(query, page, limit);
                List<TypechoUserlog> list = pageList.getList();
                if(list.size() < 1){
                    JSONObject noData = new JSONObject();
                    noData.put("code" , 1);
                    noData.put("msg"  , "");
                    noData.put("data" , new ArrayList());
                    noData.put("count", 0);
                    noData.put("total", total);
                    return noData.toString();
                }
                for (int i = 0; i < list.size(); i++) {
                    Integer cid = list.get(i).getCid();
                    TypechoForum forum = service.selectByKey(cid);

                    if(forum!=null){
                        Map json = JSONObject.parseObject(JSONObject.toJSONString(forum), Map.class);

                        Integer userid = forum.getAuthorId();
                        //基本信息处理
                        List imgList = baseFull.getImageSrc(forum.getText());
                        json.put("images",imgList);
                        List<Map<String, String>> videoList = baseFull.getVideoInfo(forum.getText());
                        json.put("videos", videoList);
                        String text = forum.getText();
                        text = baseFull.toStrByChinese(text);
                        json.put("text",text.length()>200 ? text.substring(0,200)+"……" : text);
                        //获取用户信息
                        TypechoForumSection section = forumSectionService.selectByKey(forum.getSection());
                        if(section==null){
                            json.put("sectionJson",new HashMap<>());

                        }else{
                            Map sectionJson = JSONObject.parseObject(JSONObject.toJSONString(section), Map.class);
                            json.put("sectionJson",sectionJson);
                        }
                        Map userJson = UserStatus.getUserInfo(userid,apiconfigService,usersService);
                        //获取用户等级
//                        TypechoComments comments = new TypechoComments();
//                        comments.setAuthorId(userid);
//                        Integer lv = commentsService.total(comments,null);
//                        userJson.put("lv", baseFull.getLv(lv));
                        json.put("userJson",userJson);
                        if (uStatus != 0) {
                            TypechoFan fan = new TypechoFan();
                            fan.setUid(uid);
                            fan.setTouid(forum.getAuthorId());
                            Integer isFollow = fanService.total(fan);
                            json.put("isFollow",isFollow);

                            TypechoUserlog userlog = new TypechoUserlog();
                            userlog.setCid(forum.getId());
                            userlog.setType("postLike");
                            userlog.setUid(uid);
                            Integer isLikes = userlogService.total(userlog);
                            if(isLikes > 0){
                                json.put("isLikes",1);
                            }else{
                                json.put("isLikes",0);
                            }

                        }else{
                            json.put("isLikes",0);
                            json.put("isFollow",0);
                        }
                        List<identifyCompany> companyList = jdbcTemplate.query("SELECT * FROM `" + prefix + "_company` WHERE uid = " + userid + ";", new BeanPropertyRowMapper<>(identifyCompany.class));
                        List<identifyConsumer> consumerList = jdbcTemplate.query("SELECT * FROM `" + prefix + "_consumer` WHERE uid = " + userid + ";", new BeanPropertyRowMapper<>(identifyConsumer.class));
                        if (companyList.size() > 0) {
                            identifyCompany company = companyList.get(0);
                            if (company.getIdentifyStatus().equals("1")) {
                                userJson.put("lvrz", 1);
                            } else {
                                userJson.put("lvrz", -1);
                            }
                        } else {
                            userJson.put("lvrz", 0);
                        }
                        if (consumerList.size() > 0) {
                            identifyConsumer consumer = consumerList.get(0);
                            if (consumer.getIdentifyStatus().equals("1")) {
                                userJson.put("smrz", 1);
                            } else {
                                userJson.put("smrz", -1);
                            }
                        } else {
                            userJson.put("smrz", 0);
                        }
                        json.put("logid", list.get(i).getId());
                        jsonList.add(json);
                    }

                }
                redisHelp.delete(this.dataprefix+"_"+"postMarkList_"+page+"_"+limit+"_"+uid, redisTemplate);
                redisHelp.setList(this.dataprefix+"_"+"postMarkList_"+page+"_"+limit+"_"+uid, jsonList, 5, redisTemplate);
            }
        }catch (Exception e){
            if(cacheList.size()>0){
                jsonList = cacheList;
            }
        }
        JSONObject response = new JSONObject();
        response.put("code" , 1);
        response.put("msg"  , "");
        response.put("data" , null != jsonList ? jsonList : new JSONArray());
        response.put("count", jsonList.size());
        response.put("total", total);
        return response.toString();


    }
    /**
     * 帖子评论
     */
    @RequestMapping(value = "/postComments")
    @ResponseBody
    @LoginRequired(purview = "0")
    public String postComments(@RequestParam(value = "postid", required = false) Integer  postid,
                             @RequestParam(value = "text", required = false) String  text,
                               @RequestParam(value = "pic", required = false) String  pic,
                             @RequestParam(value = "token", required = false) String  token,
                             @RequestParam(value = "parent", required = false, defaultValue = "0") Integer parent,
                               @RequestParam(value = "verifyCode", required = false) String verifyCode,
                             HttpServletRequest request) {
        try{
            String  ip = baseFull.getIpAddr(request);

            if(text.length()<1){
                if(pic.length()<1){
                    return Result.getResultJson(0,"评论不能为空",null);
                }else{
                    text = "[图片]";
                }
            }else{
                if(text.length()>1500){
                    return Result.getResultJson(0,"超出最大评论长度",null);
                }
            }

            Map map =redisHelp.getMapValue(this.dataprefix+"_"+"userInfo"+token,redisTemplate);
            Integer uid =Integer.parseInt(map.get("uid").toString());

            String isSilence = redisHelp.getRedis(this.dataprefix+"_"+uid+"_silence",redisTemplate);
            if(isSilence!=null){
                return Result.getResultJson(0,"你的操作太频繁了，请稍后再试",null);
            }
            TypechoApiconfig apiconfig = UStatus.getConfig(this.dataprefix,apiconfigService,redisTemplate);
            if(apiconfig.getVerifyLevel()>1){
                if (StringUtils.isEmpty(verifyCode)) {
                    return Result.getResultJson(0,"图片验证码不能为空",null);
                }
                String kaptchaCode = redisHelp.getRedis(this.dataprefix+"_"+ip+"_verifyCode",redisTemplate);
                if (StringUtils.isEmpty(kaptchaCode) || !verifyCode.equals(kaptchaCode)) {
                    return Result.getResultJson(0,"图片验证码错误",null);
                }
            }
            //登录情况下，刷数据攻击拦截
            if(apiconfig.getBanRobots().equals(1)) {
                String isRepeated = redisHelp.getRedis(this.dataprefix+"_"+uid+"_isForumPost",redisTemplate);
                if(isRepeated==null){
                    redisHelp.setRedis(this.dataprefix+"_"+uid+"_isForumPost","1",4,redisTemplate);
                }else{
                    Integer frequency = Integer.parseInt(isRepeated) + 1;
                    if(frequency==4){
                        securityService.safetyMessage("用户ID："+uid+"，在帖子评论接口疑似存在攻击行为，请及时确认处理。","system");
                        redisHelp.setRedis(this.dataprefix+"_"+uid+"_silence","1",apiconfig.getSilenceTime(),redisTemplate);
                        return Result.getResultJson(0,"你的操作过于频繁，已被禁言十分钟！",null);
                    }else{
                        redisHelp.setRedis(this.dataprefix+"_"+uid+"_isForumPost",frequency.toString(),5,redisTemplate);
                    }
                    return Result.getResultJson(0,"你的操作太频繁了",null);
                }
            }
            //违禁词拦截
            String forbidden = apiconfig.getForbidden();
            Integer intercept = 0;
            Integer isForbidden = baseFull.getForbidden(forbidden,text);
            if(isForbidden.equals(1)){
                intercept = 1;
            }
            if(intercept.equals(1)){
                //以十分钟为检测周期，违禁一次刷新一次，等于4次则禁言
                String isIntercept = redisHelp.getRedis(this.dataprefix+"_"+uid+"_isIntercept",redisTemplate);
                if(isIntercept==null){
                    redisHelp.setRedis(this.dataprefix+"_"+uid+"_isIntercept","1",600,redisTemplate);
                }else{
                    Integer frequency = Integer.parseInt(isIntercept) + 1;
                    if(frequency==4){
                        securityService.safetyMessage("用户ID："+uid+"，在帖子评论接口多次触发违禁，请及时确认处理。","system");
                        redisHelp.setRedis(this.dataprefix+"_"+uid+"_silence","1",apiconfig.getInterceptTime(),redisTemplate);
                        return Result.getResultJson(0,"你已多次发送违禁词，被禁言一小时！",null);
                    }else{
                        redisHelp.setRedis(this.dataprefix+"_"+uid+"_isIntercept",frequency.toString(),600,redisTemplate);
                    }

                }
                return Result.getResultJson(0,"内容存在违禁词",null);
            }
            //违禁词拦截结束
            TypechoForum forum = service.selectByKey(postid);
            if(forum==null){
                return Result.getResultJson(0,"帖子不存在",null);
            }
            if(forum.getStatus().equals(2)){
                return Result.getResultJson(0,"帖子已锁定，禁止评论",null);
            }
            if(forum.getStatus().equals(0)){
                return Result.getResultJson(0,"帖子未审核通过，无法评论",null);
            }
            TypechoForumComment pcomment = new TypechoForumComment();
            if(!parent.equals(0)){
                pcomment = forumCommentService.selectByKey(parent);
                if(pcomment==null){
                    return Result.getResultJson(0,"评论不存在",null);
                }
            }
            text = text.replace("||rn||","\r\n");
            //腾讯云内容违规检测
            if(apiconfig.getCmsSwitch().equals(1)||apiconfig.getCmsSwitch().equals(3)){
                try{
                    String setText = baseFull.htmlToText(text);
                    setText = baseFull.encrypt(setText);
                    Map violationData = securityService.textViolation(setText);
                    String Suggestion = violationData.get("Suggestion").toString();
                    if(Suggestion.equals("Block")){
                        return Result.getResultJson(0,"内容涉及违规，请检查后重新提交！",null);
                    }
                    if(Suggestion.equals("Review")){
                        return Result.getResultJson(0,"内容涉及违规，请检查后重新提交！",null);
                    }
                }catch (Exception e){
                    e.printStackTrace();
                }
            }
            Long date = System.currentTimeMillis();
            String created = String.valueOf(date).substring(0,10);
            TypechoForumComment comment = new TypechoForumComment();
            comment.setCreated(Integer.parseInt(created));
            comment.setUid(uid);
            comment.setText(text);
            comment.setPic(pic);
            comment.setParent(parent);
            comment.setForumid(postid);
            comment.setSection(forum.getSection());
            if (apiconfig.getForumReplyAudit().equals(1)) {
                comment.setStatus(0);
            } else {
                comment.setStatus(1);
            }
            //修改用户最新发布时间
            TypechoUsers user = new TypechoUsers();
            user.setUid(uid);
            user.setIp(ip);
            user.setPosttime(Integer.parseInt(created));
            usersService.update(user);
            int rows =  forumCommentService.insert(comment);
            if (apiconfig.getForumReplyAudit().equals(0)) {
                //如果无需审核，则修改帖子评论数量和最新回复时间
                TypechoForumComment oldcomment = new TypechoForumComment();
                oldcomment.setForumid(postid);
                oldcomment.setStatus(1);
                Integer commentsNum = forumCommentService.total(oldcomment,null);
                TypechoForum newForum = new TypechoForum();
                newForum.setId(postid);
                newForum.setReplyTime(Integer.parseInt(created));
                newForum.setCommentsNum(commentsNum);
                service.update(newForum);

                //发送消息通知
                String title = apiconfig.getWebinfoTitle();
                Integer isPush = apiconfig.getIsPush();
                if(!parent.equals(0)){

                    if(!pcomment.getUid().equals(uid)){
                        TypechoInbox inbox = new TypechoInbox();
                        inbox.setUid(uid);
                        inbox.setTouid(pcomment.getUid());
                        inbox.setType("postComment");
                        inbox.setText(text);
                        inbox.setValue(forum.getId());
                        inbox.setCreated(Integer.parseInt(created));
                        inbox.setCid(comment.getId());
                        inboxService.insert(inbox);
                        if(isPush.equals(1)) {
                            TypechoUsers pushUser = usersService.selectByKey(pcomment.getUid());
                            String chatUserName;
                            TypechoUsers chatuser = usersService.selectByKey(uid);
                            if(chatuser.getScreenName()==null || chatuser.getScreenName().isEmpty()){
                                chatUserName = chatuser.getName();
                            }else{
                                chatUserName = chatuser.getScreenName();
                            }
                            if(pushUser!=null){
                                if(pushUser.getClientId()!=null){
                                    try {
                                        pushService.sendPushMsg(pushUser.getClientId(),title+" - "+chatUserName,"回复了你："+text,"payload","comment:"+forum.getId());
                                    }catch (Exception e){
                                        System.err.println("通知发送失败");
                                        e.printStackTrace();
                                    }

                                }
                            }else{
                                System.out.println("上级评论用户不存在");
                            }

                        }
                    }


                }else{
                    if(!forum.getAuthorId().equals(uid)){
                        TypechoInbox inbox = new TypechoInbox();
                        inbox.setUid(uid);
                        inbox.setTouid(forum.getAuthorId());
                        inbox.setType("postComment");
                        inbox.setText(text);
                        inbox.setValue(forum.getId());
                        inbox.setCreated(Integer.parseInt(created));
                        inbox.setCid(comment.getId());
                        inboxService.insert(inbox);
                        if(isPush.equals(1)) {
                            TypechoUsers pushUser = usersService.selectByKey(forum.getAuthorId());
                            String chatUserName;
                            TypechoUsers chatuser = usersService.selectByKey(uid);
                            if(chatuser.getScreenName()==null || chatuser.getScreenName().isEmpty()){
                                chatUserName = chatuser.getName();
                            }else{
                                chatUserName = chatuser.getScreenName();
                            }
                            if(pushUser!=null){
                                if(pushUser.getClientId()!=null){
                                    try {
                                        pushService.sendPushMsg(pushUser.getClientId(),title+" - "+chatUserName,"评论了你："+text,"payload","comment:"+forum.getId());
                                    }catch (Exception e){
                                        System.err.println("通知发送失败");
                                        e.printStackTrace();
                                    }

                                }
                            }else{
                                System.out.println("所属帖子用户不存在");
                            }

                        }
                    }

                }
            }
            if (apiconfig.getForumReplyAudit().equals(1)) {
                editFile.setLog("用户"+uid+"发布了新帖子评论。");
                if(rows > 0){
                    redisHelp.deleteKeysWithPattern("*"+this.dataprefix+"_postCommentList_1*",redisTemplate,this.dataprefix);
                }
                JSONObject response = new JSONObject();
                response.put("code" , rows);
                response.put("msg"  , rows > 0 ? "请等待管理员审核" : "发布失败");
                return response.toString();
            }
            editFile.setLog("用户"+uid+"发布了新帖子评论。");
            if(rows > 0){
                redisHelp.deleteKeysWithPattern("*"+this.dataprefix+"_postCommentList_1*",redisTemplate,this.dataprefix);
            }
            JSONObject response = new JSONObject();
            response.put("code" , rows);
            response.put("msg"  , rows > 0 ? "发布成功" : "发布失败");
            return response.toString();
        }catch (Exception e){
            e.printStackTrace();
            return Result.getResultJson(0,"接口请求异常，请联系管理员",null);
        }

    }
    /**
     * 帖子评论删除
     */
    @RequestMapping(value = "/postCommentDelete")
    @ResponseBody
    @LoginRequired(purview = "0")
    public String postCommentDelete(@RequestParam(value = "id", required = false) Integer  id,
                                  @RequestParam(value = "token", required = false) String  token) {
        try{
            Map map =redisHelp.getMapValue(this.dataprefix+"_"+"userInfo"+token,redisTemplate);
            Integer uid  = Integer.parseInt(map.get("uid").toString());
            String group = map.get("group").toString();
            TypechoForumComment comment = forumCommentService.selectByKey(id);
            if(comment==null){
                return Result.getResultJson(0,"评论不存在",null);
            }
            Integer sectionId = comment.getSection();
            Integer forumPermissions = UStatus.forumPermissions(map,forumModeratorService,2,sectionId);
            if(forumPermissions.equals(0)){
                return Result.getResultJson(0,"你没有操作权限",null);
            }
            int rows = forumCommentService.delete(id);

            Long date = System.currentTimeMillis();
            String created = String.valueOf(date).substring(0,10);
            TypechoInbox insert = new TypechoInbox();
            insert.setUid(uid);
            insert.setTouid(comment.getUid());
            insert.setType("system");
            insert.setText("你的评论【"+comment.getText()+"】已被删除");
            insert.setCreated(Integer.parseInt(created));
            inboxService.insert(insert);

            //修改帖子评论数量
            TypechoForumComment oldcomment = new TypechoForumComment();
            oldcomment.setForumid(comment.getForumid());
            oldcomment.setStatus(1);
            Integer commentsNum = forumCommentService.total(oldcomment,null);
            TypechoForum newForum = new TypechoForum();
            newForum.setId(comment.getForumid());
            newForum.setCommentsNum(commentsNum);
            service.update(newForum);

            JSONObject response = new JSONObject();
            if(rows > 0){
                redisHelp.deleteKeysWithPattern("*"+this.dataprefix+"_postCommentList_1*",redisTemplate,this.dataprefix);
            }
            response.put("code" ,rows > 0 ? 1: 0 );
            response.put("data" , rows);
            response.put("msg"  , rows > 0 ? "操作成功" : "操作失败");
            return response.toString();
        }catch (Exception e){
            e.printStackTrace();
            return Result.getResultJson(0,"接口请求异常，请联系管理员",null);
        }

    }

    /**
     * 帖子评论审核
     */
    @RequestMapping(value = "/postCommentReview")
    @ResponseBody
    @LoginRequired(purview = "0")
    public String spaceReview(@RequestParam(value = "id", required = false) Integer  id,
                              @RequestParam(value = "postid", required = false) Integer  postid,
                              @RequestParam(value = "type", required = false, defaultValue = "1") Integer  type,
                              @RequestParam(value = "token", required = false) String  token) {
        try{
            if(!type.equals(1)&&!type.equals(0)){
                return Result.getResultJson(0,"参数错误",null);
            }
            Map map =redisHelp.getMapValue(this.dataprefix+"_"+"userInfo"+token,redisTemplate);
            Integer uid  = Integer.parseInt(map.get("uid").toString());

            TypechoApiconfig apiconfig = UStatus.getConfig(this.dataprefix,apiconfigService,redisTemplate);
            TypechoForumComment comment = forumCommentService.selectByKey(id);
            if(comment==null){
                return Result.getResultJson(0,"评论不存在",null);
            }
            if(comment.getStatus().equals(type)){
                return Result.getResultJson(0,"评论已被进行相同操作",null);
            }
            TypechoForum forum   = service.selectByKey(comment.getForumid());
            if(forum==null){
                return Result.getResultJson(0,"所属帖子不存在",null);
            }
            Integer forumPermissions = UStatus.forumPermissions(map,forumModeratorService,1,comment.getSection());
            if(forumPermissions.equals(0)){
                return Result.getResultJson(0,"你没有操作权限",null);
            }
            int rows = 0;
            Long date = System.currentTimeMillis();
            String created = String.valueOf(date).substring(0,10);
            TypechoInbox insert = new TypechoInbox();
            if(type.equals(1)){
                TypechoForumComment newComment = new TypechoForumComment();
                newComment.setUid(comment.getUid());
                newComment.setId(id);
                newComment.setStatus(type);
                rows = forumCommentService.update(newComment);
                //修改帖子评论数量
                TypechoForumComment oldcomment = new TypechoForumComment();
                oldcomment.setForumid(comment.getForumid());
                oldcomment.setStatus(1);
                Integer commentsNum = forumCommentService.total(oldcomment,null);
                TypechoForum newForum = new TypechoForum();
                newForum.setId(comment.getForumid());
                newForum.setCommentsNum(commentsNum);
                service.update(newForum);

            }else{
                rows = forumCommentService.delete(id);
            }
            insert.setUid(uid);
            insert.setTouid(comment.getUid());
            insert.setType("system");
            if(type.equals(1)){
                insert.setText("你的评论已审核通过");
            }
            if(type.equals(0)){
                insert.setText("你的评论未审核通过，已被删除");
            }
            insert.setCreated(Integer.parseInt(created));
            inboxService.insert(insert);

            if(type.equals(1)) {
                TypechoForumComment oldcomment = new TypechoForumComment();
                oldcomment.setForumid(postid);
                oldcomment.setStatus(1);
                Integer commentsNum = forumCommentService.total(oldcomment,null);
                TypechoForum newForum = new TypechoForum();
                newForum.setId(postid);
                newForum.setReplyTime(Integer.parseInt(created));
                newForum.setCommentsNum(commentsNum);
                service.update(newForum);
                //发送消息通知
                String title = apiconfig.getWebinfoTitle();
                Integer isPush = apiconfig.getIsPush();
                if(!comment.getParent().equals(0)){
                    TypechoForumComment pcomment = forumCommentService.selectByKey(comment.getParent());
                    if(pcomment!=null){
                        if(!pcomment.getUid().equals(uid)){
                            TypechoInbox inbox = new TypechoInbox();
                            inbox.setUid(uid);
                            inbox.setTouid(pcomment.getUid());
                            inbox.setType("postComment");
                            inbox.setText(comment.getText());
                            inbox.setValue(forum.getId());
                            inbox.setCreated(Integer.parseInt(created));
                            inbox.setCid(comment.getId());
                            inboxService.insert(inbox);
                            if(isPush.equals(1)) {
                                TypechoUsers user = new TypechoUsers();
                                TypechoUsers pushUser = usersService.selectByKey(pcomment.getUid());
                                String chatUserName;
                                TypechoUsers chatuser = usersService.selectByKey(uid);
                                if(chatuser.getScreenName()==null || chatuser.getScreenName().isEmpty()){
                                    chatUserName = chatuser.getName();
                                }else{
                                    chatUserName = chatuser.getScreenName();
                                }
                                if(pushUser!=null){
                                    if(pushUser.getClientId()!=null){
                                        try {
                                            pushService.sendPushMsg(pushUser.getClientId(),title+" - "+chatUserName,"回复了你："+comment.getText(),"payload","comment:"+forum.getId());
                                        }catch (Exception e){
                                            System.err.println("通知发送失败");
                                            e.printStackTrace();
                                        }

                                    }
                                }else{
                                    System.out.println("上级评论用户不存在");
                                }
                            }
                        }
                    }else{
                        System.out.println("上级评论不存在");
                    }



                }else{
                    if(!forum.getAuthorId().equals(uid)){
                        TypechoInbox inbox = new TypechoInbox();
                        inbox.setUid(uid);
                        inbox.setTouid(forum.getAuthorId());
                        inbox.setType("postComment");
                        inbox.setText(comment.getText());
                        inbox.setValue(forum.getId());
                        inbox.setCreated(Integer.parseInt(created));
                        inbox.setCid(comment.getId());
                        inboxService.insert(inbox);
                        if(isPush.equals(1)) {
                            TypechoUsers user = new TypechoUsers();
                            TypechoUsers pushUser = usersService.selectByKey(forum.getAuthorId());
                            String chatUserName;
                            TypechoUsers chatuser = usersService.selectByKey(uid);
                            if(chatuser.getScreenName()==null || chatuser.getScreenName().isEmpty()){
                                chatUserName = chatuser.getName();
                            }else{
                                chatUserName = chatuser.getScreenName();
                            }
                            if(pushUser!=null){
                                if(pushUser.getClientId()!=null){
                                    try {
                                        pushService.sendPushMsg(pushUser.getClientId(),title+" - "+chatUserName,"评论了你："+comment.getText(),"payload","comment:"+forum.getId());
                                    }catch (Exception e){
                                        System.err.println("通知发送失败");
                                        e.printStackTrace();
                                    }

                                }
                            }else{
                                System.out.println("所属帖子用户不存在");
                            }

                        }
                    }

                }
            }
            if(rows > 0){
                redisHelp.deleteKeysWithPattern("*"+this.dataprefix+"_postCommentList_1*",redisTemplate,this.dataprefix);
            }
            JSONObject response = new JSONObject();
            response.put("code" ,rows > 0 ? 1: 0 );
            response.put("data" , rows);
            response.put("msg"  , rows > 0 ? "操作成功，请等待缓存刷新" : "操作失败");
            return response.toString();
        }catch (Exception e){
            e.printStackTrace();
            return Result.getResultJson(0,"接口请求异常，请联系管理员",null);
        }
    }

    /**
     * 帖子评论点赞
     */
    @RequestMapping(value = "/postCommentLike")
    @ResponseBody
    @LoginRequired(purview = "0")
    public String postCommentLike(@RequestParam(value = "id", required = false) Integer  id,
                             @RequestParam(value = "token", required = false) String  token) {
        try{
            Map map =redisHelp.getMapValue(this.dataprefix+"_"+"userInfo"+token,redisTemplate);
            Integer uid  = Integer.parseInt(map.get("uid").toString());
            Long date = System.currentTimeMillis();
            String userTime = String.valueOf(date).substring(0,10);

            //生成操作日志
            TypechoUserlog userlog = new TypechoUserlog();
            userlog.setUid(uid);
            userlog.setCid(id);
            userlog.setType("postCommentLike");
            Integer isLikes = userlogService.total(userlog);
            if(isLikes>0){
                return Result.getResultJson(0,"你已经点赞过了",null);
            }
            TypechoForumComment comment = forumCommentService.selectByKey(id);
            if(comment==null){
                return Result.getResultJson(0,"该帖子评论不存在",null);
            }

            userlog.setCreated(Integer.parseInt(userTime));
            userlogService.insert(userlog);
            Integer likes = comment.getLikes();
            likes = likes + 1;
            TypechoForumComment newComment = new TypechoForumComment();
            newComment.setLikes(likes);
            newComment.setId(id);
            int rows = forumCommentService.update(newComment);
            JSONObject response = new JSONObject();
            response.put("code" ,rows > 0 ? 1: 0 );
            response.put("data" , rows);
            response.put("msg"  , rows > 0 ? "点赞成功" : "点赞失败");
            return response.toString();
        }catch (Exception e){
            e.printStackTrace();
            return Result.getResultJson(0,"接口请求异常，请联系管理员",null);
        }

    }
    /**
     * 评论列表
     */
    @RequestMapping(value = "/postCommentList")
    @ResponseBody
    @LoginRequired(purview = "-1")
    public String postCommentList (
            @RequestParam(value = "searchParams", required = false) String  searchParams,
            @RequestParam(value = "page"        , required = false, defaultValue = "1") Integer page,
            @RequestParam(value = "limit"       , required = false, defaultValue = "15") Integer limit,
            @RequestParam(value = "searchKey"        , required = false, defaultValue = "") String searchKey,
            @RequestParam(value = "order", required = false, defaultValue = "created") String  order,
            @RequestParam(value = "token", required = false) String  token) {

        if(limit>50){
            limit = 50;
        }
        String sqlParams = "null";
        //如果开启全局登录，则必须登录才能得到数据
        Integer uStatus = UStatus.getStatus(token,this.dataprefix,redisTemplate);
        TypechoApiconfig apiconfig = UStatus.getConfig(this.dataprefix,apiconfigService,redisTemplate);
        if(apiconfig.getIsLogin().equals(1)){
            if(uStatus==0){
                return Result.getResultJson(0,"用户未登录或Token验证失败",null);
            }
        }
        //验证结束
        Map map = new HashMap();
        Integer uid = 0;
        String group = "";

        TypechoForumComment query = new TypechoForumComment();

        if (StringUtils.isNotBlank(searchParams)) {
            JSONObject object = JSON.parseObject(searchParams);
            query = object.toJavaObject(TypechoForumComment.class);
            Map paramsJson = JSONObject.parseObject(JSONObject.toJSONString(query), Map.class);
            sqlParams = paramsJson.toString();

        }
        if (uStatus != 0) {
            map = redisHelp.getMapValue(this.dataprefix + "_" + "userInfo" + token, redisTemplate);
            uid =Integer.parseInt(map.get("uid").toString());
            group = map.get("group").toString();
            if(!group.equals("administrator")&&!group.equals("editor")){
                Integer forumPermissions = UStatus.forumPermissions(map,forumModeratorService,5,0);
                if(forumPermissions.equals(0)){
                    query.setStatus(1);
                }
                //非管理员和编辑只能查询审核通过的评论
            }
        }
        if (uStatus == 0) {
            query.setStatus(1);
            //未登录只能获取通过的评论
        }
        List cacheList =  redisHelp.getList(this.dataprefix+"_"+"postCommentList_"+page+"_"+limit+"_"+searchKey+"_"+uid+"_"+order+"_"+sqlParams,redisTemplate);
        List jsonList = new ArrayList();

        Integer total = forumCommentService.total(query,searchKey);
        try{
            if(cacheList.size()>0){
                jsonList = cacheList;
            }else{
                PageList<TypechoForumComment> pageList = forumCommentService.selectPage(query, page, limit,order,searchKey);
                List<TypechoForumComment> list = pageList.getList();
                if(list.size() < 1){
                    JSONObject noData = new JSONObject();
                    noData.put("code" , 1);
                    noData.put("msg"  , "");
                    noData.put("data" , new ArrayList());
                    noData.put("count", 0);
                    noData.put("total", total);
                    return noData.toString();
                }
                for (int i = 0; i < list.size(); i++) {
                    Map json = JSONObject.parseObject(JSONObject.toJSONString(list.get(i)), Map.class);
                    TypechoForumComment comment = list.get(i);
                    Integer userid = comment.getUid();
                    //获取上级评论
                    Integer parent = comment.getParent();
                    if(parent > 0){
                        Map parentJson = new HashMap();
                        TypechoForumComment parentComment = forumCommentService.selectByKey(parent);
                        if(parentComment==null){
                            parentJson.put("id",parent);
                            parentJson.put("text","评论已删除");
                            parentJson.put("username","");
                        }else{
                            parentJson.put("id",parent);
                            parentJson.put("text",parentComment.getText());
                            parentJson.put("pic",parentComment.getPic());
                            Integer parentUid = parentComment.getUid();
                            TypechoUsers parentUser = usersService.selectByKey(parentUid);
                            String name = parentUser.getName();
                            if(parentUser.getScreenName()!=null&&parentUser.getScreenName()!=""){
                                name = parentUser.getScreenName();
                            }
                            parentJson.put("username",name);
                        }
                        json.put("parentJson",parentJson);
                    }
                    //获取上级帖子
                    Map postJson = new HashMap();
                    Integer forumid = comment.getForumid();
                    TypechoForum postInfo = service.selectByKey(forumid);
                    if(postInfo==null){
                        postJson.put("id",forumid);
                        postJson.put("title","评论已删除");
                    }else{
                        postJson.put("id",forumid);
                        postJson.put("title",postInfo.getTitle());
                    }
                    json.put("postJson",postJson);
                    //获取用户信息
                    Map userJson = UserStatus.getUserInfo(userid,apiconfigService,usersService);
                    //获取用户等级
//                    TypechoComments comments = new TypechoComments();
//                    comments.setAuthorId(userid);
//                    Integer lv = commentsService.total(comments,null);
//                    userJson.put("lv", baseFull.getLv(lv));
                    json.put("userJson",userJson);
                    if (uStatus != 0) {

                        TypechoUserlog userlog = new TypechoUserlog();
                        userlog.setCid(comment.getId());
                        userlog.setType("postCommentLike");
                        userlog.setUid(uid);
                        Integer isLikes = userlogService.total(userlog);
                        if(isLikes > 0){
                            json.put("isLikes",1);
                        }else{
                            json.put("isLikes",0);
                        }

                    }else{
                        json.put("isLikes",0);
                    }

                    jsonList.add(json);
                }
                redisHelp.delete(this.dataprefix+"_"+"postCommentList_"+page+"_"+limit+"_"+searchKey+"_"+uid+"_"+order+"_"+sqlParams,redisTemplate);
                redisHelp.setList(this.dataprefix+"_"+"postCommentList_"+page+"_"+limit+"_"+searchKey+"_"+uid+"_"+order+"_"+sqlParams,jsonList,10,redisTemplate);

            }
        }catch (Exception e){
            e.printStackTrace();
            if(cacheList.size()>0){
                jsonList = cacheList;
            }
        }
        JSONObject response = new JSONObject();
        response.put("code" , 1);
        response.put("msg"  , "");
        response.put("data" , jsonList);
        response.put("count", jsonList.size());
        response.put("total", total);
        return response.toString();
    }
    /**
     * 版块列表
     */
    @RequestMapping(value = "/sectionList")
    @ResponseBody
    @LoginRequired(purview = "-1")
    public String sectionList (
            @RequestParam(value = "searchParams", required = false) String  searchParams,
            @RequestParam(value = "page"        , required = false, defaultValue = "1") Integer page,
            @RequestParam(value = "limit"       , required = false, defaultValue = "15") Integer limit,
            @RequestParam(value = "searchKey"        , required = false, defaultValue = "") String searchKey,
            @RequestParam(value = "order", required = false, defaultValue = "order") String  order,
            @RequestParam(value = "token", required = false) String  token) {

        if(limit>50){
            limit = 50;
        }
        TypechoForumSection query = new TypechoForumSection();
        if (StringUtils.isNotBlank(searchParams)) {
            JSONObject object = JSON.parseObject(searchParams);
            query = object.toJavaObject(TypechoForumSection.class);

        }
        Map map = new HashMap();
        Integer uid = 0;
        Integer cacheTime = 20;
        //如果开启全局登录，则必须登录才能得到数据
        Integer uStatus = UStatus.getStatus(token,this.dataprefix,redisTemplate);
        TypechoApiconfig apiconfig = UStatus.getConfig(this.dataprefix,apiconfigService,redisTemplate);
        if(apiconfig.getIsLogin().equals(1)){
            if(uStatus==0){
                return Result.getResultJson(0,"用户未登录或Token验证失败",null);
            }
        }
        //验证结束
        if (uStatus != 0) {
            map = redisHelp.getMapValue(this.dataprefix + "_" + "userInfo" + token, redisTemplate);
            uid =Integer.parseInt(map.get("uid").toString());
            cacheTime = 5;
        }
        List cacheList =  redisHelp.getList(this.dataprefix+"_"+"sectionList_"+page+"_"+limit+"_"+uid+"_"+searchParams,redisTemplate);
        List jsonList = new ArrayList();

        Integer total = forumSectionService.total(query);
        try{
            if(cacheList.size()>0){
                jsonList = cacheList;
            }else{
                PageList<TypechoForumSection> pageList = forumSectionService.selectPage(query, page, limit,order,searchKey);
                List<TypechoForumSection> list = pageList.getList();
                if(list.size() < 1){
                    JSONObject noData = new JSONObject();
                    noData.put("code" , 1);
                    noData.put("msg"  , "");
                    noData.put("data" , new ArrayList());
                    noData.put("count", 0);
                    noData.put("total", total);
                    return noData.toString();
                }
                for (int i = 0; i < list.size(); i++) {
                    Map json = JSONObject.parseObject(JSONObject.toJSONString(list.get(i)), Map.class);

                    TypechoForum forum = new TypechoForum();
                    Integer sectionId = list.get(i).getId();
                    forum.setSection(sectionId);
                    Integer postNum = service.total(forum,null);
                    json.put("postNum",postNum);
                    //中间要加上用户是否关注
                    TypechoUserlog log = new TypechoUserlog();
                    log.setType("sectionFollow");

                    log.setCid(sectionId);
                    Integer followNum = userlogService.total(log);
                    json.put("followNum",followNum);
                    log.setUid(uid);
                    List<TypechoUserlog> userlogList = userlogService.selectList(log);
                    if(userlogList.size()>0){
                        json.put("isFollow",1);
                        json.put("sectionExp",userlogList.get(0).getNum());
                    }else{
                        json.put("isFollow",0);
                        json.put("sectionExp",0);
                    }

                    jsonList.add(json);
                }
                redisHelp.delete(this.dataprefix+"_"+"sectionList_"+page+"_"+limit+"_"+uid+"_"+searchParams,redisTemplate);
                redisHelp.setList(this.dataprefix+"_"+"sectionList_"+page+"_"+limit+"_"+uid+"_"+searchParams,jsonList,cacheTime,redisTemplate);

            }
        }catch (Exception e){
            e.printStackTrace();
            if(cacheList.size()>0){
                jsonList = cacheList;
            }
        }
        JSONObject response = new JSONObject();
        response.put("code" , 1);
        response.put("msg"  , "");
        response.put("data" , jsonList);
        response.put("count", jsonList.size());
        response.put("total", total);
        return response.toString();
    }
    /**
     * 新增版块
     */
    @RequestMapping(value = "/addSection")
    @ResponseBody
    @LoginRequired(purview = "0")
    public String addSection(@RequestParam(value = "name", required = false, defaultValue = "") String  name,
                               @RequestParam(value = "pic", required = false) String  pic,
                               @RequestParam(value = "bg", required = false) String  bg,
                               @RequestParam(value = "type", required = false, defaultValue = "section") String  type,
                               @RequestParam(value = "text", required = false, defaultValue = "") String  text,
                               @RequestParam(value = "slug", required = false, defaultValue = "") String  slug,
                               @RequestParam(value = "parent", required = false, defaultValue = "0") Integer parent,
                               @RequestParam(value = "restrict", required = false, defaultValue = "0") Integer restrict,
                               @RequestParam(value = "order", required = false, defaultValue = "0") Integer order,
                               @RequestParam(value = "token", required = false) String  token) {
        try {
            if(name.length() < 1){
                return Result.getResultJson(0,"请输入名称",null);
            }
            Map map =redisHelp.getMapValue(this.dataprefix+"_"+"userInfo"+token,redisTemplate);
            Integer uid  = Integer.parseInt(map.get("uid").toString());

            Integer forumPermissions = UStatus.forumPermissions(map,forumModeratorService,5,0);
            if(forumPermissions.equals(0)){
                return Result.getResultJson(0,"你没有操作权限",null);
            }
            if(!type.equals("sort")&&!type.equals("section")){
                return Result.getResultJson(0,"参数错误",null);
            }
            TypechoForumSection section = new TypechoForumSection();
            section.setName(name);
            section.setPic(pic);
            section.setBg(bg);
            section.setText(text);
            section.setParent(parent);
            section.setRestrictKey(restrict);
            section.setType(type);
            section.setOrderKey(order);
            section.setSlug(slug);

            int rows =  forumSectionService.insert(section);
            editFile.setLog("管理员"+uid+"创建了新板块。");
            JSONObject response = new JSONObject();
            response.put("code" , rows);
            response.put("msg"  , rows > 0 ? "创建成功" : "创建失败");
            redisHelp.deleteKeysWithPattern("*"+this.dataprefix+"_sectionList_1*",redisTemplate,this.dataprefix);
            return response.toString();
        }catch (Exception e){
            e.printStackTrace();
            return Result.getResultJson(0,"接口请求异常，请联系管理员",null);
        }
    }

    /**
     * 编辑版块
     */
    @RequestMapping(value = "/editSection")
    @ResponseBody
    @LoginRequired(purview = "0")
    public String editSection(
            @RequestParam(value = "id", required = false) Integer  id,
            @RequestParam(value = "name", required = false) String  name,
            @RequestParam(value = "pic", required = false) String  pic,
            @RequestParam(value = "bg", required = false) String  bg,
            @RequestParam(value = "text", required = false, defaultValue = "") String  text,
            @RequestParam(value = "slug", required = false, defaultValue = "") String  slug,
            @RequestParam(value = "parent", required = false, defaultValue = "0") Integer parent,
            @RequestParam(value = "restrict", required = false, defaultValue = "0") Integer restrict,
            @RequestParam(value = "order", required = false, defaultValue = "0") Integer order,
            @RequestParam(value = "token", required = false) String  token) {
        try {
            Map map =redisHelp.getMapValue(this.dataprefix+"_"+"userInfo"+token,redisTemplate);
            Integer uid  = Integer.parseInt(map.get("uid").toString());
            Integer forumPermissions = UStatus.forumPermissions(map,forumModeratorService,5,0);
            if(forumPermissions.equals(0)){
                return Result.getResultJson(0,"你没有操作权限",null);
            }
            TypechoForumSection oldSection = forumSectionService.selectByKey(id);
            if(oldSection==null){
                return Result.getResultJson(0,"板块不存在",null);
            }
            TypechoForumSection section = new TypechoForumSection();
            section.setId(id);
            section.setName(name);
            section.setPic(pic);
            section.setBg(bg);
            section.setText(text);
            section.setParent(parent);
            section.setRestrictKey(restrict);
            section.setOrderKey(order);
            section.setSlug(slug);
            int rows =  forumSectionService.update(section);
            redisHelp.delete(this.dataprefix+"_"+"sectionInfoJson_"+id,redisTemplate);
            editFile.setLog("管理员"+uid+"修改了板块"+id);
            JSONObject response = new JSONObject();
            response.put("code" , rows);
            response.put("msg"  , rows > 0 ? "保存成功" : "保存失败");
            redisHelp.deleteKeysWithPattern("*"+this.dataprefix+"_sectionList_1*",redisTemplate,this.dataprefix);
            return response.toString();
        }catch (Exception e){
            e.printStackTrace();
            return Result.getResultJson(0,"接口请求异常，请联系管理员",null);
        }
    }
    /**
     * 删除版块
     */
    @RequestMapping(value = "/deleteSection")
    @ResponseBody
    @LoginRequired(purview = "0")
    public String deleteSection(@RequestParam(value = "id", required = false) Integer  id,
                                @RequestParam(value = "token", required = false) String  token) {
        try{
            Map map =redisHelp.getMapValue(this.dataprefix+"_"+"userInfo"+token,redisTemplate);
            Integer loguid  = Integer.parseInt(map.get("uid").toString());
            Integer forumPermissions = UStatus.forumPermissions(map,forumModeratorService,5,0);
            if(forumPermissions.equals(0)){
                return Result.getResultJson(0,"你没有操作权限",null);
            }
            TypechoForumSection section = forumSectionService.selectByKey(id);
            if(section == null){
                return Result.getResultJson(0,"该数据不存在",null);
            }
            String type = section.getType();

            //如果是大类，版块变为自由版块，如果为版块，所有帖子都会删除。
            if(type.equals("sort")){
                jdbcTemplate.execute("UPDATE "+this.prefix+"_forum_section SET parent = 0 WHERE parent="+id+";");
            }
            if(type.equals("section")){
                jdbcTemplate.execute("DELETE FROM "+this.prefix+"_forum WHERE section = "+id+";");
            }
            int rows =  forumSectionService.delete(id);
            redisHelp.delete(this.dataprefix+"_"+"sectionInfoJson_"+id+"_0",redisTemplate);
            editFile.setLog("管理员"+loguid+"删除了版块"+id);
            JSONObject response = new JSONObject();
            response.put("code" , rows);
            response.put("msg"  , rows > 0 ? "操作成功" : "操作失败");
            redisHelp.deleteKeysWithPattern("*"+this.dataprefix+"_sectionList_1*",redisTemplate,this.dataprefix);
            return response.toString();
        }catch (Exception e){
            e.printStackTrace();
            return Result.getResultJson(0,"接口请求异常，请联系管理员",null);
        }
    }
    /**
     * 关注版块
     */
    @RequestMapping(value = "/sectionFollow")
    @ResponseBody
    @LoginRequired(purview = "0")
    public String sectionFollow(@RequestParam(value = "sectionId", required = false, defaultValue = "1") Integer  sectionId,
                              @RequestParam(value = "token", required = false) String  token,
                              @RequestParam(value = "type", required = false, defaultValue = "1") Integer type) {
        try {
            if(sectionId==0||type==null){
                return Result.getResultJson(0, "参数不正确", null);
            }
            Map map = redisHelp.getMapValue(this.dataprefix + "_" + "userInfo" + token, redisTemplate);
            Integer uid =Integer.parseInt(map.get("uid").toString());
            //登录情况下，刷数据攻击拦截
            String isSilence = redisHelp.getRedis(this.dataprefix+"_"+uid+"_silence",redisTemplate);
            if(isSilence!=null){
                return Result.getResultJson(0,"你已被禁止请求，请耐心等待",null);
            }
            TypechoApiconfig apiconfig = UStatus.getConfig(this.dataprefix,apiconfigService,redisTemplate);
            String isRepeated = redisHelp.getRedis(this.dataprefix+"_"+uid+"_isRepeated",redisTemplate);
            if(isRepeated==null){
                redisHelp.setRedis(this.dataprefix+"_"+uid+"_isRepeated","1",1,redisTemplate);
            }else{
                Integer frequency = Integer.parseInt(isRepeated) + 1;
                if(frequency==1){
                    securityService.safetyMessage("用户ID："+uid+"，在版块关注接口疑似存在攻击行为，请及时确认处理。","system");
                    redisHelp.setRedis(this.dataprefix+"_"+uid+"_silence","1",apiconfig.getSilenceTime(),redisTemplate);
                    return Result.getResultJson(0,"你的请求存在恶意行为，10分钟内禁止操作！",null);
                }else{
                    redisHelp.setRedis(this.dataprefix+"_"+uid+"_isRepeated",frequency.toString(),3,redisTemplate);
                }
                return Result.getResultJson(0,"你的操作太频繁了",null);
            }
            //攻击拦截结束
            TypechoForumSection section = forumSectionService.selectByKey(sectionId);
            if(section==null){
                return Result.getResultJson(0,"板块不存在",null);
            }
            TypechoUserlog log = new TypechoUserlog();
            log.setType("sectionFollow");
            log.setUid(uid);
            log.setCid(sectionId);
            Integer isFollow = userlogservice.total(log);
            if(type.equals(1)){

                if(isFollow > 0){
                    return Result.getResultJson(0,"你已经关注了哦！",null);
                }
                Long date = System.currentTimeMillis();
                String curTime = String.valueOf(date).substring(0,10);

                log.setCreated(Integer.parseInt(curTime));
                //关注后，用户日志表的num将成为版块等级，这里只是提示
                userlogservice.insert(log);
                editFile.setLog("用户"+uid+"关注了版块"+sectionId);
                return Result.getResultJson(1,"操作成功",null);
            }else{
                if(isFollow.equals(0)){
                    return Result.getResultJson(0,"你还未关注哦！",null);
                }
                List<TypechoUserlog> userlogList = userlogService.selectList(log);
                Integer logID = userlogList.get(0).getId();
                int rows = userlogService.delete(logID);
                JSONObject response = new JSONObject();
                response.put("code", rows > 0 ? 1 : 0);
                response.put("data", rows);
                response.put("msg", rows > 0 ? "操作成功" : "操作失败");
                return response.toString();
            }
        }catch (Exception e){
            e.printStackTrace();
            return Result.getResultJson(0,"接口请求异常，请联系管理员",null);
        }
    }
    /**
     * 关注版块
     */
    @RequestMapping(value = "/mySectionFollow")
    @ResponseBody
    @LoginRequired(purview = "0")
    public String sectionFollow(@RequestParam(value = "token", required = false) String  token) {
        Map map = redisHelp.getMapValue(this.dataprefix + "_" + "userInfo" + token, redisTemplate);
        Integer uid =Integer.parseInt(map.get("uid").toString());


        List jsonList = new ArrayList();
        List cacheList = redisHelp.getList(this.dataprefix+"_"+"mySectionFollow_"+uid,redisTemplate);
        try {
            if (cacheList.size() > 0) {
                jsonList = cacheList;
            } else {
                TypechoUserlog log = new TypechoUserlog();
                log.setType("sectionFollow");
                log.setUid(uid);
                List<TypechoUserlog> list = userlogService.selectList(log);
                if(list.size() < 1){
                    JSONObject noData = new JSONObject();
                    noData.put("code" , 1);
                    noData.put("msg"  , "");
                    noData.put("data" , new ArrayList());
                    noData.put("count", 0);
                    return noData.toString();
                }
                for (int i = 0; i < list.size(); i++) {
                    Map json = JSONObject.parseObject(JSONObject.toJSONString(list.get(i)), Map.class);
                    Integer sectionId = list.get(i).getCid();
                    TypechoForumSection section = forumSectionService.selectByKey(sectionId);
                    Map sectionJson = new HashMap();
                    if(section!=null){
                        sectionJson = JSONObject.parseObject(JSONObject.toJSONString(section), Map.class);
                        TypechoForum forum = new TypechoForum();
                        forum.setSection(sectionId);
                        Integer postNum = service.total(forum,null);
                        sectionJson.put("postNum",postNum);
                        //中间要加上用户是否关注
                        sectionJson.put("isFollow",1);
                        sectionJson.put("sectionExp",list.get(i).getNum());

                        json.put("sectionJson",sectionJson);
                        jsonList.add(json);
                    }
                }
                redisHelp.delete(this.dataprefix+"_"+"mySectionFollow_"+uid,redisTemplate);
                redisHelp.setList(this.dataprefix+"_"+"mySectionFollow_"+uid,jsonList,5,redisTemplate);
            }
        }catch (Exception e){
            e.printStackTrace();
            if(cacheList.size()>0){
                jsonList = cacheList;
            }
        }
        JSONObject response = new JSONObject();
        response.put("code" , 1);
        response.put("msg"  , "");
        response.put("data" , jsonList);
        response.put("count", jsonList.size());
        return response.toString();

    }
    /**
     * 版块签到
     */
    @RequestMapping(value = "/sectionClock")
    @ResponseBody
    @LoginRequired(purview = "0")
    public String sectionClock(@RequestParam(value = "sectionId", required = false, defaultValue = "1") Integer  sectionId,
                              @RequestParam(value = "token", required = false) String  token) {
        try {
            if(sectionId==0){
                return Result.getResultJson(0, "参数不正确", null);
            }
            Map map = redisHelp.getMapValue(this.dataprefix + "_" + "userInfo" + token, redisTemplate);
            Integer uid =Integer.parseInt(map.get("uid").toString());
            TypechoApiconfig apiconfig = UStatus.getConfig(this.dataprefix,apiconfigService,redisTemplate);
            //登录情况下，刷数据攻击拦截
            String isSilence = redisHelp.getRedis(this.dataprefix+"_"+uid+"_silence",redisTemplate);
            if(isSilence!=null){
                return Result.getResultJson(0,"你已被禁止请求，请耐心等待",null);
            }
            String isRepeated = redisHelp.getRedis(this.dataprefix+"_"+uid+"_isRepeated",redisTemplate);
            if(isRepeated==null){
                redisHelp.setRedis(this.dataprefix+"_"+uid+"_isRepeated","1",1,redisTemplate);
            }else{
                Integer frequency = Integer.parseInt(isRepeated) + 1;
                if(frequency==1){
                    securityService.safetyMessage("用户ID："+uid+"，在版块关注接口疑似存在攻击行为，请及时确认处理。","system");
                    redisHelp.setRedis(this.dataprefix+"_"+uid+"_silence","1",apiconfig.getSilenceTime(),redisTemplate);
                    return Result.getResultJson(0,"你的请求存在恶意行为，10分钟内禁止操作！",null);
                }else{
                    redisHelp.setRedis(this.dataprefix+"_"+uid+"_isRepeated",frequency.toString(),3,redisTemplate);
                }
                return Result.getResultJson(0,"你的操作太频繁了",null);
            }
            //攻击拦截结束
            TypechoForumSection section = forumSectionService.selectByKey(sectionId);
            if(section==null){
                return Result.getResultJson(0,"板块不存在",null);
            }
            TypechoUserlog follow = new TypechoUserlog();
            follow.setType("sectionFollow");
            follow.setUid(uid);
            follow.setCid(sectionId);
            Integer isFollow = userlogservice.total(follow);
            if(isFollow < 1){
                return Result.getResultJson(0,"请先关注版块！",null);
            }

            TypechoUserlog log = new TypechoUserlog();
            log.setType("sectionClock");
            log.setUid(uid);
            log.setCid(sectionId);
            List<TypechoUserlog> info = userlogservice.selectList(log);
            //获取上次时间
            if (info.size()>0){
                Integer time = info.get(0).getCreated();
                String oldStamp = time+"000";
                SimpleDateFormat sdf=new SimpleDateFormat("yyyyMMdd");
                String oldtime = sdf.format(new Date(Long.parseLong(oldStamp)));
                Integer old = Integer.parseInt(oldtime);
                //获取本次时间
                Long curStamp = System.currentTimeMillis();  //获取当前时间戳
                String curtime = sdf.format(new Date(Long.parseLong(String.valueOf(curStamp))));
                Integer cur = Integer.parseInt(curtime);
                if(old>=cur){
                    return Result.getResultJson(0,"你已经签到过了哦",null);
                }
            }
            //完成签到
            Long date = System.currentTimeMillis();
            String curTime = String.valueOf(date).substring(0,10);
            log.setCreated(Integer.parseInt(curTime));
            userlogservice.insert(log);

            //增加用户关注等级
            List<TypechoUserlog> userlogList = userlogService.selectList(follow);
            Integer logID = userlogList.get(0).getId();
            follow.setId(logID);
            Integer curNum = userlogList.get(0).getNum();
            curNum = curNum + 5;
            follow.setNum(curNum);
            userlogservice.update(follow);

            editFile.setLog("用户"+uid+"签到了版块"+sectionId);
            return Result.getResultJson(1,"签到成功，获得版块经验值5",null);
        }catch (Exception e){
            e.printStackTrace();
            return Result.getResultJson(0,"接口请求异常，请联系管理员",null);
        }
    }
    /**
     * 今日签到前100
     */
    @RequestMapping(value = "/sectionClockList")
    @ResponseBody
    @LoginRequired(purview = "-1")
    public String sectionClockList(@RequestParam(value = "sectionId", required = false, defaultValue = "1") Integer  sectionId){
        List jsonList = new ArrayList();
        List cacheList = redisHelp.getList(this.dataprefix+"_"+"sectionClockList_"+sectionId,redisTemplate);
        try {
            if (cacheList.size() > 0) {
                jsonList = cacheList;
            } else {
                //获取今日签到列表
                List<TypechoUserlog> clockList = jdbcTemplate.queryForObject("SELECT * FROM `"+prefix+"_userlog` WHERE type = 'sectionClock' and DATE(FROM_UNIXTIME(created)) = CURDATE() ORDER BY created DESC LIMIT 100;", List.class);
                for (int i = 0; i < clockList.size(); i++) {
                    Map json = JSONObject.parseObject(JSONObject.toJSONString(clockList.get(i)), Map.class);
                    Integer userid = clockList.get(i).getUid();
                    //获取用户信息
                    Map userJson = UserStatus.getUserInfo(userid,apiconfigService,usersService);
                    //获取用户等级
//                    TypechoComments comments = new TypechoComments();
//                    comments.setAuthorId(userid);
//                    Integer lv = commentsService.total(comments,null);
//                    userJson.put("lv", baseFull.getLv(lv));
                    json.put("userJson",userJson);
                    jsonList.add(json);
                }
                redisHelp.delete(this.dataprefix+"_"+"sectionClockList_"+sectionId,redisTemplate);
                redisHelp.setList(this.dataprefix+"_"+"sectionClockList_"+sectionId,jsonList,5,redisTemplate);
            }
        }catch (Exception e){
            e.printStackTrace();
            if(cacheList.size()>0){
                jsonList = cacheList;
            }
        }
        JSONObject response = new JSONObject();
        response.put("code" , 1);
        response.put("msg"  , "");
        response.put("data" , jsonList);
        response.put("count", jsonList.size());
        return response.toString();

    }
    /**
     * 版块推荐
     */
    @RequestMapping(value = "/sectionRecommend")
    @ResponseBody
    @LoginRequired(purview = "0")
    public String sectionInfo(@RequestParam(value = "sectionId", required = false) Integer  sectionId,
                              @RequestParam(value = "type", required = false,defaultValue = "0") Integer  type,
                              @RequestParam(value = "token", required = false) String  token) {
        try {
            if(!type.equals(0)&&!type.equals(1)){
                return Result.getResultJson(0, "参数不正确", null);
            }
            if(sectionId==0){
                return Result.getResultJson(0, "参数不正确", null);
            }
            Map map = redisHelp.getMapValue(this.dataprefix + "_" + "userInfo" + token, redisTemplate);
            Integer uid =Integer.parseInt(map.get("uid").toString());
            TypechoForumSection section = forumSectionService.selectByKey(sectionId);
            if(section==null){
                return Result.getResultJson(0,"板块不存在",null);
            }
            if(section.getParent().equals(0)){
                return Result.getResultJson(0,"大类不允许推荐",null);
            }
            Integer forumPermissions = UStatus.forumPermissions(map,forumModeratorService,5,sectionId);
            if(forumPermissions.equals(0)){
                return Result.getResultJson(0,"你没有操作权限",null);
            }
            TypechoForumSection newSection = new TypechoForumSection();
            newSection.setId(sectionId);
            newSection.setIsrecommend(type);

            int rows = forumSectionService.update(newSection);
            editFile.setLog("管理员"+uid+"请求修改版块"+sectionId+"推荐状态");
            JSONObject response = new JSONObject();
            response.put("code" , rows);
            response.put("msg"  , rows > 0 ? "操作成功" : "操作失败");
            return response.toString();
        }catch (Exception e){
            e.printStackTrace();
            return Result.getResultJson(0,"接口请求异常，请联系管理员",null);
        }
    }
    /**
     * 版块信息
     */
    @RequestMapping(value = "/sectionInfo")
    @ResponseBody
    @LoginRequired(purview = "-1")
    public String sectionInfo(@RequestParam(value = "id", required = false) Integer  id,
                            @RequestParam(value = "token", required = false) String  token) {
        try {
            Integer uid = 0;
            Integer cacheTime = 20;
            Integer uStatus = UStatus.getStatus(token, this.dataprefix, redisTemplate);
            if (!uStatus.equals(0)) {
                Map map = redisHelp.getMapValue(this.dataprefix + "_" + "userInfo" + token, redisTemplate);
                uid =Integer.parseInt(map.get("uid").toString());
                cacheTime = 3;
            }
            Map sectionInfoJson = new HashMap<String, String>();
            Map cacheInfo = redisHelp.getMapValue(this.dataprefix+"_"+"sectionInfoJson_"+id,redisTemplate);
            if(cacheInfo.size()>0){
                sectionInfoJson = cacheInfo;
            }else{
                TypechoForumSection section = forumSectionService.selectByKey(id);
                if(section==null){
                    return Result.getResultJson(0,"板块不存在",null);
                }
                Integer sectionId = section.getId();
                //查询帖子数量
                TypechoForum forum = new TypechoForum();
                forum.setSection(sectionId);
                Integer postNum = service.total(forum,null);

                //查询版主列表
                List moderators = new ArrayList();
                TypechoForumModerator moderator = new TypechoForumModerator();
                moderator.setSectionId(sectionId);
                List<TypechoForumModerator> moderatorList = forumModeratorService.selectList(moderator);
                for (int i = 0; i < moderatorList.size(); i++) {
                    Map json = JSONObject.parseObject(JSONObject.toJSONString(moderatorList.get(i)), Map.class);
                    Integer userid = moderatorList.get(i).getUid();
                    //获取用户信息
                    Map userJson = UserStatus.getUserInfo(userid,apiconfigService,usersService);
                    //获取用户等级
//                    TypechoComments comments = new TypechoComments();
//                    comments.setAuthorId(userid);
//                    Integer lv = commentsService.total(comments,null);
//                    userJson.put("lv", baseFull.getLv(lv));
                    json.put("userJson",userJson);
                    moderators.add(json);
                }

                sectionInfoJson = JSONObject.parseObject(JSONObject.toJSONString(section), Map.class);
                sectionInfoJson.put("postNum",postNum);
                sectionInfoJson.put("moderators",moderators);
                //查询是否关注和关注等级
                TypechoUserlog log = new TypechoUserlog();
                log.setType("sectionFollow");
                log.setUid(uid);
                log.setCid(sectionId);
                Integer followNum = userlogService.total(log);
                sectionInfoJson.put("followNum",followNum);
                log.setUid(uid);
                List<TypechoUserlog> userlogList = userlogService.selectList(log);
                if(userlogList.size()>0){
                    sectionInfoJson.put("isFollow",1);
                    sectionInfoJson.put("sectionExp",userlogList.get(0).getNum());
                }else{
                    sectionInfoJson.put("isFollow",0);
                    sectionInfoJson.put("sectionExp",0);
                }
                //查询是否签到
                TypechoUserlog logClock = new TypechoUserlog();
                logClock.setType("sectionClock");
                logClock.setUid(uid);
                logClock.setCid(sectionId);
                List<TypechoUserlog> info = userlogservice.selectList(logClock);
                //获取上次时间
                if (info.size()>0){
                    Integer time = info.get(0).getCreated();
                    String oldStamp = time+"000";
                    SimpleDateFormat sdf=new SimpleDateFormat("yyyyMMdd");
                    String oldtime = sdf.format(new Date(Long.parseLong(oldStamp)));
                    Integer old = Integer.parseInt(oldtime);
                    //获取本次时间
                    Long curStamp = System.currentTimeMillis();  //获取当前时间戳
                    String curtime = sdf.format(new Date(Long.parseLong(String.valueOf(curStamp))));
                    Integer cur = Integer.parseInt(curtime);
                    if(old>=cur){
                        sectionInfoJson.put("isClock",1);
                    }else{
                        sectionInfoJson.put("isClock",0);
                    }
                }else{
                    sectionInfoJson.put("isClock",0);
                }
                //获取今日签到人数
                Integer clockNum = jdbcTemplate.queryForObject("SELECT COUNT(*) FROM `"+prefix+"_userlog` WHERE type = 'sectionClock' and DATE(FROM_UNIXTIME(created)) = CURDATE();", Integer.class);
                sectionInfoJson.put("clockNum",clockNum);

                redisHelp.delete(this.dataprefix+"_"+"sectionInfoJson_"+id+'_'+uid,redisTemplate);
                redisHelp.setKey(this.dataprefix+"_"+"sectionInfoJson_"+id+'_'+uid,sectionInfoJson,cacheTime,redisTemplate);
            }
            JSONObject response = new JSONObject();
            response.put("code", 1);
            response.put("msg", "");
            response.put("data", sectionInfoJson);
            return response.toString();
        }catch (Exception e){
            e.printStackTrace();
            JSONObject response = new JSONObject();
            response.put("code", 1);
            response.put("msg", "");
            response.put("data", null);

            return response.toString();
        }
    }
    /**
     * 查询用户是否为版块的版主
     */
    @RequestMapping(value = "/userPurview")
    @ResponseBody
    @LoginRequired(purview = "-1")
    public String userPurview(@RequestParam(value = "uid", required = false) Integer  uid){
        List jsonList = new ArrayList();
        List cacheList = redisHelp.getList(this.dataprefix+"_"+"userPurview_"+uid,redisTemplate);
        try {
            if (cacheList.size() > 0) {
                jsonList = cacheList;
            } else {
                TypechoForumModerator moderator = new TypechoForumModerator();
                moderator.setUid(uid);
                jsonList = forumModeratorService.selectList(moderator);
                redisHelp.delete(this.dataprefix+"_"+"userPurview_"+uid,redisTemplate);
                if(jsonList.size()>0){
                    redisHelp.setList(this.dataprefix+"_"+"userPurview_"+uid,jsonList,10,redisTemplate);
                }

            }
        }catch (Exception e){
            e.printStackTrace();
            if(cacheList.size()>0){
                jsonList = cacheList;
            }
        }
        JSONObject response = new JSONObject();
        response.put("code" , 1);
        response.put("msg"  , "");
        response.put("data" , jsonList);
        response.put("count", jsonList.size());
        return response.toString();
    }
    /**
     * 设置版块权限
     */
    @RequestMapping(value = "/setModerator")
    @ResponseBody
    @LoginRequired(purview = "0")
    public String setModerator(@RequestParam(value = "sectionId", required = false) Integer  sectionId,
                               @RequestParam(value = "uid", required = false) Integer  uid,
                               @RequestParam(value = "purview", required = false, defaultValue = "0") Integer  purview,
                               @RequestParam(value = "token", required = false) String  token) {
        try{
            Map map =redisHelp.getMapValue(this.dataprefix+"_"+"userInfo"+token,redisTemplate);
            Integer loguid  = Integer.parseInt(map.get("uid").toString());
            String group = map.get("group").toString();
            Integer userPurview = 0;
            TypechoForumModerator isModerator = new TypechoForumModerator();
            isModerator.setSectionId(sectionId);
            isModerator.setUid(loguid);
            List<TypechoForumModerator> moderatorList = forumModeratorService.selectList(isModerator);
            if(moderatorList.size()>0){
                userPurview = moderatorList.get(0).getPurview();
            }
            //权限低于大版主，无法操作
            if(userPurview < 5){
                if(!group.equals("administrator") && !group.equals("editor")){
                    return Result.getResultJson(0,"你没有操作权限",null);
                }
            }else{
                //大版主也无法设置大版主权限，只有系统管理员可以
                if(purview.equals(5)){
                    if(!group.equals("administrator") && !group.equals("editor")){
                        return Result.getResultJson(0,"你没有操作权限",null);
                    }
                }
            }
            TypechoUsers users = usersService.selectByKey(uid);
            if(users==null){
                return Result.getResultJson(0,"该用户不存在",null);
            }
            TypechoForumSection section = forumSectionService.selectByKey(sectionId);
            if(section==null){
                return Result.getResultJson(0,"该板块不存在",null);
            }
            if(section.getType().equals("sort")){
                return Result.getResultJson(0,"该类型版块不支持添加管理人员",null);
            }
            TypechoForumModerator moderator = new TypechoForumModerator();
            moderator.setUid(uid);
            moderator.setSectionId(sectionId);
            Integer curUserModerator = forumModeratorService.total(moderator);
            if(curUserModerator > 0){
                return Result.getResultJson(0,"该用户已是该版块管理人员",null);
            }
            moderator.setPurview(purview);

            int rows =  forumModeratorService.insert(moderator);
            editFile.setLog("管理员"+loguid+"为版块"+sectionId+"添加了管理人员"+uid);
            JSONObject response = new JSONObject();
            response.put("code" , rows);
            response.put("msg"  , rows > 0 ? "操作成功" : "操作失败");
            return response.toString();
        }catch (Exception e){
            e.printStackTrace();
            return Result.getResultJson(0,"接口请求异常，请联系管理员",null);
        }
    }
    /**
     * 撤销用户权限
     */
    @RequestMapping(value = "/deleteModerator")
    @ResponseBody
    @LoginRequired(purview = "0")
    public String deleteModerator(@RequestParam(value = "id", required = false) Integer  id,
                               @RequestParam(value = "token", required = false) String  token) {
        try{
            Map map =redisHelp.getMapValue(this.dataprefix+"_"+"userInfo"+token,redisTemplate);
            Integer loguid  = Integer.parseInt(map.get("uid").toString());
            String group = map.get("group").toString();
            if(!group.equals("administrator") && !group.equals("editor")){
                return Result.getResultJson(0,"你没有操作权限",null);
            }
            TypechoForumModerator moderator = forumModeratorService.selectByKey(id);
            if(moderator==null){
                return Result.getResultJson(0,"该信息不存在",null);
            }
            Integer uid = moderator.getUid();
            Integer sectionId = moderator.getSectionId();
            int rows =  forumModeratorService.delete(id);
            editFile.setLog("管理员"+loguid+"撤销了版块"+sectionId+"管理人员"+uid);
            JSONObject response = new JSONObject();
            response.put("code" , rows);
            response.put("msg"  , rows > 0 ? "撤销权限成功" : "操作失败");
            return response.toString();
        }catch (Exception e){
            e.printStackTrace();
            return Result.getResultJson(0,"接口请求异常，请联系管理员",null);
        }

    }

    /**
     * 我关注的人的帖子
     */
    @RequestMapping(value = "/followPosts")
    @ResponseBody
    @LoginRequired(purview = "0")
    public String followSpace(@RequestParam(value = "token", required = false) String  token,
                              @RequestParam(value = "page"        , required = false, defaultValue = "1") Integer page,
                              @RequestParam(value = "limit"       , required = false, defaultValue = "15") Integer limit){
        Integer uStatus = UStatus.getStatus(token,this.dataprefix,redisTemplate);
        page = page - 1;

        Map map =redisHelp.getMapValue(this.dataprefix+"_"+"userInfo"+token,redisTemplate);
        Integer uid  = Integer.parseInt(map.get("uid").toString());
        List jsonList = new ArrayList();
        List cacheList = redisHelp.getList(this.dataprefix+"_"+"followPosts_"+uid+"_"+page+"_"+limit,redisTemplate);
        try {
            if (cacheList.size() > 0) {
                jsonList = cacheList;
            } else {
                TypechoApiconfig apiconfig = UStatus.getConfig(this.dataprefix,apiconfigService,redisTemplate);
                String sql = "SELECT forum.* FROM "+prefix+"_forum AS forum JOIN "+prefix+"_fan AS fan ON forum.authorId = fan.touid WHERE fan.uid = ? AND forum.status = 1 ORDER BY forum.created DESC LIMIT ?, ?";
                List<Map<String, Object>> list = jdbcTemplate.queryForList(sql, uid, page, limit);
                if(list.size() < 1){
                    JSONObject noData = new JSONObject();
                    noData.put("code" , 1);
                    noData.put("msg"  , "");
                    noData.put("data" , new ArrayList());
                    noData.put("count", 0);
                    return noData.toString();
                }
                for (int i = 0; i < list.size(); i++) {
                    Map json = JSONObject.parseObject(JSONObject.toJSONString(list.get(i)), Map.class);
                    TypechoForum forum = JSON.parseObject(JSON.toJSONString(json), TypechoForum.class);
                    Integer userid = forum.getAuthorId();
                    //基本信息处理
                    List imgList = baseFull.getImageSrc(forum.getText());
                    json.put("images",imgList);
                    List<Map<String, String>> videoList = baseFull.getVideoInfo(forum.getText());
                    json.put("videos", videoList);
                    String text = forum.getText();
                    text = baseFull.toStrByChinese(text);
                    json.put("text",text.length()>200 ? text.substring(0,200)+"……" : text);
                    //获取用户信息
                    Map userJson = UserStatus.getUserInfo(userid,apiconfigService,usersService);
                    //获取用户等级
//                    TypechoComments comments = new TypechoComments();
//                    comments.setAuthorId(userid);
//                    Integer lv = commentsService.total(comments,null);
//                    userJson.put("lv", baseFull.getLv(lv));
                    json.put("userJson",userJson);
                    if (uStatus != 0) {
                        TypechoFan fan = new TypechoFan();
                        fan.setUid(uid);
                        fan.setTouid(forum.getAuthorId());
                        Integer isFollow = fanService.total(fan);
                        json.put("isFollow",isFollow);

                        TypechoUserlog userlog = new TypechoUserlog();
                        userlog.setCid(forum.getId());
                        userlog.setType("postLike");
                        userlog.setUid(uid);
                        Integer isLikes = userlogService.total(userlog);
                        if(isLikes > 0){
                            json.put("isLikes",1);
                        }else{
                            json.put("isLikes",0);
                        }

                    }else{
                        json.put("isLikes",0);
                        json.put("isFollow",0);
                    }
                    TypechoForumSection section = forumSectionService.selectByKey(forum.getSection());
                    if(section==null){
                        json.put("sectionJson",new HashMap<>());

                    }else{
                        Map sectionJson = JSONObject.parseObject(JSONObject.toJSONString(section), Map.class);
                        json.put("sectionJson",sectionJson);
                    }
                    jsonList.add(json);
                }
                redisHelp.delete(this.dataprefix+"_"+"followPosts_"+uid+"_"+page+"_"+limit,redisTemplate);
                redisHelp.setList(this.dataprefix+"_"+"followPosts_"+uid+"_"+page+"_"+limit,jsonList,5,redisTemplate);
            }
        }catch (Exception e){
            e.printStackTrace();
            if(cacheList.size()>0){
                jsonList = cacheList;
            }
        }
        JSONObject response = new JSONObject();
        response.put("code" , 1);
        response.put("msg"  , "");
        response.put("data" , jsonList);
        response.put("count", jsonList.size());
        return response.toString();

    }

}
