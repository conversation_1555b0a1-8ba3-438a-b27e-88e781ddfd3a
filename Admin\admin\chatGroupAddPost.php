<?php
session_start();
?>
<?php
include_once 'connect.php';
$uid = trim($_POST['uid']);
$name = $_POST['name'];
$pic = $_POST['pic'];

$time = time();
$file = $_SERVER['PHP_SELF'];


if (isset($_SESSION['loginadmin']) && $_SESSION['loginadmin'] <> '') {
    $charu = "insert into typecho_chat (uid,toid,created,type,name,pic) values ('$uid','$uid','$time','1','$name','$pic')";
    $result = mysqli_query($connect, $charu);
    $redisKeys = $connectRedis->keys('starapi_*');
        foreach ($redisKeys as $redisKey) {
            $connectRedis->del($redisKey);
        }
    if ($result) {
            echo "<script>alert('创建成功');location.href = 'chatGroupAdmin.php';</script>";
        } else {
            echo "<script>alert('创建失败');location.href = 'chatGroupAdmin.php';</script>";   
        }
} else {
    echo "<script>alert('非法操作，行为已记录');location.href = 'warning.php?route=$file';</script>";
}
