package com.StarProApi.entity;

import java.io.Serializable;
import lombok.Data;
import java.util.Date;
import java.util.List;

/**
 * TypechoGpt
 * <AUTHOR> 2024-06-17
 */
@Data
public class TypechoGpt implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id  
     */
    private Integer id;

    /**
     * name  模型名称
     */
    private String name;

    /**
     * source  模型来源
     */
    private String source;

    /**
     * isVip  是否仅VIP可用
     */
    private Integer isVip;

    /**
     * price  每次请求消耗多少金币
     */
    private Integer price;

    /**
     * avatar  模型头像
     */
    private String avatar;

    /**
     * intro  模型简介
     */
    private String intro;

    /**
     * created  创建时间
     */
    private Integer created;

    /**
     * appId  大模型渠道appId
     */
    private String appId;

    /**
     * apiKey  大模型渠道apiKey
     */
    private String apiKey;


    /**
     * 应用类型 0是聊天大模型 1是AI应用
     */
    private Integer type;

    /**
     * prompt 仅AI应用需要设置
     */
    private String prompt;

}