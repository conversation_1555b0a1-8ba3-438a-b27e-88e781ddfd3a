<?php
session_start();
?>
<?php
include_once 'connect.php';
$uid = $_POST['uid'];
$type = $_POST['type'];
$name = $_POST['name'];
$intro = $_POST['intro'];
$urltype = $_POST['urltype'];
$url = $_POST['url'];
$img = $_POST['img'];
$close = strtotime($_POST['close']);

$file = $_SERVER['PHP_SELF'];


if (isset($_SESSION['loginadmin']) && $_SESSION['loginadmin'] <> '') {
    $charu = "insert into typecho_ads (`type`,`close`,`img`,`url`,`urltype`,`intro`,`name`,`uid`,`status`) values ('$type','$close','$img','$url','$urltype','$intro','$name','$uid','1')";
    $result = mysqli_query($connect, $charu);
$redisKeys = $connectRedis->keys('starapi_*');
    foreach ($redisKeys as $redisKey) {
        $connectRedis->del($redisKey);
    }
    if ($result) {
            echo "<script>alert('发布成功');location.href = 'adAdmin.php';</script>";
        } else {
            echo "<script>alert('发布失败');location.href = 'adAdmin.php';</script>";   
        }
} else {
    echo "<script>alert('非法操作，行为已记录');location.href = 'warning.php?route=$file';</script>";
}