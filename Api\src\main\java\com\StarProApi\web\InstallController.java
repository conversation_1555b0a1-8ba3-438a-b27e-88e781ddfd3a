package com.StarProApi.web;

import com.StarProApi.annotation.LoginRequired;
import com.StarProApi.common.PHPass;
import com.StarProApi.common.RedisHelp;
import com.StarProApi.common.ResultAll;
import com.StarProApi.entity.TypechoUsers;
import com.StarProApi.service.TypechoUsersService;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

/**
 * 初次安装控制器
 *
 * 用户检测数据表和字段是否存在，不存在则添加实现安装
 * */
@Controller
@RequestMapping(value = "/installStar")
public class InstallController {
    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Autowired
    private RedisTemplate redisTemplate;

    @Autowired
    private TypechoUsersService usersService;

    @Value("${mybatis.configuration.variables.prefix}")
    private String prefix;

    @Value("${web.prefix}")
    private String dataprefix;

    @Value("${webinfo.key}")
    private String key;



    RedisHelp redisHelp =new RedisHelp();
    ResultAll Result = new ResultAll();
    PHPass phpass = new PHPass(8);
    /**
     * 检测环境和应用
     */
    @RequestMapping(value = "/isInstall")
    @ResponseBody
    public String isInstall(){
        Integer code = 1;
        String msg = "安装正常";
        try {
            String isInstall = redisHelp.getRedis(this.dataprefix+"_"+"isInstall",redisTemplate);

        }catch (Exception e){
            code = 100;
            msg =  "Redis连接失败或未安装";
        }
        try {
            Integer i = jdbcTemplate.queryForObject("select count(*) from information_schema.columns where table_name = '"+prefix+"_users';", Integer.class);
            if (i.equals(0)){
                code = 101;
                msg =  "Typecho未安装或者数据表前缀不正确。";
            }
        }catch (Exception e){
            code = 102;
            msg =  "Mysql数据库连接失败或未安装";
        }
        JSONObject response = new JSONObject();
        response.put("code" , code);
        response.put("msg"  ,msg);
        return response.toString();
    }
    /**
     * 安装Typecho数据库
     */
    @RequestMapping(value = "/typechoInstall")
    @ResponseBody
    @LoginRequired(purview = "-3")
    public String typechoInstall(@RequestParam(value = "webkey", required = false,defaultValue = "") String  webkey,@RequestParam(value = "name", required = false) String  name,@RequestParam(value = "password", required = false) String  password) {
        if(!webkey.equals(this.key)){
            return Result.getResultJson(0,"错误码：250，请输入正确的访问KEY。如果忘记，可在服务器/opt/application.properties中查看",null);
        }
        if(name==null||password==null){
            return Result.getResultJson(0,"请求参数错误！",null);
        }
        String isRepeated = redisHelp.getRedis("isTypechoInstall",redisTemplate);
        if(isRepeated==null){
            redisHelp.setRedis("isTypechoInstall","1",15,redisTemplate);
        }else{
            return Result.getResultJson(0,"你的操作太频繁了",null);
        }
        String text = "执行信息 ------";
        Integer i = 1;
        //判断typecho是否安装，或者数据表前缀是否正确
        try {
            i = jdbcTemplate.queryForObject("select count(*) from information_schema.columns where table_name = '"+prefix+"_users';", Integer.class);
            if (i > 0){
                return Result.getResultJson(0,"Typecho数据库已载入，无需重试",null);
            }
        }catch (Exception e){
            return Result.getResultJson(0,"Mysql数据库连接失败或未安装",null);
        }
        try {
            //安装用户表
            jdbcTemplate.execute("CREATE TABLE `"+prefix+"_users` (" +
                    "  `uid` int(10) unsigned NOT NULL AUTO_INCREMENT," +
                    "  `name` varchar(32) DEFAULT NULL," +
                    "  `password` varchar(64) DEFAULT NULL," +
                    "  `mail` varchar(200) DEFAULT NULL," +
                    "  `url` varchar(200) DEFAULT 'bm'," +
                    "  `screenName` varchar(32) DEFAULT NULL," +
                    "  `created` int(10) unsigned DEFAULT '0'," +
                    "  `activated` int(10) unsigned DEFAULT '0'," +
                    "  `logged` int(10) unsigned DEFAULT '0'," +
                    "  `group` varchar(16) DEFAULT 'visitor'," +
                    "  `authCode` varchar(64) DEFAULT NULL," +
                    "  PRIMARY KEY (`uid`)," +
                    "  UNIQUE KEY `name` (`name`)," +
                    "  UNIQUE KEY `mail` (`mail`)" +
                    ") ENGINE=MyISAM AUTO_INCREMENT=2 DEFAULT CHARSET=utf8;");
            text+="用户表创建完成。";
            String passwd = phpass.HashPassword(password);
            Long date = System.currentTimeMillis();
            String userTime = String.valueOf(date).substring(0, 10);
            TypechoUsers user = new TypechoUsers();
            user.setName(name);
            user.setPassword(passwd);
            user.setCreated(Integer.parseInt(userTime));
            user.setGroupKey("administrator");
            usersService.insert(user);
            text+="管理员"+name+"添加完成。";
            //安装内容表
            jdbcTemplate.execute("CREATE TABLE `"+prefix+"_contents` (" +
                    "  `cid` int(10) unsigned NOT NULL AUTO_INCREMENT," +
                    "  `title` varchar(200) DEFAULT NULL," +
                    "  `slug` varchar(200) DEFAULT NULL," +
                    "  `created` int(10) unsigned DEFAULT '0'," +
                    "  `modified` int(10) unsigned DEFAULT '0'," +
                    "  `text` longtext," +
                    "  `order` int(10) unsigned DEFAULT '0'," +
                    "  `authorId` int(10) unsigned DEFAULT '0'," +
                    "  `template` varchar(32) DEFAULT NULL," +
                    "  `type` varchar(16) DEFAULT 'post'," +
                    "  `status` varchar(16) DEFAULT 'publish'," +
                    "  `password` varchar(32) DEFAULT NULL," +
                    "  `commentsNum` int(10) unsigned DEFAULT '0'," +
                    "  `allowComment` char(1) DEFAULT '0'," +
                    "  `allowPing` char(1) DEFAULT '0'," +
                    "  `allowFeed` char(1) DEFAULT '0'," +
                    "  `parent` int(10) unsigned DEFAULT '0'," +
                    "  PRIMARY KEY (`cid`)," +
                    "  UNIQUE KEY `slug` (`slug`)," +
                    "  KEY `created` (`created`)" +
                    ") ENGINE=MyISAM DEFAULT CHARSET=utf8;");
            text+="内容表创建完成。";
            //安装评论表
            jdbcTemplate.execute("CREATE TABLE `"+prefix+"_comments` (" +
                    "  `coid` int(10) unsigned NOT NULL AUTO_INCREMENT," +
                    "  `cid` int(10) unsigned DEFAULT '0'," +
                    "  `created` int(10) unsigned DEFAULT '0'," +
                    "  `author` varchar(200) DEFAULT NULL," +
                    "  `authorId` int(10) unsigned DEFAULT '0'," +
                    "  `ownerId` int(10) unsigned DEFAULT '0'," +
                    "  `mail` varchar(200) DEFAULT NULL," +
                    "  `url` varchar(200) DEFAULT NULL," +
                    "  `ip` varchar(64) DEFAULT NULL," +
                    "  `agent` varchar(200) DEFAULT NULL," +
                    "  `text` text," +
                    "  `type` varchar(16) DEFAULT 'comment'," +
                    "  `status` varchar(16) DEFAULT 'approved'," +
                    "  `parent` int(10) unsigned DEFAULT '0'," +
                    "  PRIMARY KEY (`coid`)," +
                    "  KEY `cid` (`cid`)," +
                    "  KEY `created` (`created`)" +
                    ") ENGINE=MyISAM DEFAULT CHARSET=utf8;");
            text+="评论表创建完成。";
            //自定义字段表
            jdbcTemplate.execute("CREATE TABLE `"+prefix+"_fields` (" +
                    "  `cid` int(10) unsigned NOT NULL," +
                    "  `name` varchar(200) NOT NULL," +
                    "  `type` varchar(8) DEFAULT 'str'," +
                    "  `str_value` text," +
                    "  `int_value` int(10) DEFAULT '0'," +
                    "  `float_value` float DEFAULT '0'," +
                    "  PRIMARY KEY (`cid`,`name`)," +
                    "  KEY `int_value` (`int_value`)," +
                    "  KEY `float_value` (`float_value`)" +
                    ") ENGINE=MyISAM DEFAULT CHARSET=utf8;");
            text+="自定义字段表创建完成。";
            //分类标签表
            jdbcTemplate.execute("CREATE TABLE `"+prefix+"_metas` (" +
                    "  `mid` int(10) unsigned NOT NULL AUTO_INCREMENT," +
                    "  `name` varchar(200) DEFAULT NULL," +
                    "  `slug` varchar(200) DEFAULT NULL," +
                    "  `type` varchar(32) NOT NULL," +
                    "  `description` varchar(200) DEFAULT NULL," +
                    "  `count` int(10) unsigned DEFAULT '0'," +
                    "  `order` int(10) unsigned DEFAULT '0'," +
                    "  `parent` int(10) unsigned DEFAULT '0'," +
                    "  PRIMARY KEY (`mid`)," +
                    "  KEY `slug` (`slug`)" +
                    ") ENGINE=MyISAM DEFAULT CHARSET=utf8;");
            text+="分类标签表创建完成。";
            //数据关联表
            jdbcTemplate.execute("CREATE TABLE `"+prefix+"_relationships` (" +
                    "  `cid` int(10) unsigned NOT NULL," +
                    "  `mid` int(10) unsigned NOT NULL," +
                    "  PRIMARY KEY (`cid`,`mid`)" +
                    ") ENGINE=MyISAM DEFAULT CHARSET=utf8;");
            text+="数据关联表创建完成。";
        }catch (Exception e){
            e.printStackTrace();
            return Result.getResultJson(0,"数据库语句执行失败，请检查数据库版本及服务器性能后重试。",null);
        }
        text+=" ------ 执行结束，第一步安装数据表导入完成，请点击继续安装！！！";
        JSONObject response = new JSONObject();
        response.put("code" , 1);
        response.put("msg"  ,text);
        return response.toString();
    }
    /**
     * 新安装
     */
    @RequestMapping(value = "/newInstall")
    @ResponseBody
    @LoginRequired(purview = "-3")
    public String newInstall(@RequestParam(value = "webkey", required = false,defaultValue = "") String  webkey) {
        if(!webkey.equals(this.key)){
            return "请输入正确的访问KEY。如果忘记，可在服务器/opt/application.properties中查看";
        }
        try {
            String isInstall = redisHelp.getRedis(this.dataprefix+"_"+"isInstall",redisTemplate);
            if(isInstall!=null){
                return "虽然重复执行也没关系，但是还是尽量不要频繁点哦，1分钟后再来操作吧！";
            }
        }catch (Exception e){
            return "Redis连接失败或未安装";
        }
        Integer i = 1;
        String text = "执行信息 ------";
        //判断typecho是否安装，或者数据表前缀是否正确
        try {
            i = jdbcTemplate.queryForObject("select count(*) from information_schema.columns where table_name = '"+prefix+"_users';", Integer.class);
            if (i == 0){
                return "Typecho未安装或者数据表前缀不正确，请尝试安装typecho或者修改properties配置文件。";
            }else{
                text+="Typecho程序确认安装。";
            }
        }catch (Exception e){
            return "Mysql数据库连接失败或未安装";
        }
        //每次安装和升级都删除配置缓存
        redisHelp.delete(dataprefix+"_"+"config",redisTemplate);
        //修改请求头
        jdbcTemplate.execute("ALTER TABLE "+prefix+"_comments MODIFY agent varchar(520);");
        //查询文章表是否存在views字段
        i = jdbcTemplate.queryForObject("select count(*) from information_schema.columns where table_name = '"+prefix+"_contents' and column_name = 'views';", Integer.class);
        if (i == 0){
            //新增字段
            jdbcTemplate.execute("alter table "+prefix+"_contents ADD views integer(10) DEFAULT 0;");
            text+="内容模块，字段views添加完成。";
        }else{
            text+="内容模块，字段views已经存在，无需添加。";
        }
        //查询文章表是否存在likes字段
        i = jdbcTemplate.queryForObject("select count(*) from information_schema.columns where table_name = '"+prefix+"_contents' and column_name = 'likes';", Integer.class);
        if (i == 0){
            jdbcTemplate.execute("alter table "+prefix+"_contents ADD likes integer(10) DEFAULT 0;");
            text+="内容模块，字段likes添加完成。";
        }else{
            text+="内容模块，字段likes已经存在，无需添加。";
        }
        //查询文章表是否存在isrecommend字段
        i = jdbcTemplate.queryForObject("select count(*) from information_schema.columns where table_name = '"+prefix+"_contents' and column_name = 'isrecommend';", Integer.class);
        if (i == 0){
            jdbcTemplate.execute("alter table "+prefix+"_contents ADD isrecommend integer(2) DEFAULT 0;");
            text+="内容模块，字段isrecommend添加完成。";
        }else{
            text+="内容模块，字段isrecommend已经存在，无需添加。";
        }
        //查询文章表是否存在istop字段
        i = jdbcTemplate.queryForObject("select count(*) from information_schema.columns where table_name = '"+prefix+"_contents' and column_name = 'istop';", Integer.class);
        if (i == 0){
            jdbcTemplate.execute("alter table "+prefix+"_contents ADD istop integer(2) DEFAULT 0;");
            text+="内容模块，字段istop添加完成。";
        }else{
            text+="内容模块，字段istop已经存在，无需添加。";
        }
        //查询文章表是否存在isswiper字段
        i = jdbcTemplate.queryForObject("select count(*) from information_schema.columns where table_name = '"+prefix+"_contents' and column_name = 'isswiper';", Integer.class);
        if (i == 0){
            jdbcTemplate.execute("alter table "+prefix+"_contents ADD isswiper integer(2) DEFAULT 0;");
            text+="内容模块，字段isswiper添加完成。";
        }else{
            text+="内容模块，字段isswiper已经存在，无需添加。";
        }
        //查询文章表是否存在replyTime字段
        i = jdbcTemplate.queryForObject("select count(*) from information_schema.columns where table_name = '"+prefix+"_contents' and column_name = 'replyTime';", Integer.class);
        if (i == 0){
            jdbcTemplate.execute("alter table "+prefix+"_contents ADD replyTime integer(10) DEFAULT 0;");
            text+="内容模块，字段replyTime添加完成。";
        }else{
            text+="内容模块，字段replyTime已经存在，无需添加。";
        }
        //查询文章评论表是否存在likes字段
        i = jdbcTemplate.queryForObject("select count(*) from information_schema.columns where table_name = '"+prefix+"_comments' and column_name = 'likes';", Integer.class);
        if (i == 0){
            jdbcTemplate.execute("alter table "+prefix+"_comments ADD likes integer(11) DEFAULT 0;");
            text+="文章评论表，字段likes添加完成。";
        }else{
            text+="文章评论表，字段likes已经存在，无需添加。";
        }
        //查询用户表是否存在introduce字段
        i = jdbcTemplate.queryForObject("select count(*) from information_schema.columns where table_name = '"+prefix+"_users' and column_name = 'introduce';", Integer.class);
        if (i == 0){
            jdbcTemplate.execute("alter table "+prefix+"_users ADD introduce varchar(255);");
            text+="用户模块，字段introduce添加完成。";
        }else{
            text+="用户模块，字段introduce已经存在，无需添加。";
        }
        //查询用户表是否存在account字段
        i = jdbcTemplate.queryForObject("select count(*) from information_schema.columns where table_name = '"+prefix+"_users' and column_name = 'assets';", Integer.class);
        if (i == 0){
            jdbcTemplate.execute("alter table "+prefix+"_users ADD assets integer(11) DEFAULT 0;");
            text+="用户模块，字段assets添加完成。";
        }else{
            text+="用户模块，字段assets已经存在，无需添加。";
        }
        //查询用户表是否存在address字段
        i = jdbcTemplate.queryForObject("select count(*) from information_schema.columns where table_name = '"+prefix+"_users' and column_name = 'address';", Integer.class);
        if (i == 0){
            jdbcTemplate.execute("alter table "+prefix+"_users ADD address text;");
            text+="用户模块，字段address添加完成。";
        }else{
            text+="用户模块，字段address已经存在，无需添加。";
        }
        //查询用户表是否存在address字段
        i = jdbcTemplate.queryForObject("select count(*) from information_schema.columns where table_name = '"+prefix+"_users' and column_name = 'pay';", Integer.class);
        if (i == 0){
            jdbcTemplate.execute("alter table "+prefix+"_users ADD pay text;");
            text+="用户模块，字段pay添加完成。";
        }else{
            text+="用户模块，字段pay已经存在，无需添加。";
        }
        //查询用户表是否存在customize字段
        i = jdbcTemplate.queryForObject("select count(*) from information_schema.columns where table_name = '"+prefix+"_users' and column_name = 'customize';", Integer.class);
        if (i == 0){
            jdbcTemplate.execute("alter table "+prefix+"_users ADD customize varchar(255) DEFAULT NULL;");
            text+="用户模块，字段customize添加完成。";
        }else{
            text+="用户模块，字段customize已经存在，无需添加。";
        }
        //查询用户表是否存在vip字段
        i = jdbcTemplate.queryForObject("select count(*) from information_schema.columns where table_name = '"+prefix+"_users' and column_name = 'vip';", Integer.class);
        if (i == 0){
            jdbcTemplate.execute("alter table "+prefix+"_users ADD vip integer(10) DEFAULT 0;");
            text+="用户模块，字段vip添加完成。";
        }else{
            text+="用户模块，字段vip已经存在，无需添加。";
        }
        //查询用户表是否存在experience字段
        i = jdbcTemplate.queryForObject("select count(*) from information_schema.columns where table_name = '"+prefix+"_users' and column_name = 'experience';", Integer.class);
        if (i == 0){
            jdbcTemplate.execute("alter table "+prefix+"_users ADD experience integer(11) DEFAULT 0;");
            text+="用户模块，字段experience添加完成。";
        }else{
            text+="用户模块，字段experience已经存在，无需添加。";
        }

        //查询用户表是否存在avatar字段
        i = jdbcTemplate.queryForObject("select count(*) from information_schema.columns where table_name = '"+prefix+"_users' and column_name = 'avatar';", Integer.class);
        if (i == 0){
            jdbcTemplate.execute("alter table "+prefix+"_users ADD avatar text;");
            text+="用户模块，字段avatar添加完成。";
        }else{
            text+="用户模块，字段avatar已经存在，无需添加。";
        }
        //查询用户表是否存在clientId字段
        i = jdbcTemplate.queryForObject("select count(*) from information_schema.columns where table_name = '"+prefix+"_users' and column_name = 'clientId';", Integer.class);
        if (i == 0){
            jdbcTemplate.execute("alter table "+prefix+"_users ADD clientId varchar(255) DEFAULT NULL;");
            text+="用户模块，字段clientId添加完成。";
        }else{
            text+="用户模块，字段clientId已经存在，无需添加。";
        }
        //查询用户表是否存在bantime字段
        i = jdbcTemplate.queryForObject("select count(*) from information_schema.columns where table_name = '"+prefix+"_users' and column_name = 'bantime';", Integer.class);
        if (i == 0){
            jdbcTemplate.execute("alter table "+prefix+"_users ADD bantime integer(10) DEFAULT 0;");
            text+="用户模块，字段bantime添加完成。";
        }else{
            text+="用户模块，字段bantime已经存在，无需添加。";
        }
        //查询用户表是否存在posttime字段
        i = jdbcTemplate.queryForObject("select count(*) from information_schema.columns where table_name = '"+prefix+"_users' and column_name = 'posttime';", Integer.class);
        if (i == 0){
            jdbcTemplate.execute("alter table "+prefix+"_users ADD posttime integer(10) DEFAULT 0;");
            text+="用户模块，字段posttime添加完成。";
        }else{
            text+="用户模块，字段posttime已经存在，无需添加。";
        }
        //查询用户表是否存在ip字段
        i = jdbcTemplate.queryForObject("select count(*) from information_schema.columns where table_name = '"+prefix+"_users' and column_name = 'ip';", Integer.class);
        if (i == 0){
            jdbcTemplate.execute("alter table "+prefix+"_users ADD ip varchar(255) DEFAULT '';");
            text+="用户模块，字段ip添加完成。";
        }else{
            text+="用户模块，字段ip已经存在，无需添加。";
        }
        //查询用户表是否存在local字段
        i = jdbcTemplate.queryForObject("select count(*) from information_schema.columns where table_name = '"+prefix+"_users' and column_name = 'local';", Integer.class);
        if (i == 0){
            jdbcTemplate.execute("alter table "+prefix+"_users ADD local varchar(255) DEFAULT '';");
            text+="用户模块，字段local添加完成。";
        }else{
            text+="用户模块，字段local已经存在，无需添加。";
        }
        //查询用户表是否存在phone字段
        i = jdbcTemplate.queryForObject("select count(*) from information_schema.columns where table_name = '"+prefix+"_users' and column_name = 'phone';", Integer.class);
        if (i == 0){
            jdbcTemplate.execute("alter table "+prefix+"_users ADD phone varchar(30) DEFAULT '';");
            text+="用户模块，字段phone添加完成。";
        }else{
            text+="用户模块，字段phone已经存在，无需添加。";
        }
        //查询用户表是否存在userBg字段
        i = jdbcTemplate.queryForObject("select count(*) from information_schema.columns where table_name = '"+prefix+"_users' and column_name = 'userBg';", Integer.class);
        if (i == 0){
            jdbcTemplate.execute("alter table "+prefix+"_users ADD userBg varchar(400) DEFAULT ''  COMMENT '用户主页背景图链接';");
            text+="用户模块，字段userBg添加完成。";
        }else{
            text+="用户模块，字段userBg已经存在，无需添加。";
        }
        //查询用户表是否存在invitationCode字段
        i = jdbcTemplate.queryForObject("select count(*) from information_schema.columns where table_name = '"+prefix+"_users' and column_name = 'invitationCode';", Integer.class);
        if (i == 0){
            jdbcTemplate.execute("alter table "+prefix+"_users ADD invitationCode varchar(255) DEFAULT ''  COMMENT '用户邀请码';");
            text+="用户模块，字段invitationCode添加完成。";
        }else{
            text+="用户模块，字段invitationCode已经存在，无需添加。";
        }
        //查询用户表是否存在invitationUser字段
        i = jdbcTemplate.queryForObject("select count(*) from information_schema.columns where table_name = '"+prefix+"_users' and column_name = 'invitationUser';", Integer.class);
        if (i == 0){
            jdbcTemplate.execute("alter table "+prefix+"_users ADD invitationUser integer(11) DEFAULT 0  COMMENT '邀请用户';;");
            text+="用户模块，字段invitationUser添加完成。";
        }else{
            text+="用户模块，字段invitationUser已经存在，无需添加。";
        }
        //查询用户表是否存在points字段
        i = jdbcTemplate.queryForObject("select count(*) from information_schema.columns where table_name = '"+prefix+"_users' and column_name = 'points';", Integer.class);
        if (i == 0){
            jdbcTemplate.execute("alter table "+prefix+"_users ADD points integer(11) DEFAULT 0  COMMENT '用户积分';;");
            text+="用户模块，字段points添加完成。";
        }else{
            text+="用户模块，字段points已经存在，无需添加。";
        }



            //查询分类标签表是否存在imgurl字段
        i = jdbcTemplate.queryForObject("select count(*) from information_schema.columns where table_name = '"+prefix+"_metas' and column_name = 'imgurl';", Integer.class);
        if (i == 0){
            jdbcTemplate.execute("alter table "+prefix+"_metas ADD imgurl varchar(500) DEFAULT NULL;");
            text+="标签分类模块，字段imgurl添加完成。";
        }else{
            text+="标签分类模块，字段imgurl已经存在，无需添加。";
        }
        //查询分类标签表是否存在isrecommend字段
        i = jdbcTemplate.queryForObject("select count(*) from information_schema.columns where table_name = '"+prefix+"_metas' and column_name = 'isrecommend';", Integer.class);
        if (i == 0){
            jdbcTemplate.execute("alter table "+prefix+"_metas ADD isrecommend integer(2) DEFAULT 0;");
            text+="标签分类模块，字段isrecommend添加完成。";
        }else{
            text+="标签分类模块，字段isrecommend已经存在，无需添加。";
        }
        try {
            Thread.sleep(1000);
        } catch (InterruptedException ie) {
            Thread.currentThread().interrupt();
        }
        //判断用户日志表是否存在
        i = jdbcTemplate.queryForObject("select count(*) from information_schema.columns where table_name = '"+prefix+"_userlog';", Integer.class);
        if (i == 0){
            jdbcTemplate.execute("CREATE TABLE `"+prefix+"_userlog` (" +
                    "  `id` int(11) NOT NULL AUTO_INCREMENT," +
                    "  `uid` int(11) NOT NULL DEFAULT '-1' COMMENT '用户id'," +
                    "  `cid` int(11) NOT NULL DEFAULT '0'," +
                    "  `type` varchar(255) DEFAULT NULL COMMENT '类型'," +
                    "  `num` int(11) DEFAULT '0' COMMENT '数值，用于后期扩展'," +
                    "  `created` int(10) NOT NULL DEFAULT '0' COMMENT '时间'," +
                    "  PRIMARY KEY (`id`)" +
                    ") ENGINE=MyISAM DEFAULT CHARSET=utf8 COMMENT='用户日志（收藏，扩展等）';");
            text+="用户操作模块创建完成。";
        }else{
            text+="用户操作模块已经存在，无需添加。";
        }
        //查询日志表是否存在toid字段
        i = jdbcTemplate.queryForObject("select count(*) from information_schema.columns where table_name = '"+prefix+"_userlog' and column_name = 'toid';", Integer.class);
        if (i == 0){
            jdbcTemplate.execute("alter table "+prefix+"_userlog ADD toid integer(11) DEFAULT 0;");
            text+="用户操作模块，字段toid添加完成。";
        }else{
            text+="用户操作模块，字段toid已经存在，无需添加。";
        }
        //判断用户社会化API表是否存在
        i = jdbcTemplate.queryForObject("select count(*) from information_schema.columns where table_name = '"+prefix+"_userapi';", Integer.class);
        if (i == 0){
            jdbcTemplate.execute("CREATE TABLE `"+prefix+"_userapi` (" +
                    "  `id` int(11) NOT NULL AUTO_INCREMENT," +
                    "  `headImgUrl` varchar(255) DEFAULT NULL COMMENT '头像，可能用不上'," +
                    "  `openId` varchar(255) DEFAULT NULL COMMENT '开放平台ID'," +
                    "  `access_token` varchar(255) DEFAULT NULL COMMENT '唯一值'," +
                    "  `appLoginType` varchar(255) DEFAULT NULL COMMENT '渠道类型'," +
                    "  `uid` int(11) DEFAULT '0' COMMENT '用户ID'," +
                    "  PRIMARY KEY (`id`)" +
                    ") ENGINE=MyISAM DEFAULT CHARSET=utf8 COMMENT='社会化登陆';");
            text+="社会化登录模块创建完成。";
        }else{
            text+="社会化登录模块已经存在，无需添加。";
        }
        try {
            Thread.sleep(500);
        } catch (InterruptedException ie) {
            Thread.currentThread().interrupt();
        }

        //积分商品&积分阅读模块
        i = jdbcTemplate.queryForObject("select count(*) from information_schema.columns where table_name = '"+prefix+"_shop';", Integer.class);
        if (i == 0){
            jdbcTemplate.execute("CREATE TABLE `"+prefix+"_shop` (" +
                    "  `id` int(11) NOT NULL AUTO_INCREMENT," +
                    "  `title` varchar(300) DEFAULT NULL COMMENT '商品标题'," +
                    "  `imgurl` varchar(500) DEFAULT NULL COMMENT '商品图片'," +
                    "  `text` text COMMENT '商品内容'," +
                    "  `price` int(11) DEFAULT '0' COMMENT '商品价格'," +
                    "  `num` int(11) DEFAULT '0' COMMENT '商品数量'," +
                    "  `type` int(11) DEFAULT '1' COMMENT '商品类型（实体，源码，工具，教程）'," +
                    "  `value` text COMMENT '收费显示（除实体外，这个字段购买后显示）'," +
                    "  `cid` int(11) DEFAULT '-1' COMMENT '所属文章'," +
                    "  `uid` int(11) DEFAULT '0' COMMENT '发布人'," +
                    "  PRIMARY KEY (`id`)" +
                    ") ENGINE=MyISAM DEFAULT CHARSET=utf8 COMMENT='商品表';");
            text+="积分商城模块创建完成。";
        }else{
            text+="积分商城模块已经存在，无需添加。";
        }

        //查询商品表是否存在vipDiscount字段
        i = jdbcTemplate.queryForObject("select count(*) from information_schema.columns where table_name = '"+prefix+"_shop' and column_name = 'vipDiscount';", Integer.class);
        if (i == 0){
            jdbcTemplate.execute("alter table "+prefix+"_shop ADD vipDiscount varchar(255) NOT NULL DEFAULT '0.1' COMMENT 'VIP折扣，权高于系统设置折扣';");
            text+="积分商城模块，字段vipDiscount添加完成。";
        }else{
            text+="积分商城模块，字段vipDiscount已经存在，无需添加。";
        }
        //查询商品表是否存在created字段
        i = jdbcTemplate.queryForObject("select count(*) from information_schema.columns where table_name = '"+prefix+"_shop' and column_name = 'created';", Integer.class);
        if (i == 0){
            jdbcTemplate.execute("alter table "+prefix+"_shop ADD created integer(10) DEFAULT 0;");
            text+="积分商城模块，字段created添加完成。";
        }else{
            text+="积分商城模块，字段created已经存在，无需添加。";
        }
        //查询商品表是否存在status字段
        i = jdbcTemplate.queryForObject("select count(*) from information_schema.columns where table_name = '"+prefix+"_shop' and column_name = 'status';", Integer.class);
        if (i == 0){
            jdbcTemplate.execute("alter table "+prefix+"_shop ADD status integer(10) DEFAULT 0;");
            text+="积分商城模块，字段status添加完成。";
        }else{
            text+="积分商城模块，字段status已经存在，无需添加。";
        }
        //查询商品表是否存在sellNum字段
        i = jdbcTemplate.queryForObject("select count(*) from information_schema.columns where table_name = '"+prefix+"_shop' and column_name = 'sellNum';", Integer.class);
        if (i == 0){
            jdbcTemplate.execute("alter table "+prefix+"_shop ADD sellNum integer(11) DEFAULT 0;");
            text+="积分商城模块，字段sellNum添加完成。";
        }else{
            text+="积分商城模块，字段sellNum已经存在，无需添加。";
        }
        //查询聊天室模块是否存在isMd字段
        i = jdbcTemplate.queryForObject("select count(*) from information_schema.columns where table_name = '"+prefix+"_shop' and column_name = 'isMd';", Integer.class);
        if (i == 0){
            jdbcTemplate.execute("alter table "+prefix+"_shop ADD `isMd` int(2) unsigned DEFAULT '1' COMMENT '是否为Markdown编辑器发布'");
            text+="积分商城模块，字段isMd添加完成。";
        }else{
            text+="积分商城模块，字段isMd已经存在，无需添加。";
        }
        //查询聊天室模块是否存在sort字段
        i = jdbcTemplate.queryForObject("select count(*) from information_schema.columns where table_name = '"+prefix+"_shop' and column_name = 'sort';", Integer.class);
        if (i == 0){
            jdbcTemplate.execute("alter table "+prefix+"_shop ADD `sort` int(11) unsigned DEFAULT '0' COMMENT '商品大类'");
            text+="积分商城模块，字段sort添加完成。";
        }else{
            text+="积分商城模块，字段sort已经存在，无需添加。";
        }
        //查询聊天室模块是否存在subtype字段
        i = jdbcTemplate.queryForObject("select count(*) from information_schema.columns where table_name = '"+prefix+"_shop' and column_name = 'subtype';", Integer.class);
        if (i == 0){
            jdbcTemplate.execute("alter table "+prefix+"_shop ADD `subtype` int(11) unsigned DEFAULT '0' COMMENT '子类型'");
            text+="积分商城模块，字段subtype添加完成。";
        }else{
            text+="积分商城模块，字段subtype已经存在，无需添加。";
        }
        //查询聊天室模块是否存在isView字段
        i = jdbcTemplate.queryForObject("select count(*) from information_schema.columns where table_name = '"+prefix+"_shop' and column_name = 'isView';", Integer.class);
        if (i == 0){
            jdbcTemplate.execute("alter table "+prefix+"_shop ADD `isView` int(2) unsigned DEFAULT '1' COMMENT '是否可见'");
            text+="积分商城模块，字段isView添加完成。";
        }else{
            text+="积分商城模块，字段isView已经存在，无需添加。";
        }
        //查询商品表是否存在integral字段
        i = jdbcTemplate.queryForObject("select count(*) from information_schema.columns where table_name = '"+prefix+"_shop' and column_name = 'integral';", Integer.class);
        if (i == 0){
            jdbcTemplate.execute("alter table "+prefix+"_shop ADD integral integer(11) DEFAULT 0 COMMENT '商品所需积分';");
            text+="积分商城模块，字段integral添加完成。";
        }else{
            text+="积分商城模块，字段integral已经存在，无需添加。";
        }
        //判断充值记录表是否存在
        i = jdbcTemplate.queryForObject("select count(*) from information_schema.columns where table_name = '"+prefix+"_paylog';", Integer.class);
        if (i == 0){
            jdbcTemplate.execute("CREATE TABLE `"+prefix+"_paylog` (" +
                    "  `pid` int(11) NOT NULL AUTO_INCREMENT," +
                    "  `subject` varchar(255) DEFAULT NULL," +
                    "  `total_amount` varchar(255) DEFAULT NULL," +
                    "  `out_trade_no` varchar(255) DEFAULT NULL," +
                    "  `trade_no` varchar(255) DEFAULT NULL," +
                    "  `paytype` varchar(255) DEFAULT '' COMMENT '支付类型'," +
                    "  `uid` int(11) DEFAULT '-1' COMMENT '充值人ID'," +
                    "  `created` int(10) DEFAULT NULL," +
                    "  `status` int(11) DEFAULT '0' COMMENT '支付状态（0未支付，1已支付）'," +
                    "  PRIMARY KEY (`pid`)" +
                    ") ENGINE=MyISAM DEFAULT CHARSET=utf8 COMMENT='支付渠道充值记录';");
            text+="资产日志模块创建完成。";
        }else{
            text+="资产日志模块已经存在，无需添加。";
        }

        //添加卡密充值模块
        i = jdbcTemplate.queryForObject("select count(*) from information_schema.columns where table_name = '"+prefix+"_paykey';", Integer.class);
        if (i == 0){
            jdbcTemplate.execute("CREATE TABLE `"+prefix+"_paykey` (" +
                    "  `id` int(11) NOT NULL AUTO_INCREMENT," +
                    "  `value` varchar(255) DEFAULT '' COMMENT '密钥'," +
                    "  `price` int(11) DEFAULT '0' COMMENT '等值积分'," +
                    "  `status` int(2) DEFAULT '0' COMMENT '0未使用，1已使用'," +
                    "  `created` int(10) DEFAULT '0' COMMENT '创建时间'," +
                    "  `uid` int(11) DEFAULT '-1' COMMENT '使用用户'," +
                    "  PRIMARY KEY (`id`)" +
                    ") ENGINE=MyISAM DEFAULT CHARSET=utf8 COMMENT='卡密充值相关';");
            text+="卡密充值模块创建完成。";
        }else{
            text+="卡密充值模块已经存在，无需添加。";
        }
        try {
            Thread.sleep(500);
        } catch (InterruptedException ie) {
            Thread.currentThread().interrupt();
        }
        //添加API配置中心模块
        i = jdbcTemplate.queryForObject("select count(*) from information_schema.columns where table_name = '"+prefix+"_apiconfig';", Integer.class);
        if (i == 0){
            jdbcTemplate.execute("CREATE TABLE `"+prefix+"_apiconfig` (" +
                    "  `id` int(11) NOT NULL AUTO_INCREMENT," +
                    "  `webinfoTitle` varchar(500) NOT NULL DEFAULT '' COMMENT '网站名称'," +
                    "  `webinfoUrl` varchar(500) NOT NULL DEFAULT '' COMMENT '网站URL'," +
                    "  `webinfoUploadUrl` varchar(255) NOT NULL DEFAULT 'http://127.0.0.1:8081/' COMMENT '本地图片访问路径'," +
                    "  `webinfoAvatar` varchar(500) NOT NULL DEFAULT 'https://cravatar.cn/wp-content/themes/cravatar/assets/img/img1.png#' COMMENT '头像源'," +
                    "  `pexelsKey` varchar(255) NOT NULL DEFAULT '' COMMENT '图库key'," +
                    "  `scale` int(11) NOT NULL DEFAULT '100' COMMENT '一元能买多少积分'," +
                    "  `clock` int(11) NOT NULL DEFAULT '0' COMMENT '签到最多多少积分'," +
                    "  `vipPrice` int(11) NOT NULL DEFAULT '200' COMMENT 'VIP一天价格'," +
                    "  `vipDay` int(11) NOT NULL DEFAULT '300' COMMENT '多少天VIP等于永久'," +
                    "  `vipDiscount` varchar(11) NOT NULL DEFAULT '0.1' COMMENT 'VIP折扣'," +
                    "  `isEmail` int(2) NOT NULL DEFAULT '1' COMMENT '邮箱开关（0完全关闭邮箱，1只开启邮箱注册，2邮箱注册和操作通知）'," +
                    "  `isInvite` int(11) NOT NULL DEFAULT '0' COMMENT '注册是否验证邀请码（默认关闭）'," +
                    "  `cosAccessKey` varchar(300) NOT NULL DEFAULT ''," +
                    "  `cosSecretKey` varchar(300) NOT NULL DEFAULT ''," +
                    "  `cosBucket` varchar(255) NOT NULL DEFAULT ''," +
                    "  `cosBucketName` varchar(255) NOT NULL DEFAULT ''," +
                    "  `cosPath` varchar(255) DEFAULT ''," +
                    "  `cosPrefix` varchar(255) NOT NULL DEFAULT ''," +
                    "  `aliyunEndpoint` varchar(500) NOT NULL DEFAULT ''," +
                    "  `aliyunAccessKeyId` varchar(255) NOT NULL DEFAULT ''," +
                    "  `aliyunAccessKeySecret` varchar(255) NOT NULL DEFAULT ''," +
                    "  `aliyunAucketName` varchar(255) NOT NULL DEFAULT ''," +
                    "  `aliyunUrlPrefix` varchar(255) NOT NULL DEFAULT ''," +
                    "  `aliyunFilePrefix` varchar(255) NOT NULL DEFAULT ''," +
                    "  `ftpHost` varchar(255) NOT NULL DEFAULT ''," +
                    "  `ftpPort` int(11) NOT NULL DEFAULT '21'," +
                    "  `ftpUsername` varchar(255) NOT NULL DEFAULT ''," +
                    "  `ftpPassword` varchar(255) NOT NULL DEFAULT ''," +
                    "  `ftpBasePath` varchar(255) NOT NULL DEFAULT ''," +
                    "  `alipayAppId` varchar(255) NOT NULL DEFAULT ''," +
                    "  `alipayPrivateKey` text," +
                    "  `alipayPublicKey` text," +
                    "  `alipayNotifyUrl` varchar(500) NOT NULL DEFAULT ''," +
                    "  `appletsAppid` varchar(255) NOT NULL DEFAULT ''," +
                    "  `appletsSecret` text," +
                    "  `wxpayAppId` varchar(255) NOT NULL DEFAULT ''," +
                    "  `wxpayMchId` varchar(255) NOT NULL DEFAULT ''," +
                    "  `wxpayKey` text," +
                    "  `wxpayNotifyUrl` varchar(500) DEFAULT ''," +
                    "  PRIMARY KEY (`id`)" +
                    ") ENGINE=MyISAM DEFAULT CHARSET=utf8 COMMENT='api配置信息表';");
            text+="API配置中心模块创建完成。";
            //修改请求头
            jdbcTemplate.execute("INSERT INTO `"+prefix+"_apiconfig` (webinfoTitle) VALUES ('网站名称');");
        }else{
            text+="API配置中心模块已经存在，无需添加。";
        }
        try {
            Thread.sleep(500);
        } catch (InterruptedException ie) {
            Thread.currentThread().interrupt();
        }
        //查询配置中心表是否存在auditlevel字段
        i = jdbcTemplate.queryForObject("select count(*) from information_schema.columns where table_name = '"+prefix+"_apiconfig' and column_name = 'auditlevel';", Integer.class);
        if (i == 0){
            jdbcTemplate.execute("alter table "+prefix+"_apiconfig ADD auditlevel integer(2) DEFAULT 1;");
            text+="配置中心模块，字段auditlevel添加完成。";
        }else{
            text+="配置中心模块，字段auditlevel已经存在，无需添加。";
        }
        //查询配置中心表是否存在forbidden字段
        i = jdbcTemplate.queryForObject("select count(*) from information_schema.columns where table_name = '"+prefix+"_apiconfig' and column_name = 'forbidden';", Integer.class);
        if (i == 0){
            jdbcTemplate.execute("alter table "+prefix+"_apiconfig ADD forbidden text;");
            text+="配置中心模块，字段forbidden添加完成。";
        }else{
            text+="配置中心模块，字段forbidden已经存在，无需添加。";
        }
        //查询配置中心表是否存在qqAppletsAppid字段
        i = jdbcTemplate.queryForObject("select count(*) from information_schema.columns where table_name = '"+prefix+"_apiconfig' and column_name = 'qqAppletsAppid';", Integer.class);
        if (i == 0){
            jdbcTemplate.execute("alter table "+prefix+"_apiconfig ADD qqAppletsAppid varchar(500) DEFAULT NULL;");
            text+="配置中心模块，字段qqAppletsAppid添加完成。";
        }else{
            text+="配置中心模块，字段qqAppletsAppid已经存在，无需添加。";
        }
        //查询配置中心表是否存在qqAppletsSecret字段
        i = jdbcTemplate.queryForObject("select count(*) from information_schema.columns where table_name = '"+prefix+"_apiconfig' and column_name = 'qqAppletsSecret';", Integer.class);
        if (i == 0){
            jdbcTemplate.execute("alter table "+prefix+"_apiconfig ADD qqAppletsSecret varchar(500) DEFAULT NULL;");
            text+="配置中心模块，字段qqAppletsSecret添加完成。";
        }else{
            text+="配置中心模块，字段qqAppletsSecret已经存在，无需添加。";
        }
        //查询配置中心表是否存在wxAppId字段
        i = jdbcTemplate.queryForObject("select count(*) from information_schema.columns where table_name = '"+prefix+"_apiconfig' and column_name = 'wxAppId';", Integer.class);
        if (i == 0){
            jdbcTemplate.execute("alter table "+prefix+"_apiconfig ADD wxAppId varchar(500) DEFAULT NULL;");
            text+="配置中心模块，字段wxAppId添加完成。";
        }else{
            text+="配置中心模块，字段wxAppId已经存在，无需添加。";
        }
        //查询配置中心表是否存在wxAppSecret字段
        i = jdbcTemplate.queryForObject("select count(*) from information_schema.columns where table_name = '"+prefix+"_apiconfig' and column_name = 'wxAppSecret';", Integer.class);
        if (i == 0){
            jdbcTemplate.execute("alter table "+prefix+"_apiconfig ADD wxAppSecret varchar(500) DEFAULT NULL;");
            text+="配置中心模块，字段wxAppSecret添加完成。";
        }else{
            text+="配置中心模块，字段wxAppSecret已经存在，无需添加。";
        }
        //查询配置中心表是否存在fields字段
        i = jdbcTemplate.queryForObject("select count(*) from information_schema.columns where table_name = '"+prefix+"_apiconfig' and column_name = 'fields';", Integer.class);
        if (i == 0){
            jdbcTemplate.execute("alter table "+prefix+"_apiconfig ADD fields varchar(500) DEFAULT 'able';");
            text+="配置中心模块，字段fields添加完成。";
        }else{
            text+="配置中心模块，字段fields已经存在，无需添加。";
        }
        //查询配置中心表是否存在pushAdsPrice字段
        i = jdbcTemplate.queryForObject("select count(*) from information_schema.columns where table_name = '"+prefix+"_apiconfig' and column_name = 'pushAdsPrice';", Integer.class);
        if (i == 0){
            jdbcTemplate.execute("alter table "+prefix+"_apiconfig ADD `pushAdsPrice` int(11) NOT NULL DEFAULT '100' COMMENT '推流广告价格(积分/天)'");
            text+="配置中心模块，字段pushAdsPrice添加完成。";
        }else{
            text+="配置中心模块，字段pushAdsPrice已经存在，无需添加。";
        }
        try {
            Thread.sleep(500);
        } catch (InterruptedException ie) {
            Thread.currentThread().interrupt();
        }
        //查询配置中心表是否存在pushAdsNum字段
        i = jdbcTemplate.queryForObject("select count(*) from information_schema.columns where table_name = '"+prefix+"_apiconfig' and column_name = 'pushAdsNum';", Integer.class);
        if (i == 0){
            jdbcTemplate.execute("alter table "+prefix+"_apiconfig ADD `pushAdsNum` int(11) NOT NULL DEFAULT '10' COMMENT '推流广告数量'");
            text+="配置中心模块，字段pushAdsNum添加完成。";
        }else{
            text+="配置中心模块，字段pushAdsNum已经存在，无需添加。";
        }
        //查询配置中心表是否存在bannerAdsPrice字段
        i = jdbcTemplate.queryForObject("select count(*) from information_schema.columns where table_name = '"+prefix+"_apiconfig' and column_name = 'bannerAdsPrice';", Integer.class);
        if (i == 0){
            jdbcTemplate.execute("alter table "+prefix+"_apiconfig ADD `bannerAdsPrice` int(11) NOT NULL DEFAULT '100' COMMENT '横幅广告价格(积分/天)'");
            text+="配置中心模块，字段bannerAdsPrice添加完成。";
        }else{
            text+="配置中心模块，字段bannerAdsPrice已经存在，无需添加。";
        }
        //查询配置中心表是否存在bannerAdsNum字段
        i = jdbcTemplate.queryForObject("select count(*) from information_schema.columns where table_name = '"+prefix+"_apiconfig' and column_name = 'bannerAdsNum';", Integer.class);
        if (i == 0){
            jdbcTemplate.execute("alter table "+prefix+"_apiconfig ADD `bannerAdsNum` int(11) NOT NULL DEFAULT '5' COMMENT '横幅广告数量'");
            text+="配置中心模块，字段bannerAdsNum添加完成。";
        }else{
            text+="配置中心模块，字段bannerAdsNum已经存在，无需添加。";
        }
        //查询配置中心表是否存在startAdsPrice字段
        i = jdbcTemplate.queryForObject("select count(*) from information_schema.columns where table_name = '"+prefix+"_apiconfig' and column_name = 'startAdsPrice';", Integer.class);
        if (i == 0){
            jdbcTemplate.execute("alter table "+prefix+"_apiconfig ADD `startAdsPrice` int(11) NOT NULL DEFAULT '100' COMMENT '启动图广告价格(积分/天)'");
            text+="配置中心模块，字段startAdsPrice添加完成。";
        }else{
            text+="配置中心模块，字段startAdsPrice已经存在，无需添加。";
        }
        //查询配置中心表是否存在startAdsNum字段
        i = jdbcTemplate.queryForObject("select count(*) from information_schema.columns where table_name = '"+prefix+"_apiconfig' and column_name = 'startAdsNum';", Integer.class);
        if (i == 0){
            jdbcTemplate.execute("alter table "+prefix+"_apiconfig ADD `startAdsNum` int(11) NOT NULL DEFAULT '1' COMMENT '启动图广告数量'");
            text+="配置中心模块，字段startAdsNum添加完成。";
        }else{
            text+="配置中心模块，字段startAdsNum已经存在，无需添加。";
        }
        //查询配置中心表是否存在epayUrl字段
        i = jdbcTemplate.queryForObject("select count(*) from information_schema.columns where table_name = '"+prefix+"_apiconfig' and column_name = 'epayUrl';", Integer.class);
        if (i == 0){
            jdbcTemplate.execute("alter table "+prefix+"_apiconfig ADD `epayUrl` varchar(500) DEFAULT '' COMMENT '易支付接口地址'");
            text+="配置中心模块，字段epayUrl添加完成。";
        }else{
            text+="配置中心模块，字段epayUrl已经存在，无需添加。";
        }
        //查询配置中心表是否存在epayPid字段
        i = jdbcTemplate.queryForObject("select count(*) from information_schema.columns where table_name = '"+prefix+"_apiconfig' and column_name = 'epayPid';", Integer.class);
        if (i == 0){
            jdbcTemplate.execute("alter table "+prefix+"_apiconfig ADD `epayPid` int(11) COMMENT '易支付商户ID'");
            text+="配置中心模块，字段epayPid添加完成。";
        }else{
            text+="配置中心模块，字段epayPid已经存在，无需添加。";
        }
        //查询配置中心表是否存在epayKey字段
        i = jdbcTemplate.queryForObject("select count(*) from information_schema.columns where table_name = '"+prefix+"_apiconfig' and column_name = 'epayKey';", Integer.class);
        if (i == 0){
            jdbcTemplate.execute("alter table "+prefix+"_apiconfig ADD `epayKey` varchar(300) DEFAULT '' COMMENT '易支付商户密钥'");
            text+="配置中心模块，字段epayKey添加完成。";
        }else{
            text+="配置中心模块，字段epayKey已经存在，无需添加。";
        }
        //查询配置中心表是否存在epayNotifyUrl字段
        i = jdbcTemplate.queryForObject("select count(*) from information_schema.columns where table_name = '"+prefix+"_apiconfig' and column_name = 'epayNotifyUrl';", Integer.class);
        if (i == 0){
            jdbcTemplate.execute("alter table "+prefix+"_apiconfig ADD `epayNotifyUrl` varchar(500) DEFAULT '' COMMENT '易支付回调地址'");
            text+="配置中心模块，字段epayNotifyUrl添加完成。";
        }else{
            text+="配置中心模块，字段epayNotifyUrl已经存在，无需添加。";
        }
        //查询配置中心表是否存在mchSerialNo字段
        i = jdbcTemplate.queryForObject("select count(*) from information_schema.columns where table_name = '"+prefix+"_apiconfig' and column_name = 'mchSerialNo';", Integer.class);
        if (i == 0){
            jdbcTemplate.execute("alter table "+prefix+"_apiconfig ADD `mchSerialNo` text COMMENT '微信支付商户证书序列号'");
            text+="配置中心模块，字段mchSerialNo添加完成。";
        }else{
            text+="配置中心模块，字段mchSerialNo已经存在，无需添加。";
        }
        //查询配置中心表是否存在mchApiV3Key字段
        i = jdbcTemplate.queryForObject("select count(*) from information_schema.columns where table_name = '"+prefix+"_apiconfig' and column_name = 'mchApiV3Key';", Integer.class);
        if (i == 0){
            jdbcTemplate.execute("alter table "+prefix+"_apiconfig ADD `mchApiV3Key` text COMMENT '微信支付API3私钥'");
            text+="配置中心模块，字段mchApiV3Key添加完成。";
        }else{
            text+="配置中心模块，字段mchApiV3Key已经存在，无需添加。";
        }
        //查询配置中心表是否存在cloudUid字段
        i = jdbcTemplate.queryForObject("select count(*) from information_schema.columns where table_name = '"+prefix+"_apiconfig' and column_name = 'cloudUid';", Integer.class);
        if (i == 0){
            jdbcTemplate.execute("alter table "+prefix+"_apiconfig ADD `cloudUid` varchar(255) DEFAULT '' COMMENT '云控UID'");
            text+="配置中心模块，字段cloudUid添加完成。";
        }else{
            text+="配置中心模块，字段cloudUid已经存在，无需添加。";
        }
        //查询配置中心表是否存在cloudUrl字段
        i = jdbcTemplate.queryForObject("select count(*) from information_schema.columns where table_name = '"+prefix+"_apiconfig' and column_name = 'cloudUrl';", Integer.class);
        if (i == 0){
            jdbcTemplate.execute("alter table "+prefix+"_apiconfig ADD `cloudUrl` varchar(255) DEFAULT '' COMMENT '云控URL'");
            text+="配置中心模块，字段cloudUrl添加完成。";
        }else{
            text+="配置中心模块，字段cloudUrl已经存在，无需添加。";
        }
        //查询配置中心表是否存在pushAppId字段
        i = jdbcTemplate.queryForObject("select count(*) from information_schema.columns where table_name = '"+prefix+"_apiconfig' and column_name = 'pushAppId';", Integer.class);
        if (i == 0){
            jdbcTemplate.execute("alter table "+prefix+"_apiconfig ADD `pushAppId` varchar(255) DEFAULT '' COMMENT 'pushAppId'");
            text+="配置中心模块，字段pushAppId添加完成。";
        }else{
            text+="配置中心模块，字段pushAppId已经存在，无需添加。";
        }
        //查询配置中心表是否存在pushAppKey字段
        i = jdbcTemplate.queryForObject("select count(*) from information_schema.columns where table_name = '"+prefix+"_apiconfig' and column_name = 'pushAppKey';", Integer.class);
        if (i == 0){
            jdbcTemplate.execute("alter table "+prefix+"_apiconfig ADD `pushAppKey` varchar(255) DEFAULT '' COMMENT 'pushAppKey'");
            text+="配置中心模块，字段pushAppKey添加完成。";
        }else{
            text+="配置中心模块，字段pushAppKey已经存在，无需添加。";
        }
        //查询配置中心表是否存在pushMasterSecret字段
        i = jdbcTemplate.queryForObject("select count(*) from information_schema.columns where table_name = '"+prefix+"_apiconfig' and column_name = 'pushMasterSecret';", Integer.class);
        if (i == 0){
            jdbcTemplate.execute("alter table "+prefix+"_apiconfig ADD `pushMasterSecret` varchar(255) DEFAULT '' COMMENT 'pushMasterSecret'");
            text+="配置中心模块，字段pushMasterSecret添加完成。";
        }else{
            text+="配置中心模块，字段pushMasterSecret已经存在，无需添加。";
        }
        //查询配置中心表是否存在isPush字段
        i = jdbcTemplate.queryForObject("select count(*) from information_schema.columns where table_name = '"+prefix+"_apiconfig' and column_name = 'isPush';", Integer.class);
        if (i == 0){
            jdbcTemplate.execute("alter table "+prefix+"_apiconfig ADD `isPush` int(2) DEFAULT '0' COMMENT '是否开启消息通知'");
            text+="配置中心模块，字段isPush添加完成。";
        }else{
            text+="配置中心模块，字段isPush已经存在，无需添加。";
        }
        //查询配置中心表是否存在disableCode字段
        i = jdbcTemplate.queryForObject("select count(*) from information_schema.columns where table_name = '"+prefix+"_apiconfig' and column_name = 'disableCode';", Integer.class);
        if (i == 0){
            jdbcTemplate.execute("alter table "+prefix+"_apiconfig ADD `disableCode` int(2) DEFAULT '0' COMMENT '是否禁用代码'");
            text+="配置中心模块，字段disableCode添加完成。";
        }else{
            text+="配置中心模块，字段disableCode已经存在，无需添加。";
        }
        //查询配置中心表是否存在allowDelete字段
        i = jdbcTemplate.queryForObject("select count(*) from information_schema.columns where table_name = '"+prefix+"_apiconfig' and column_name = 'allowDelete';", Integer.class);
        if (i == 0){
            jdbcTemplate.execute("alter table "+prefix+"_apiconfig ADD `allowDelete` int(2) DEFAULT '0' COMMENT '是否允许用户删除文章或评论'");
            text+="配置中心模块，字段allowDelete添加完成。";
        }else{
            text+="配置中心模块，字段allowDelete已经存在，无需添加。";
        }
        //查询配置中心表是否存在allowDelete字段
        i = jdbcTemplate.queryForObject("select count(*) from information_schema.columns where table_name = '"+prefix+"_apiconfig' and column_name = 'contentAuditlevel';", Integer.class);
        if (i == 0){
            jdbcTemplate.execute("alter table "+prefix+"_apiconfig ADD `contentAuditlevel` int(2) DEFAULT '2' COMMENT '内容审核模式'");
            text+="配置中心模块，字段contentAuditlevel添加完成。";
        }else{
            text+="配置中心模块，字段contentAuditlevel已经存在，无需添加。";
        }
        //查询配置中心表是否存在uploadLevel字段
        i = jdbcTemplate.queryForObject("select count(*) from information_schema.columns where table_name = '"+prefix+"_apiconfig' and column_name = 'uploadLevel';", Integer.class);
        if (i == 0){
            jdbcTemplate.execute("alter table "+prefix+"_apiconfig ADD `uploadLevel` int(2) DEFAULT '0' COMMENT '上传限制等级（0只允许图片，1关闭上传接口，2只允许图片视频，3允许所有类型文件）'");
            text+="配置中心模块，字段uploadLevel添加完成。";
        }else{
            text+="配置中心模块，字段uploadLevel已经存在，无需添加。";
        }
        //查询配置中心表是否存在clockExp字段
        i = jdbcTemplate.queryForObject("select count(*) from information_schema.columns where table_name = '"+prefix+"_apiconfig' and column_name = 'clockExp';", Integer.class);
        if (i == 0){
            jdbcTemplate.execute("alter table "+prefix+"_apiconfig ADD `clockExp` int(11) DEFAULT '0' COMMENT '签到经验'");
            text+="配置中心模块，字段clockExp添加完成。";
        }else{
            text+="配置中心模块，字段clockExp已经存在，无需添加。";
        }
        //查询配置中心表是否存在reviewExp字段
        i = jdbcTemplate.queryForObject("select count(*) from information_schema.columns where table_name = '"+prefix+"_apiconfig' and column_name = 'reviewExp';", Integer.class);
        if (i == 0){
            jdbcTemplate.execute("alter table "+prefix+"_apiconfig ADD `reviewExp` int(11) DEFAULT '0' COMMENT '每日前三次评论经验'");
            text+="配置中心模块，字段reviewExp添加完成。";
        }else{
            text+="配置中心模块，字段reviewExp已经存在，无需添加。";
        }
        //查询配置中心表是否存在postExp字段
        i = jdbcTemplate.queryForObject("select count(*) from information_schema.columns where table_name = '"+prefix+"_apiconfig' and column_name = 'postExp';", Integer.class);
        if (i == 0){
            jdbcTemplate.execute("alter table "+prefix+"_apiconfig ADD `postExp` int(11) DEFAULT '0' COMMENT '每日前三次发布内容经验（文章，动态，帖子）'");
            text+="配置中心模块，字段postExp添加完成。";
        }else{
            text+="配置中心模块，字段postExp已经存在，无需添加。";
        }
        //查询配置中心表是否存在violationExp字段
        i = jdbcTemplate.queryForObject("select count(*) from information_schema.columns where table_name = '"+prefix+"_apiconfig' and column_name = 'violationExp';", Integer.class);
        if (i == 0){
            jdbcTemplate.execute("alter table "+prefix+"_apiconfig ADD `violationExp` int(11) DEFAULT '0' COMMENT '违规扣除经验'");
            text+="配置中心模块，字段violationExp添加完成。";
        }else{
            text+="配置中心模块，字段violationExp已经存在，无需添加。";
        }
        //查询配置中心表是否存在deleteExp字段
        i = jdbcTemplate.queryForObject("select count(*) from information_schema.columns where table_name = '"+prefix+"_apiconfig' and column_name = 'deleteExp';", Integer.class);
        if (i == 0){
            jdbcTemplate.execute("alter table "+prefix+"_apiconfig ADD `deleteExp` int(11) DEFAULT '0' COMMENT '删除扣除经验（文章，评论，动态，帖子）'");
            text+="配置中心模块，字段deleteExp添加完成。";
        }else{
            text+="配置中心模块，字段deleteExp已经存在，无需添加。";
        }
        //查询配置中心表是否存在spaceMinExp字段
        i = jdbcTemplate.queryForObject("select count(*) from information_schema.columns where table_name = '"+prefix+"_apiconfig' and column_name = 'spaceMinExp';", Integer.class);
        if (i == 0){
            jdbcTemplate.execute("alter table "+prefix+"_apiconfig ADD `spaceMinExp` int(11) DEFAULT '20' COMMENT '发布动态要求最低经验值'");
            text+="配置中心模块，字段spaceMinExp添加完成。";
        }else{
            text+="配置中心模块，字段spaceMinExp已经存在，无需添加。";
        }
        //查询配置中心表是否存在chatMinExp字段
        i = jdbcTemplate.queryForObject("select count(*) from information_schema.columns where table_name = '"+prefix+"_apiconfig' and column_name = 'chatMinExp';", Integer.class);
        if (i == 0){
            jdbcTemplate.execute("alter table "+prefix+"_apiconfig ADD `chatMinExp` int(11) DEFAULT '20' COMMENT '聊天要求最低经验值'");
            text+="配置中心模块，字段chatMinExp添加完成。";
        }else{
            text+="配置中心模块，字段chatMinExp已经存在，无需添加。";
        }
        //查询配置中心表是否存在qiniuDomain字段
        i = jdbcTemplate.queryForObject("select count(*) from information_schema.columns where table_name = '"+prefix+"_apiconfig' and column_name = 'qiniuDomain';", Integer.class);
        if (i == 0){
            jdbcTemplate.execute("alter table "+prefix+"_apiconfig ADD `qiniuDomain` varchar(400) DEFAULT '' COMMENT '七牛云访问域名'");
            text+="配置中心模块，字段qiniuDomain添加完成。";
        }else{
            text+="配置中心模块，字段qiniuDomain已经存在，无需添加。";
        }
        //查询配置中心表是否存在qiniuAccessKey字段
        i = jdbcTemplate.queryForObject("select count(*) from information_schema.columns where table_name = '"+prefix+"_apiconfig' and column_name = 'qiniuAccessKey';", Integer.class);
        if (i == 0){
            jdbcTemplate.execute("alter table "+prefix+"_apiconfig ADD `qiniuAccessKey` varchar(400) DEFAULT '' COMMENT '七牛云公钥'");
            text+="配置中心模块，字段qiniuAccessKey添加完成。";
        }else{
            text+="配置中心模块，字段qiniuAccessKey已经存在，无需添加。";
        }
        //查询配置中心表是否存在qiniuSecretKey字段
        i = jdbcTemplate.queryForObject("select count(*) from information_schema.columns where table_name = '"+prefix+"_apiconfig' and column_name = 'qiniuSecretKey';", Integer.class);
        if (i == 0){
            jdbcTemplate.execute("alter table "+prefix+"_apiconfig ADD `qiniuSecretKey` varchar(400) DEFAULT '' COMMENT '七牛云私钥'");
            text+="配置中心模块，字段qiniuSecretKey添加完成。";
        }else{
            text+="配置中心模块，字段qiniuSecretKey已经存在，无需添加。";
        }
        //查询配置中心表是否存在qiniuBucketName字段
        i = jdbcTemplate.queryForObject("select count(*) from information_schema.columns where table_name = '"+prefix+"_apiconfig' and column_name = 'qiniuBucketName';", Integer.class);
        if (i == 0){
            jdbcTemplate.execute("alter table "+prefix+"_apiconfig ADD `qiniuBucketName` varchar(255) DEFAULT '' COMMENT '七牛云存储桶名称'");
            text+="配置中心模块，字段qiniuBucketName添加完成。";
        }else{
            text+="配置中心模块，字段qiniuBucketName已经存在，无需添加。";
        }
        //查询配置中心表是否存在codeAccessKeyId字段
        i = jdbcTemplate.queryForObject("select count(*) from information_schema.columns where table_name = '"+prefix+"_apiconfig' and column_name = 'codeAccessKeyId';", Integer.class);
        if (i == 0){
            jdbcTemplate.execute("alter table "+prefix+"_apiconfig ADD `codeAccessKeyId` varchar(400) DEFAULT '' COMMENT '阿里云短信AccessKeyId'");
            text+="配置中心模块，字段codeAccessKeyId添加完成。";
        }else{
            text+="配置中心模块，字段codeAccessKeyId已经存在，无需添加。";
        }
        //查询配置中心表是否存在codeAccessKeySecret字段
        i = jdbcTemplate.queryForObject("select count(*) from information_schema.columns where table_name = '"+prefix+"_apiconfig' and column_name = 'codeAccessKeySecret';", Integer.class);
        if (i == 0){
            jdbcTemplate.execute("alter table "+prefix+"_apiconfig ADD `codeAccessKeySecret` varchar(400) DEFAULT '' COMMENT '阿里云短信AccessKeySecret'");
            text+="配置中心模块，字段codeAccessKeySecret添加完成。";
        }else{
            text+="配置中心模块，字段codeAccessKeySecret已经存在，无需添加。";
        }
        //查询配置中心表是否存在codeEndpoint字段
        i = jdbcTemplate.queryForObject("select count(*) from information_schema.columns where table_name = '"+prefix+"_apiconfig' and column_name = 'codeEndpoint';", Integer.class);
        if (i == 0){
            jdbcTemplate.execute("alter table "+prefix+"_apiconfig ADD `codeEndpoint` varchar(400) DEFAULT 'dysmsapi.aliyuncs.com' COMMENT '阿里云短信请求地址'");
            text+="配置中心模块，字段codeEndpoint添加完成。";
        }else{
            text+="配置中心模块，字段codeEndpoint已经存在，无需添加。";
        }
        //查询配置中心表是否存在codeTemplate字段
        i = jdbcTemplate.queryForObject("select count(*) from information_schema.columns where table_name = '"+prefix+"_apiconfig' and column_name = 'codeTemplate';", Integer.class);
        if (i == 0){
            jdbcTemplate.execute("alter table "+prefix+"_apiconfig ADD `codeTemplate` varchar(255) DEFAULT '' COMMENT '阿里云短信请求地址'");
            text+="配置中心模块，字段codeTemplate添加完成。";
        }else{
            text+="配置中心模块，字段codeTemplate已经存在，无需添加。";
        }
        //查询配置中心表是否存在codeSignName字段
        i = jdbcTemplate.queryForObject("select count(*) from information_schema.columns where table_name = '"+prefix+"_apiconfig' and column_name = 'codeSignName';", Integer.class);
        if (i == 0){
            jdbcTemplate.execute("alter table "+prefix+"_apiconfig ADD `codeSignName` varchar(255) DEFAULT 'StarPro' COMMENT '阿里云短信签名'");
            text+="配置中心模块，字段codeSignName添加完成。";
        }else{
            text+="配置中心模块，字段codeSignName已经存在，无需添加。";
        }
        //查询配置中心表是否存在isPhone字段
        i = jdbcTemplate.queryForObject("select count(*) from information_schema.columns where table_name = '"+prefix+"_apiconfig' and column_name = 'isPhone';", Integer.class);
        if (i == 0){
            jdbcTemplate.execute("alter table "+prefix+"_apiconfig ADD `isPhone` int(2) DEFAULT '0' COMMENT '是否开启手机号支持'");
            text+="配置中心模块，字段isPhone添加完成。";
        }else{
            text+="配置中心模块，字段isPhone已经存在，无需添加。";
        }
        //查询配置中心表是否存在silenceTime字段
        i = jdbcTemplate.queryForObject("select count(*) from information_schema.columns where table_name = '"+prefix+"_apiconfig' and column_name = 'silenceTime';", Integer.class);
        if (i == 0){
            jdbcTemplate.execute("alter table "+prefix+"_apiconfig ADD `silenceTime` int(11) DEFAULT '600' COMMENT '疑似攻击自动封禁时间(s)'");
            text+="配置中心模块，字段silenceTime添加完成。";
        }else{
            text+="配置中心模块，字段silenceTime已经存在，无需添加。";
        }
        //查询配置中心表是否存在interceptTime字段
        i = jdbcTemplate.queryForObject("select count(*) from information_schema.columns where table_name = '"+prefix+"_apiconfig' and column_name = 'interceptTime';", Integer.class);
        if (i == 0){
            jdbcTemplate.execute("alter table "+prefix+"_apiconfig ADD `interceptTime` int(11) DEFAULT '3600' COMMENT '多次触发违规自动封禁时间(s)'");
            text+="配置中心模块，字段interceptTime添加完成。";
        }else{
            text+="配置中心模块，字段interceptTime已经存在，无需添加。";
        }
        //查询配置中心表是否存在isLogin字段
        i = jdbcTemplate.queryForObject("select count(*) from information_schema.columns where table_name = '"+prefix+"_apiconfig' and column_name = 'isLogin';", Integer.class);
        if (i == 0){
            jdbcTemplate.execute("alter table "+prefix+"_apiconfig ADD `isLogin` int(2) DEFAULT '0' COMMENT '开启全局登录'");
            text+="配置中心模块，字段isLogin添加完成。";
        }else{
            text+="配置中心模块，字段isLogin已经存在，无需添加。";
        }
        //查询配置中心表是否存在postMax字段
        i = jdbcTemplate.queryForObject("select count(*) from information_schema.columns where table_name = '"+prefix+"_apiconfig' and column_name = 'postMax';", Integer.class);
        if (i == 0){
            jdbcTemplate.execute("alter table "+prefix+"_apiconfig ADD `postMax` int(11) DEFAULT '999' COMMENT '每日最大发布'");
            text+="配置中心模块，字段postMax添加完成。";
        }else{
            text+="配置中心模块，字段postMax已经存在，无需添加。";
        }
        //查询配置中心表是否存在forumAudit字段
        i = jdbcTemplate.queryForObject("select count(*) from information_schema.columns where table_name = '"+prefix+"_apiconfig' and column_name = 'forumAudit';", Integer.class);
        if (i == 0){
            jdbcTemplate.execute("alter table "+prefix+"_apiconfig ADD `forumAudit` int(11) DEFAULT '1' COMMENT '帖子及帖子评论是否需要审核'");
            text+="配置中心模块，字段forumAudit添加完成。";
        }else{
            text+="配置中心模块，字段forumAudit已经存在，无需添加。";
        }
        //查询配置中心表是否存在spaceAudit字段
        i = jdbcTemplate.queryForObject("select count(*) from information_schema.columns where table_name = '"+prefix+"_apiconfig' and column_name = 'spaceAudit';", Integer.class);
        if (i == 0){
            jdbcTemplate.execute("alter table "+prefix+"_apiconfig ADD `spaceAudit` int(11) DEFAULT '0' COMMENT '动态是否需要审核'");
            text+="配置中心模块，字段spaceAudit添加完成。";
        }else{
            text+="配置中心模块，字段spaceAudit已经存在，无需添加。";
        }
        //查询配置中心表是否存在uploadType字段
        i = jdbcTemplate.queryForObject("select count(*) from information_schema.columns where table_name = '"+prefix+"_apiconfig' and column_name = 'uploadType';", Integer.class);
        if (i == 0){
            jdbcTemplate.execute("alter table "+prefix+"_apiconfig ADD `uploadType` varchar(100) DEFAULT 'local' COMMENT '上传类型'");
            text+="配置中心模块，字段uploadType添加完成。";
        }else{
            text+="配置中心模块，字段uploadType已经存在，无需添加。";
        }
        i = jdbcTemplate.queryForObject("select count(*) from information_schema.columns where table_name = '"+prefix+"_apiconfig' and column_name = 'banRobots';", Integer.class);
        if (i == 0){
            jdbcTemplate.execute("alter table "+prefix+"_apiconfig ADD `banRobots` int(2) DEFAULT '0' COMMENT '是否开启机器人严格限制模式'");
            text+="配置中心模块，字段banRobots添加完成。";
        }else{
            text+="配置中心模块，字段banRobots已经存在，无需添加。";
        }
        i = jdbcTemplate.queryForObject("select count(*) from information_schema.columns where table_name = '"+prefix+"_apiconfig' and column_name = 'adsGiftNum';", Integer.class);
        if (i == 0){
            jdbcTemplate.execute("alter table "+prefix+"_apiconfig ADD `adsGiftNum` int(11) DEFAULT '10' COMMENT '每日广告奖励次数'");
            text+="配置中心模块，字段adsGiftNum添加完成。";
        }else{
            text+="配置中心模块，字段adsGiftNum已经存在，无需添加。";
        }
        i = jdbcTemplate.queryForObject("select count(*) from information_schema.columns where table_name = '"+prefix+"_apiconfig' and column_name = 'adsGiftAward';", Integer.class);
        if (i == 0){
            jdbcTemplate.execute("alter table "+prefix+"_apiconfig ADD `adsGiftAward` int(11) DEFAULT '5' COMMENT '每日广告奖励额'");
            text+="配置中心模块，字段adsGiftAward添加完成。";
        }else{
            text+="配置中心模块，字段adsGiftAward已经存在，无需添加。";
        }
        i = jdbcTemplate.queryForObject("select count(*) from information_schema.columns where table_name = '"+prefix+"_apiconfig' and column_name = 'verifyLevel';", Integer.class);
        if (i == 0){
            jdbcTemplate.execute("alter table "+prefix+"_apiconfig ADD `verifyLevel` int(2) DEFAULT '1' COMMENT '图片验证等级（0关闭，1发信验证，2全局验证）'");
            text+="配置中心模块，字段verifyLevel添加完成。";
        }else{
            text+="配置中心模块，字段verifyLevel已经存在，无需添加。";
        }

        i = jdbcTemplate.queryForObject("select count(*) from information_schema.columns where table_name = '"+prefix+"_apiconfig' and column_name = 'rebateLevel';", Integer.class);
        if (i == 0){
            jdbcTemplate.execute("alter table "+prefix+"_apiconfig ADD `rebateLevel` int(2) DEFAULT '0' COMMENT '邀请返利等级（0关闭，1固定奖励，2分成奖励，3全局奖励）'");
            text+="配置中心模块，字段rebateLevel添加完成。";
        }else{
            text+="配置中心模块，字段rebateLevel已经存在，无需添加。";
        }

        i = jdbcTemplate.queryForObject("select count(*) from information_schema.columns where table_name = '"+prefix+"_apiconfig' and column_name = 'rebateNum';", Integer.class);
        if (i == 0){
            jdbcTemplate.execute("alter table "+prefix+"_apiconfig ADD `rebateNum` int(11) DEFAULT '10' COMMENT '固定奖励（多少平台货币）'");
            text+="配置中心模块，字段rebateNum添加完成。";
        }else{
            text+="配置中心模块，字段rebateNum已经存在，无需添加。";
        }

        i = jdbcTemplate.queryForObject("select count(*) from information_schema.columns where table_name = '"+prefix+"_apiconfig' and column_name = 'rebateProportion';", Integer.class);
        if (i == 0){
            jdbcTemplate.execute("alter table "+prefix+"_apiconfig ADD `rebateProportion` int(11) DEFAULT '5' COMMENT '翻译比例（百分之多少）'");
            text+="配置中心模块，字段rebateProportion添加完成。";
        }else{
            text+="配置中心模块，字段rebateProportion已经存在，无需添加。";
        }

        i = jdbcTemplate.queryForObject("select count(*) from information_schema.columns where table_name = '"+prefix+"_apiconfig' and column_name = 'uploadPicMax';", Integer.class);
        if (i == 0){
            jdbcTemplate.execute("alter table "+prefix+"_apiconfig ADD `uploadPicMax` int(11) DEFAULT '5' COMMENT '图片最大上传大小'");
            text+="配置中心模块，字段uploadPicMax添加完成。";
        }else{
            text+="配置中心模块，字段uploadPicMax已经存在，无需添加。";
        }

        i = jdbcTemplate.queryForObject("select count(*) from information_schema.columns where table_name = '"+prefix+"_apiconfig' and column_name = 'uploadMediaMax';", Integer.class);
        if (i == 0){
            jdbcTemplate.execute("alter table "+prefix+"_apiconfig ADD `uploadMediaMax` int(11) DEFAULT '50' COMMENT '媒体最大上传大小'");
            text+="配置中心模块，字段uploadMediaMax添加完成。";
        }else{
            text+="配置中心模块，字段uploadMediaMax已经存在，无需添加。";
        }

        i = jdbcTemplate.queryForObject("select count(*) from information_schema.columns where table_name = '"+prefix+"_apiconfig' and column_name = 'uploadFilesMax';", Integer.class);
        if (i == 0){
            jdbcTemplate.execute("alter table "+prefix+"_apiconfig ADD `uploadFilesMax` int(11) DEFAULT '20' COMMENT '其他文件最大上传大小'");
            text+="配置中心模块，字段uploadFilesMax添加完成。";
        }else{
            text+="配置中心模块，字段uploadFilesMax已经存在，无需添加。";
        }

        i = jdbcTemplate.queryForObject("select count(*) from information_schema.columns where table_name = '"+prefix+"_apiconfig' and column_name = 'forumReplyAudit';", Integer.class);
        if (i == 0){
            jdbcTemplate.execute("alter table "+prefix+"_apiconfig ADD `forumReplyAudit` int(2) DEFAULT '0' COMMENT '帖子评论是否需要审核'");
            text+="配置中心模块，字段forumReplyAudit添加完成。";
        }else{
            text+="配置中心模块，字段forumReplyAudit已经存在，无需添加。";
        }
        i = jdbcTemplate.queryForObject("select count(*) from information_schema.columns where table_name = '"+prefix+"_apiconfig' and column_name = 'banIP';", Integer.class);
        if (i == 0){
            jdbcTemplate.execute("alter table "+prefix+"_apiconfig ADD `banIP` text COMMENT '封禁IP列表'");
            text+="配置中心模块，字段banIP添加完成。";
        }else{
            text+="配置中心模块，字段banIP已经存在，无需添加。";
        }
        try {
            Thread.sleep(500);
        } catch (InterruptedException ie) {
            Thread.currentThread().interrupt();
        }
        //添加邀请码模块
        i = jdbcTemplate.queryForObject("select count(*) from information_schema.columns where table_name = '"+prefix+"_invitation';", Integer.class);
        if (i == 0){
            jdbcTemplate.execute("CREATE TABLE `"+prefix+"_invitation` (" +
                    "  `id` int(11) NOT NULL AUTO_INCREMENT," +
                    "  `code` varchar(255) DEFAULT NULL COMMENT '邀请码'," +
                    "  `created` int(10) DEFAULT '0' COMMENT '创建时间'," +
                    "  `uid` int(11) DEFAULT '0' COMMENT '创建者'," +
                    "  `status` int(2) DEFAULT '0' COMMENT '0未使用，1已使用'," +
                    "  PRIMARY KEY (`id`)" +
                    ") ENGINE=MyISAM DEFAULT CHARSET=utf8 COMMENT='邀请码';");
            text+="邀请码模块创建完成。";
        }else{
            text+="邀请码模块已经存在，无需添加。";
        }
        //添加付费广告模块
        i = jdbcTemplate.queryForObject("select count(*) from information_schema.columns where table_name = '"+prefix+"_ads';", Integer.class);
        if (i == 0){
            jdbcTemplate.execute("CREATE TABLE `"+prefix+"_ads` (" +
                    "  `aid` int(11) NOT NULL AUTO_INCREMENT," +
                    "  `name` varchar(255) DEFAULT '' COMMENT '广告名称'," +
                    "  `type` int(11) DEFAULT '0' COMMENT '广告类型（0推流，1横幅，2启动图，3轮播图）'," +
                    "  `img` varchar(500) DEFAULT NULL COMMENT '广告缩略图'," +
                    "  `close` int(10) DEFAULT '0' COMMENT '0代表永久，其它代表结束时间'," +
                    "  `created` int(10) unsigned DEFAULT '0' COMMENT '创建时间'," +
                    "  `price` int(11) unsigned DEFAULT '0' COMMENT '购买价格'," +
                    "  `intro` varchar(500) DEFAULT '' COMMENT '广告简介'," +
                    "  `urltype` int(11) DEFAULT '0' COMMENT '0为APP内部打开，1为跳出APP'," +
                    "  `url` text COMMENT '跳转Url'," +
                    "  `uid` int(11) DEFAULT '-1' COMMENT '发布人'," +
                    "  `status` int(2) DEFAULT '0' COMMENT '0审核中，1已公开，2已到期'," +
                    "  PRIMARY KEY (`aid`)" +
                    ") ENGINE=MyISAM DEFAULT CHARSET=utf8 COMMENT='广告表';");
            text+="付费广告模块创建完成。";
        }else{
            text+="付费广告模块已经存在，无需添加。";
        }
        //添加消息通知模块
        i = jdbcTemplate.queryForObject("select count(*) from information_schema.columns where table_name = '"+prefix+"_inbox';", Integer.class);
        if (i == 0){
            jdbcTemplate.execute("CREATE TABLE `"+prefix+"_inbox` (" +
                    "  `id` int(11) NOT NULL AUTO_INCREMENT," +
                    "  `type` varchar(255) DEFAULT NULL COMMENT '消息类型：system(系统消息)，comment(评论消息)，finance(财务消息)'," +
                    "  `uid` int(11) DEFAULT '0' COMMENT '消息发送人，0是平台'," +
                    "  `text` text COMMENT '消息内容（只有简略信息）'," +
                    "  `touid` int(11) NOT NULL DEFAULT '0' COMMENT '消息接收人uid'," +
                    "  `isread` int(2) DEFAULT '0' COMMENT '是否已读，0已读，1未读'," +
                    "  `value` int(11) DEFAULT '0' COMMENT '消息指向内容的id，根据类型跳转'," +
                    "  `created` int(10) unsigned DEFAULT '0' COMMENT '创建时间'," +
                    "  PRIMARY KEY (`id`)" +
                    ") ENGINE=MyISAM DEFAULT CHARSET=utf8 COMMENT='消息表';");
            text+="消息通知模块创建完成。";
        }else{
            text+="消息通知模块已经存在，无需添加。";
        }
        //查询消息通知模块是否存在cid字段
        i = jdbcTemplate.queryForObject("select count(*) from information_schema.columns where table_name = '"+prefix+"_inbox' and column_name = 'cid';", Integer.class);
        if (i == 0){
            jdbcTemplate.execute("alter table "+prefix+"_inbox ADD `cid` int(11) DEFAULT '0' COMMENT '次级消息内容ID'");
            text+="消息通知模块，字段cid添加完成。";
        }else{
            text+="消息通知模块，字段cid已经存在，无需添加。";
        }

        //关注模块
        i = jdbcTemplate.queryForObject("select count(*) from information_schema.columns where table_name = '"+prefix+"_fan';", Integer.class);
        if (i == 0){
            jdbcTemplate.execute("CREATE TABLE `"+prefix+"_fan` (" +
                    "  `id` int(11) NOT NULL AUTO_INCREMENT," +
                    "  `created` int(10) unsigned DEFAULT '0' COMMENT '关注时间'," +
                    "  `uid` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '关注人'," +
                    "  `touid` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '被关注人'," +
                    "  PRIMARY KEY (`id`)" +
                    ") ENGINE=MyISAM DEFAULT CHARSET=utf8 COMMENT='关注表（全局内容）';");
            text+="关注模块创建完成。";
        }else{
            text+="关注模块已经存在，无需添加。";
        }
        //违规记录模块
        i = jdbcTemplate.queryForObject("select count(*) from information_schema.columns where table_name = '"+prefix+"_violation';", Integer.class);
        if (i == 0){
            jdbcTemplate.execute("CREATE TABLE `"+prefix+"_violation` (" +
                    "  `id` int(11) NOT NULL AUTO_INCREMENT," +
                    "  `uid` int(11) NOT NULL DEFAULT '0' COMMENT '违规者uid'," +
                    "  `type` varchar(255) DEFAULT NULL COMMENT '处理类型（manager管理员操作，system系统自动）'," +
                    "  `text` text COMMENT '具体原因'," +
                    "  `created` int(10) unsigned DEFAULT '0' COMMENT '违规时间'," +
                    "  `handler` int(11) unsigned DEFAULT '0' COMMENT '处理人，0为系统自动，其它为真实用户'," +
                    "  PRIMARY KEY (`id`)" +
                    ") ENGINE=MyISAM DEFAULT CHARSET=utf8 COMMENT='违规记录表';");
            text+="违规记录模块创建完成。";
        }else{
            text+="违规记录模块已经存在，无需添加。";
        }
        //查询聊违规记录模块是否存在value字段
        i = jdbcTemplate.queryForObject("select count(*) from information_schema.columns where table_name = '"+prefix+"_violation' and column_name = 'value';", Integer.class);
        if (i == 0){
            jdbcTemplate.execute("alter table "+prefix+"_violation ADD `value` int(11) NOT NULL DEFAULT '0' COMMENT '预留字段，用于指定范围禁言'");
            text+="违规记录模块，字段value添加完成。";
        }else{
            text+="违规记录模块，字段value已经存在，无需添加。";
        }
        try {
            Thread.sleep(500);
        } catch (InterruptedException ie) {
            Thread.currentThread().interrupt();
        }
        //聊天室模块
        i = jdbcTemplate.queryForObject("select count(*) from information_schema.columns where table_name = '"+prefix+"_chat';", Integer.class);
        if (i == 0){
            jdbcTemplate.execute("CREATE TABLE `"+prefix+"_chat` (" +
                    "  `id` int(11) NOT NULL AUTO_INCREMENT," +
                    "  `chatid` varchar(255) DEFAULT NULL COMMENT '聊天室id（加密值）'," +
                    "  `uid` int(11) DEFAULT '0' COMMENT '创建者'," +
                    "  `toid` int(11) DEFAULT '0' COMMENT '也是创建者（和上一个字段共同判断私聊）'," +
                    "  `created` int(10) unsigned DEFAULT '0' COMMENT '创建时间'," +
                    "  `lastTime` int(10) unsigned DEFAULT '0' COMMENT '最后聊天时间'," +
                    "  `type` int(2) unsigned DEFAULT '0' COMMENT '0是私聊，1是群聊'," +
                    "  PRIMARY KEY (`id`)" +
                    ") ENGINE=MyISAM DEFAULT CHARSET=utf8 COMMENT='聊天室表';");
            text+="聊天室模块创建完成。";
        }else{
            text+="聊天室模块已经存在，无需添加。";
        }
        //查询聊天室模块是否存在name字段
        i = jdbcTemplate.queryForObject("select count(*) from information_schema.columns where table_name = '"+prefix+"_chat' and column_name = 'name';", Integer.class);
        if (i == 0){
            jdbcTemplate.execute("alter table "+prefix+"_chat ADD `name` varchar(400) DEFAULT NULL COMMENT '聊天室名称（群聊）'");
            text+="聊天室模块，字段name添加完成。";
        }else{
            text+="聊天室模块，字段name已经存在，无需添加。";
        }
        //查询聊天室模块是否存在pic字段
        i = jdbcTemplate.queryForObject("select count(*) from information_schema.columns where table_name = '"+prefix+"_chat' and column_name = 'pic';", Integer.class);
        if (i == 0){
            jdbcTemplate.execute("alter table "+prefix+"_chat ADD `pic` varchar(400) DEFAULT NULL COMMENT '图片地址（群聊）'");
            text+="聊天室模块，字段pic添加完成。";
        }else{
            text+="聊天室模块，字段pic已经存在，无需添加。";
        }
        //查询聊天室模块是否存在ban字段
        i = jdbcTemplate.queryForObject("select count(*) from information_schema.columns where table_name = '"+prefix+"_chat' and column_name = 'ban';", Integer.class);
        if (i == 0){
            jdbcTemplate.execute("alter table "+prefix+"_chat ADD `ban` int(11) unsigned DEFAULT '0' COMMENT '屏蔽和全体禁言，存操作人id'");
            text+="聊天室模块，字段ban添加完成。";
        }else{
            text+="聊天室模块，字段ban已经存在，无需添加。";
        }
        //查询聊天室模块是否存在myUnRead字段
        i = jdbcTemplate.queryForObject("select count(*) from information_schema.columns where table_name = '"+prefix+"_chat' and column_name = 'myUnRead';", Integer.class);
        if (i == 0){
            jdbcTemplate.execute("alter table "+prefix+"_chat ADD `myUnRead` int(11) unsigned DEFAULT '0' COMMENT '我未读（只对私聊生效）'");
            text+="聊天室模块，字段myUnRead添加完成。";
        }else{
            text+="聊天室模块，字段myUnRead已经存在，无需添加。";
        }
        //查询聊天室模块是否存在otherUnRead字段
        i = jdbcTemplate.queryForObject("select count(*) from information_schema.columns where table_name = '"+prefix+"_chat' and column_name = 'otherUnRead';", Integer.class);
        if (i == 0){
            jdbcTemplate.execute("alter table "+prefix+"_chat ADD `otherUnRead` int(11) unsigned DEFAULT '0' COMMENT '对方未读（只对私聊生效）'");
            text+="聊天室模块，字段otherUnRead添加完成。";
        }else{
            text+="聊天室模块，字段otherUnRead已经存在，无需添加。";
        }
        //聊天记录模块
        i = jdbcTemplate.queryForObject("select count(*) from information_schema.columns where table_name = '"+prefix+"_chat_msg';", Integer.class);
        if (i == 0){
            jdbcTemplate.execute("CREATE TABLE `"+prefix+"_chat_msg` (" +
                    "  `id` int(11) NOT NULL AUTO_INCREMENT," +
                    "  `uid` int(11) DEFAULT '0' COMMENT '发送人'," +
                    "  `cid` int(11) DEFAULT '0' COMMENT '聊天室'," +
                    "  `text` text CHARACTER SET utf8mb4 COMMENT '消息内容'," +
                    "  `created` int(10) unsigned DEFAULT '0' COMMENT '发送时间'," +
                    "  `type` int(2) unsigned DEFAULT '0' COMMENT '0文字消息，1图片消息，3视频消息，4系统提示'," +
                    "  `url` varchar(400) DEFAULT NULL COMMENT '为链接时的url'," +
                    "  PRIMARY KEY (`id`)" +
                    ") ENGINE=MyISAM DEFAULT CHARSET=utf8 COMMENT='聊天消息';");
            text+="聊天记录模块创建完成。";
        }else{
            text+="聊天记录模块已经存在，无需添加。";
        }
        //动态模块
        i = jdbcTemplate.queryForObject("select count(*) from information_schema.columns where table_name = '"+prefix+"_space';", Integer.class);
        if (i == 0){
            jdbcTemplate.execute("CREATE TABLE `"+prefix+"_space` (" +
                    "  `id` int(11) NOT NULL AUTO_INCREMENT," +
                    "  `uid` int(11) DEFAULT '0' COMMENT '发布者'," +
                    "  `created` int(10) unsigned DEFAULT '0' COMMENT '发布时间'," +
                    "  `modified` int(10) unsigned DEFAULT '0' COMMENT '修改时间'," +
                    "  `text` text CHARACTER SET utf8mb4 COMMENT '内容'," +
                    "  `pic` text COMMENT '图片或视频，自己拆分'," +
                    "  `type` int(2) DEFAULT NULL COMMENT '0普通动态，1转发和发布文章，2转发动态，3动态评论，4视频，5商品'," +
                    "  `likes` int(10) DEFAULT '0' COMMENT '喜欢动态的数量'," +
                    "  `toid` int(10) DEFAULT '0' COMMENT '文章id，动态id等'," +
                    "  PRIMARY KEY (`id`)" +
                    ") ENGINE=MyISAM DEFAULT CHARSET=utf8 COMMENT='个人动态表';");
            text+="动态模块创建完成。";
        }else{
            text+="动态模块已经存在，无需添加。";
        }
        //查询动态模块是否存在status字段
        i = jdbcTemplate.queryForObject("select count(*) from information_schema.columns where table_name = '"+prefix+"_space' and column_name = 'status';", Integer.class);
        if (i == 0){
            jdbcTemplate.execute("alter table "+prefix+"_space ADD `status` int(2) unsigned DEFAULT '1' COMMENT '动态状态，0审核，1发布，2锁定'");
            text+="动态模块，字段status添加完成。";
        }else{
            text+="动态模块，字段status已经存在，无需添加。";
        }
        //查询动态模块是否存在onlyMe字段
        i = jdbcTemplate.queryForObject("select count(*) from information_schema.columns where table_name = '"+prefix+"_space' and column_name = 'onlyMe';", Integer.class);
        if (i == 0){
            jdbcTemplate.execute("alter table "+prefix+"_space ADD `onlyMe` int(2) unsigned DEFAULT '0' COMMENT '仅自己可见'");
            text+="动态模块，字段onlyMe添加完成。";
        }else{
            text+="动态模块，字段onlyMe已经存在，无需添加。";
        }
        text+=" ------ 执行结束，安装执行完成";

        redisHelp.setRedis(this.dataprefix+"_"+"isInstall","1",60,redisTemplate);
        JSONObject response = new JSONObject();
        response.put("code" , 1);
        response.put("msg"  ,text);
        return response.toString();
    }
    /**
     * 安装扩展功能
     */
    @RequestMapping(value = "/proInstall")
    @ResponseBody
    @LoginRequired(purview = "-3")
    public String typechoInstall(@RequestParam(value = "webkey", required = false,defaultValue = "") String  webkey) {
        if(!webkey.equals(this.key)){
            return Result.getResultJson(0,"请输入正确的访问KEY。如果忘记，可在服务器/opt/application.properties中查看",null);
        }
        String isRepeated = redisHelp.getRedis("isProInstall",redisTemplate);
        if(isRepeated==null){
            redisHelp.setRedis("isProInstall","1",15,redisTemplate);
        }else{
            return Result.getResultJson(0,"你的操作太频繁了",null);
        }
        String text = "执行信息 ------";
        Integer i = 1;
        try {
            //安装帖子表
            i = jdbcTemplate.queryForObject("select count(*) from information_schema.columns where table_name = '"+prefix+"_forum';", Integer.class);
            if (i == 0){
                jdbcTemplate.execute("CREATE TABLE `"+prefix+"_forum` (" +
                        "  `id` int(11) NOT NULL AUTO_INCREMENT," +
                        "  `title` varchar(300) DEFAULT NULL COMMENT '帖子标题'," +
                        "  `section` int(11) DEFAULT '0' COMMENT '所属版块'," +
                        "  `typeid` int(11) DEFAULT '0' COMMENT '所属板块子类'," +
                        "  `created` int(10) unsigned DEFAULT '0' COMMENT '发布时间'," +
                        "  `modified` int(10) unsigned DEFAULT '0' COMMENT '修改时间'," +
                        "  `text` text CHARACTER SET utf8mb4 COMMENT '帖子内容'," +
                        "  `authorId` int(11) unsigned DEFAULT '0' COMMENT '作者uid'," +
                        "  `status` int(2) DEFAULT '0' COMMENT '帖子状态，0审核，1发布，2锁定'," +
                        "  `commentsNum` int(11) unsigned DEFAULT '0' COMMENT '回帖数量'," +
                        "  `views` int(11) DEFAULT '0' COMMENT '阅读数量'," +
                        "  `likes` int(11) DEFAULT '0' COMMENT '点赞数量'," +
                        "  `isTop` int(2) DEFAULT '0' COMMENT '是否置顶，0普通，1当前版块置顶，2全局置顶'," +
                        "  `isrecommend` int(2) DEFAULT '0' COMMENT '是否加精&推荐'," +
                        "  `isswiper` int(2) DEFAULT '0' COMMENT '是否轮播'," +
                        "  `replyTime` int(10) DEFAULT '0' COMMENT '回复时间'," +
                        "  PRIMARY KEY (`id`)" +
                        ") ENGINE=MyISAM DEFAULT CHARSET=utf8 COMMENT='圈子&论坛帖子';");
                text+="帖子表创建完成。";
            }else{
                text+="帖子表已存在，无需安装。";
            }
            //查询帖子模块是否存在isMd字段
            i = jdbcTemplate.queryForObject("select count(*) from information_schema.columns where table_name = '"+prefix+"_forum' and column_name = 'isMd';", Integer.class);
            if (i == 0){
                jdbcTemplate.execute("alter table "+prefix+"_forum ADD `isMd` int(2) unsigned DEFAULT '1' COMMENT '是否为Markdown编辑器发布'");
                text+="帖子表，字段isMd添加完成。";
            }else{
                text+="帖子表，字段isMd已经存在，无需添加。";
            }
            //查询聊天室模块是否存在isMd字段
            i = jdbcTemplate.queryForObject("select count(*) from information_schema.columns where table_name = '"+prefix+"_forum' and column_name = 'sid';", Integer.class);
            if (i == 0){
                jdbcTemplate.execute("alter table "+prefix+"_forum ADD `sid` int(2) unsigned DEFAULT '0' COMMENT '商品ID，用于付费阅读'");
                text+="帖子表，字段sid添加完成。";
            }else{
                text+="帖子表，字段sid已经存在，无需添加。";
            }

            //安装帖子评论表
            i = jdbcTemplate.queryForObject("select count(*) from information_schema.columns where table_name = '"+prefix+"_forum_comment';", Integer.class);
            if (i == 0) {
                jdbcTemplate.execute("CREATE TABLE `" + prefix + "_forum_comment` (" +
                        "  `id` int(11) NOT NULL AUTO_INCREMENT," +
                        "  `uid` int(11) DEFAULT '0' COMMENT '评论者'," +
                        "  `created` int(10) unsigned DEFAULT '0' COMMENT '评论时间'," +
                        "  `forumid` int(11) unsigned DEFAULT '0' COMMENT '所属帖子'," +
                        "  `likes` int(11) unsigned DEFAULT '0' COMMENT '评论点赞'," +
                        "  `text` text CHARACTER SET utf8mb4 COMMENT '评论内容'," +
                        "  `parent` int(11) unsigned DEFAULT '0' COMMENT '上级评论'," +
                        "  `section` int(11) unsigned DEFAULT '0' COMMENT '所属版块'," +
                        "  PRIMARY KEY (`id`)" +
                        ") ENGINE=MyISAM DEFAULT CHARSET=utf8 COMMENT='论坛评论';");
                text += "帖子评论表创建完成。";
            }else{
                text+="帖子评论表已存在，无需安装。";
            }
            //查询版块表是否存在isrecommend字段
            i = jdbcTemplate.queryForObject("select count(*) from information_schema.columns where table_name = '"+prefix+"_forum_comment' and column_name = 'status';", Integer.class);
            if (i == 0){
                jdbcTemplate.execute("alter table "+prefix+"_forum_comment ADD `status` int(2) DEFAULT '1' COMMENT '帖子评论状态，0为未审核，1为显示'");
                text+="帖子评论表，字段status添加完成。";
            }else{
                text+="帖子评论表，字段status已经存在，无需添加。";
            }
            //安装论坛权限表
            i = jdbcTemplate.queryForObject("select count(*) from information_schema.columns where table_name = '"+prefix+"_forum_moderator';", Integer.class);
            if (i == 0) {
                jdbcTemplate.execute("CREATE TABLE `" + prefix + "_forum_moderator` (" +
                        "  `id` int(11) NOT NULL AUTO_INCREMENT," +
                        "  `sectionId` int(11) DEFAULT '0' COMMENT '版块id'," +
                        "  `uid` int(11) DEFAULT '0' COMMENT '用户id'," +
                        "  `purview` int(11) DEFAULT '0' COMMENT '权限等级依次（执行者，审核者，小版主，版主，大版主）'," +
                        "  PRIMARY KEY (`id`)" +
                        ") ENGINE=MyISAM DEFAULT CHARSET=utf8 COMMENT='版主表（映射会员表）';");
                text += "论坛权限表创建完成。";
            }else{
                text+="论坛权限表已存在，无需安装。";
            }
            //安装版块表
            i = jdbcTemplate.queryForObject("select count(*) from information_schema.columns where table_name = '"+prefix+"_forum_section';", Integer.class);
            if (i == 0) {
                jdbcTemplate.execute("CREATE TABLE `" + prefix + "_forum_section` (" +
                        "  `id` int(11) NOT NULL AUTO_INCREMENT," +
                        "  `name` varchar(300) DEFAULT NULL COMMENT '版块名称'," +
                        "  `pic` varchar(300) DEFAULT NULL COMMENT '版块图片'," +
                        "  `bg` varchar(300) DEFAULT NULL COMMENT '板块背景图'," +
                        "  `text` varchar(300) CHARACTER SET utf8mb4 DEFAULT NULL COMMENT '版块介绍'," +
                        "  `type` varchar(20) DEFAULT 'section' COMMENT 'sort为大类，section为版块'," +
                        "  `restrict` int(11) DEFAULT '0' COMMENT '发帖等级（根据权限表限制）'," +
                        "  `parent` int(11) DEFAULT '0' COMMENT '上级板块'," +
                        "  `slug` varchar(255) DEFAULT NULL COMMENT '缩略名'," +
                        "  `order` int(11) DEFAULT '0' COMMENT '排序'," +
                        "  PRIMARY KEY (`id`)" +
                        ") ENGINE=MyISAM DEFAULT CHARSET=utf8 COMMENT='论坛版块';");
                text += "版块表创建完成。";
            }else{
                text+="版块表已存在，无需安装。";
            }
            //查询版块表是否存在isrecommend字段
            i = jdbcTemplate.queryForObject("select count(*) from information_schema.columns where table_name = '"+prefix+"_forum_section' and column_name = 'isrecommend';", Integer.class);
            if (i == 0){
                jdbcTemplate.execute("alter table "+prefix+"_forum_section ADD `isrecommend` int(2) DEFAULT '0' COMMENT '是否推荐'");
                text+="版块表，字段isrecommend添加完成。";
            }else{
                text+="版块表，字段isrecommend已经存在，无需添加。";
            }

            //安装VIP类型表
            i = jdbcTemplate.queryForObject("select count(*) from information_schema.columns where table_name = '"+prefix+"_vips';", Integer.class);
            if (i == 0) {
                jdbcTemplate.execute("CREATE TABLE `" + prefix + "_vips` (" +
                        "  `id` int(11) NOT NULL AUTO_INCREMENT," +
                        "  `orderKey` int(11) DEFAULT '0' COMMENT '排序，越大越靠前'," +
                        "  `name` varchar(255) DEFAULT NULL COMMENT '套餐名称（如：月付VIP）'," +
                        "  `price` int(11) DEFAULT '0' COMMENT '套餐价格，正整数'," +
                        "  `day` int(11) DEFAULT '0' COMMENT '获得VIP天数'," +
                        "  `giftDay` int(11) DEFAULT '0' COMMENT '额外奖励天数，为0则不奖励'," +
                        "  `intro` varchar(400) DEFAULT NULL COMMENT '套餐介绍'," +
                        "  PRIMARY KEY (`id`)" +
                        ") ENGINE=MyISAM DEFAULT CHARSET=utf8 COMMENT='VIP类型表';");
                text += "VIP类型表创建完成。";
            }else{
                text+="VIP类型表已存在，无需安装。";
            }
            //安装应用表
            i = jdbcTemplate.queryForObject("select count(*) from information_schema.columns where table_name = '"+prefix+"_app';", Integer.class);
            if (i == 0) {
                jdbcTemplate.execute("CREATE TABLE `" + prefix + "_app` (" +
                        "  `id` int(11) NOT NULL AUTO_INCREMENT," +
                        "  `key` varchar(255) DEFAULT NULL COMMENT '链接密钥'," +
                        "  `name` varchar(255) CHARACTER SET utf8 DEFAULT NULL COMMENT '应用名称'," +
                        "  `type` varchar(255) CHARACTER SET utf8 DEFAULT 'app' COMMENT '应用类型（web或App）'," +
                        "  `logo` varchar(500) CHARACTER SET utf8 DEFAULT NULL COMMENT 'logo图标地址'," +
                        "  `keywords` varchar(255) CHARACTER SET utf8 DEFAULT NULL COMMENT 'web专属，SEO关键词'," +
                        "  `description` varchar(255) DEFAULT NULL COMMENT '应用简介'," +
                        "  `announcement` varchar(400) DEFAULT NULL COMMENT '弹窗公告（支持html）'," +
                        "  `mail` varchar(400) CHARACTER SET utf8 DEFAULT NULL COMMENT '邮箱地址（用于通知和显示）'," +
                        "  `website` varchar(400) CHARACTER SET utf8 DEFAULT NULL COMMENT '网址（非Api地址）'," +
                        "  `currencyName` varchar(255) CHARACTER SET utf8 DEFAULT NULL COMMENT '货币名称'," +
                        "  `version` varchar(255) CHARACTER SET utf8 DEFAULT 'v1.0.0 beta' COMMENT 'app专属，版本号'," +
                        "  `versionCode` int(11) DEFAULT '10' COMMENT 'app专属，版本码'," +
                        "  `versionIntro` varchar(400) DEFAULT NULL COMMENT '版本简介'," +
                        "  `androidUrl` varchar(400) CHARACTER SET utf8 DEFAULT NULL COMMENT '安卓下载地址'," +
                        "  `iosUrl` varchar(255) CHARACTER SET utf8 DEFAULT NULL COMMENT 'ios下载地址'," +
                        "  `field1` varchar(400) CHARACTER SET utf8 DEFAULT NULL COMMENT '预留字段1'," +
                        "  `field2` varchar(400) CHARACTER SET utf8 DEFAULT NULL COMMENT '预留字段2'," +
                        "  PRIMARY KEY (`id`)" +
                        ") ENGINE=MyISAM DEFAULT CHARSET=utf8mb4 COMMENT='应用表（web应用和APP应用）';");
                text += "应用表创建完成。";
            }else{
                text+="应用表已存在，无需安装。";
            }
            //查询应用表是否存在adpid字段
            i = jdbcTemplate.queryForObject("select count(*) from information_schema.columns where table_name = '"+prefix+"_app' and column_name = 'adpid';", Integer.class);
            if (i == 0){
                jdbcTemplate.execute("alter table "+prefix+"_app ADD `adpid` varchar(255) CHARACTER SET utf8 DEFAULT NULL COMMENT '广告联盟ID'");
                text+="应用表，字段adpid添加完成。";
            }else{
                text+="应用表，字段adpid已经存在，无需添加。";
            }
            //安装商品分类表
            i = jdbcTemplate.queryForObject("select count(*) from information_schema.columns where table_name = '"+prefix+"_shoptype';", Integer.class);
            if (i == 0) {
                jdbcTemplate.execute("CREATE TABLE `" + prefix + "_shoptype` (" +
                        "  `id` int(11) NOT NULL AUTO_INCREMENT," +
                        "  `parent` int(11) DEFAULT '0' COMMENT '上级分类'," +
                        "  `name` varchar(255) DEFAULT NULL COMMENT '分类名称'," +
                        "  `pic` varchar(400) DEFAULT NULL COMMENT '分类缩略图'," +
                        "  `intro` varchar(400) DEFAULT NULL COMMENT '分类简介'," +
                        "  `orderKey` int(11) DEFAULT '0' COMMENT '分类排序'," +
                        "  PRIMARY KEY (`id`)" +
                        ") ENGINE=MyISAM AUTO_INCREMENT=7 DEFAULT CHARSET=utf8 COMMENT='商品分类表';");
                text += "商品分类表创建完成。";
            }else{
                text+="商品分类表已存在，无需安装。";
            }
            //安装邮件模板表
            i = jdbcTemplate.queryForObject("select count(*) from information_schema.columns where table_name = '"+prefix+"_emailtemplate';", Integer.class);
            if (i == 0) {
                jdbcTemplate.execute("CREATE TABLE `" + prefix + "_emailtemplate` (" +
                        "  `id` int(11) NOT NULL AUTO_INCREMENT," +
                        "  `verifyTemplate` text COMMENT '验证码模板'," +
                        "  `reviewTemplate` text COMMENT '审核通知模板'," +
                        "  `safetyTemplate` text COMMENT '安全通知模板'," +
                        "  `replyTemplate` text COMMENT '评论&回复通知模板'," +
                        "  `orderTemplate` text COMMENT '订单通知模板'," +
                        "  PRIMARY KEY (`id`)" +
                        ") ENGINE=MyISAM DEFAULT CHARSET=utf8mb4 COMMENT='邮件模板';");
                //
                jdbcTemplate.execute("INSERT INTO `" + prefix + "_emailtemplate` " +
                        "(`verifyTemplate`, `reviewTemplate`, `safetyTemplate`, `replyTemplate`, `orderTemplate`) VALUES " +
                        "('尊敬的用户{{userName}}，您的验证码为{{code}}。验证码将在十分钟后失效，请尽快进行验证，并不要透露给他人'," +
                        "'尊敬的用户{{userName}}，您的内容【{{title}}】，{{reviewText}}。'," +
                        "'尊敬的用户{{userName}}，安全通知：{{safetyText}}。'," +
                        "'尊敬的用户{{userName}}，您的内容【{{title}}】有了新的回复：{{replyText}}。'," +
                        "'尊敬的用户{{userName}}，您的商品【{{title}}】有了新的订单。')");
                text += "安装邮件模板表创建完成。";
            }else{
                text+="安装邮件模板表已存在，无需安装。";
            }
            //安装实名认证相关表（代码由群友月亮赞助）
            //安装企业认证表
            i = jdbcTemplate.queryForObject("select count(*) from information_schema.columns where table_name = '"+prefix+"_company';", Integer.class);
            if (i == 0) {
                jdbcTemplate.execute("CREATE TABLE `" + prefix + "_company` (" +
                        "  `uid` int(11) NOT NULL COMMENT '用户ID'," +
                        "  `regno` varchar(50) NOT NULL COMMENT '企业工商注册号/统一信用代码'," +
                        "  `name` varchar(50) DEFAULT NULL COMMENT '企业法人姓名'," +
                        "  `entname` varchar(50) DEFAULT NULL COMMENT '企业名称'," +
                        "  `idcard` varchar(50) DEFAULT NULL COMMENT '企业法人身份证号'," +
                        "  `identifyStatus` varchar(10) DEFAULT NULL COMMENT '认证状态：1成功、0失败'," +
                        "  PRIMARY KEY (`uid`,`regno`)" +
                        ") ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;");
                text += "企业认证表创建完成。";
            }else{
                text+="企业认证表已存在，无需安装。";
            }
            //安装个人认证表
            i = jdbcTemplate.queryForObject("select count(*) from information_schema.columns where table_name = '"+prefix+"_consumer';", Integer.class);
            if (i == 0) {
                jdbcTemplate.execute("CREATE TABLE `" + prefix + "_consumer` (" +
                        "  `uid` int(10) NOT NULL COMMENT '用户编码'," +
                        "  `idCard` varchar(50) DEFAULT NULL COMMENT '身份证号'," +
                        "  `name` varchar(50) DEFAULT NULL COMMENT '姓名'," +
                        "  `identifyStatus` varchar(20) DEFAULT NULL COMMENT '审批状态，1为成功、0为失败'," +
                        "  PRIMARY KEY (`uid`)" +
                        ") ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;");
                text += "个人认证表创建完成。";
            }else{
                text+="个人认证表已存在，无需安装。";
            }

            //安装AI大模型表
            i = jdbcTemplate.queryForObject("select count(*) from information_schema.columns where table_name = '"+prefix+"_gpt';", Integer.class);
            if (i == 0) {
                jdbcTemplate.execute("CREATE TABLE `" + prefix + "_gpt` (" +
                        "  `id` int(11) NOT NULL AUTO_INCREMENT," +
                        "  `name` varchar(255) DEFAULT '' COMMENT '模型名称'," +
                        "  `source` varchar(255) DEFAULT 'Qwen' COMMENT '模型来源'," +
                        "  `isVip` int(11) DEFAULT '0' COMMENT '是否仅VIP可用'," +
                        "  `price` int(11) DEFAULT '0' COMMENT '每次请求消耗多少金币'," +
                        "  `avatar` varchar(300) DEFAULT NULL COMMENT '模型头像'," +
                        "  `intro` varchar(500) DEFAULT '' COMMENT '模型简介'," +
                        "  `created` int(10) unsigned DEFAULT '0' COMMENT '创建时间'," +
                        "  `appId` varchar(255) DEFAULT '' COMMENT '大模型渠道appId'," +
                        "  `apiKey` varchar(255) DEFAULT '' COMMENT '大模型渠道apiKey'," +
                        "  `type` int(10) unsigned DEFAULT '0' COMMENT '应用类型 0是聊天大模型 1是AI应用'," +
                        "  `prompt` text COMMENT 'prompt 仅AI应用需要设置'," +
                        "  PRIMARY KEY (`id`)" +
                        ") ENGINE=MyISAM DEFAULT CHARSET=utf8mb4 COMMENT='AI大模型表';");
                text += "AI大模型表创建完成。";
            }else{
                text+="AI大模型表已存在，无需安装。";
            }
            //安装AI聊天室表
            i = jdbcTemplate.queryForObject("select count(*) from information_schema.columns where table_name = '"+prefix+"_gpt_chat';", Integer.class);
            if (i == 0) {
                jdbcTemplate.execute("CREATE TABLE `" + prefix + "_gpt_chat` (" +
                        "  `id` int(11) NOT NULL AUTO_INCREMENT," +
                        "  `gptid` int(11) DEFAULT '0' COMMENT '大模型id'," +
                        "  `uid` int(11) DEFAULT '0' COMMENT '用户ID'," +
                        "  `sessionId` varchar(255) DEFAULT NULL COMMENT '聊天sessionId'," +
                        "  `created` int(10) unsigned DEFAULT '0' COMMENT '创建时间'," +
                        "  `replyTime` int(10) DEFAULT '0' COMMENT '最新回复时间'," +
                        "  PRIMARY KEY (`id`)" +
                        ") ENGINE=MyISAM DEFAULT CHARSET=utf8mb4 COMMENT='gpt聊天室';");
                text += "AI聊天室表创建完成。";
            }else{
                text+="AI聊天室表已存在，无需安装。";
            }

            //安装AI聊天消息表
            i = jdbcTemplate.queryForObject("select count(*) from information_schema.columns where table_name = '"+prefix+"_gpt_msg';", Integer.class);
            if (i == 0) {
                jdbcTemplate.execute("CREATE TABLE `" + prefix + "_gpt_msg` (" +
                        "  `id` int(11) NOT NULL AUTO_INCREMENT," +
                        "  `uid` int(11) DEFAULT '0' COMMENT '发送人'," +
                        "  `gptid` int(11) DEFAULT '0' COMMENT '所选GPT'," +
                        "  `text` text COMMENT '消息内容'," +
                        "  `created` int(10) unsigned DEFAULT '0' COMMENT '创建时间'," +
                        "  `isAI` int(1) DEFAULT '0' COMMENT '是否为AI回复'," +
                        "  PRIMARY KEY (`id`)" +
                        ") ENGINE=MyISAM DEFAULT CHARSET=utf8mb4 COMMENT='gpt聊天消息';");
                text += "AI聊天消息表创建完成。";
            }else{
                text+="AI聊天消息表已存在，无需安装。";
            }
            //配置接口实名相关再补充
            i = jdbcTemplate.queryForObject("select count(*) from information_schema.columns where table_name = '"+prefix+"_apiconfig' and column_name = 'identifyiLv';", Integer.class);
            if (i == 0){
                jdbcTemplate.execute("alter table "+prefix+"_apiconfig ADD `identifyiLv` int(2) DEFAULT '0' COMMENT '实名认证等级，0关闭，1个人，2企业，3全部'");
                text+="配置中心模块，字段identifyiLv添加完成。";
            }else{
                text+="配置中心模块，字段identifyiLv已经存在，无需添加。";
            }
            i = jdbcTemplate.queryForObject("select count(*) from information_schema.columns where table_name = '"+prefix+"_apiconfig' and column_name = 'identifyiIdcardHost';", Integer.class);
            if (i == 0){
                jdbcTemplate.execute("alter table "+prefix+"_apiconfig ADD `identifyiIdcardHost` varchar(255) DEFAULT 'https://idcert.market.alicloudapi.com' COMMENT '个人认证接口地址'");
                text+="配置中心模块，字段identifyiIdcardHost添加完成。";
            }else{
                text+="配置中心模块，字段identifyiIdcardHost已经存在，无需添加。";
            }
            i = jdbcTemplate.queryForObject("select count(*) from information_schema.columns where table_name = '"+prefix+"_apiconfig' and column_name = 'identifyiIdcardPath';", Integer.class);
            if (i == 0){
                jdbcTemplate.execute("alter table "+prefix+"_apiconfig ADD `identifyiIdcardPath` varchar(255) DEFAULT '/idcard' COMMENT '个人认证接口路径'");
                text+="配置中心模块，字段identifyiIdcardPath添加完成。";
            }else{
                text+="配置中心模块，字段identifyiIdcardPath已经存在，无需添加。";
            }
            i = jdbcTemplate.queryForObject("select count(*) from information_schema.columns where table_name = '"+prefix+"_apiconfig' and column_name = 'identifyiIdcardAppcode';", Integer.class);
            if (i == 0){
                jdbcTemplate.execute("alter table "+prefix+"_apiconfig ADD `identifyiIdcardAppcode` text COMMENT '个人认证APPCode'");
                text+="配置中心模块，字段identifyiIdcardAppcode添加完成。";
            }else{
                text+="配置中心模块，字段identifyiIdcardAppcode已经存在，无需添加。";
            }
            i = jdbcTemplate.queryForObject("select count(*) from information_schema.columns where table_name = '"+prefix+"_apiconfig' and column_name = 'identifyiCompanyHost';", Integer.class);
            if (i == 0){
                jdbcTemplate.execute("alter table "+prefix+"_apiconfig ADD `identifyiCompanyHost` varchar(255) DEFAULT 'https://qy4ys.market.alicloudapi.com' COMMENT '企业认证接口地址'");
                text+="配置中心模块，字段identifyiCompanyHost添加完成。";
            }else{
                text+="配置中心模块，字段identifyiCompanyHost已经存在，无需添加。";
            }
            i = jdbcTemplate.queryForObject("select count(*) from information_schema.columns where table_name = '"+prefix+"_apiconfig' and column_name = 'identifyiCompanyPath';", Integer.class);
            if (i == 0){
                jdbcTemplate.execute("alter table "+prefix+"_apiconfig ADD `identifyiCompanyPath` varchar(255) DEFAULT '/qysys/dmp/api/jinrun.company.company.elements4' COMMENT '企业认证接口路径'");
                text+="配置中心模块，字段identifyiCompanyPath添加完成。";
            }else{
                text+="配置中心模块，字段identifyiCompanyPath已经存在，无需添加。";
            }
            i = jdbcTemplate.queryForObject("select count(*) from information_schema.columns where table_name = '"+prefix+"_apiconfig' and column_name = 'identifyiCompanyAppcode';", Integer.class);
            if (i == 0){
               jdbcTemplate.execute("alter table "+prefix+"_apiconfig ADD `identifyiCompanyAppcode` text COMMENT '企业认证APPCode'");
                text+="配置中心模块，字段identifyiCompanyAppcode添加完成。";
            }else{
                text+="配置中心模块，字段identifyiCompanyAppcode已经存在，无需添加。";
            }

            //签到奖励积分
            i = jdbcTemplate.queryForObject("select count(*) from information_schema.columns where table_name = '"+prefix+"_apiconfig' and column_name = 'clockPoints';", Integer.class);
            if (i == 0){
                jdbcTemplate.execute("alter table "+prefix+"_apiconfig ADD `clockPoints` int(11) DEFAULT '0' COMMENT '签到奖励积分数量'");
                text+="配置中心模块，字段clockPoints添加完成。";
            }else{
                text+="配置中心模块，字段clockPoints已经存在，无需添加。";
            }
            i = jdbcTemplate.queryForObject("select count(*) from information_schema.columns where table_name = '"+prefix+"_apiconfig' and column_name = 'identifylvPost';", Integer.class);
            if (i == 0){
                jdbcTemplate.execute("alter table "+prefix+"_apiconfig ADD `identifylvPost` int(2) DEFAULT '0' COMMENT '蓝V认证后才允许发布内容'");
                text+="配置中心模块，字段identifylvPost添加完成。";
            }else{
                text+="配置中心模块，字段identifylvPost已经存在，无需添加。";
            }
            i = jdbcTemplate.queryForObject("select count(*) from information_schema.columns where table_name = '"+prefix+"_apiconfig' and column_name = 'identifysmPost';", Integer.class);
            if (i == 0){
                jdbcTemplate.execute("alter table "+prefix+"_apiconfig ADD `identifysmPost` int(2) DEFAULT '0' COMMENT '实名认证后才允许发布内容'");
                text+="配置中心模块，字段identifysmPost添加完成。";
            }else{
                text+="配置中心模块，字段identifysmPost已经存在，无需添加。";
            }
            i = jdbcTemplate.queryForObject("select count(*) from information_schema.columns where table_name = '"+prefix+"_apiconfig' and column_name = 'cmsSecretId';", Integer.class);
            if (i == 0){
                jdbcTemplate.execute("alter table "+prefix+"_apiconfig ADD `cmsSecretId` text COMMENT '腾讯云内容安全SecretId'");
                text+="配置中心模块，字段cmsSecretId添加完成。";
            }else{
                text+="配置中心模块，字段cmsSecretId已经存在，无需添加。";
            }
            i = jdbcTemplate.queryForObject("select count(*) from information_schema.columns where table_name = '"+prefix+"_apiconfig' and column_name = 'cmsSecretKey';", Integer.class);
            if (i == 0){
                jdbcTemplate.execute("alter table "+prefix+"_apiconfig ADD `cmsSecretKey` text COMMENT '腾讯云内容安全SecretKey'");
                text+="配置中心模块，字段cmsSecretKey添加完成。";
            }else{
                text+="配置中心模块，字段cmsSecretKey已经存在，无需添加。";
            }
            i = jdbcTemplate.queryForObject("select count(*) from information_schema.columns where table_name = '"+prefix+"_apiconfig' and column_name = 'cmsRegion';", Integer.class);
            if (i == 0){
                jdbcTemplate.execute("alter table "+prefix+"_apiconfig ADD `cmsRegion` varchar(255) DEFAULT 'ap-shanghai' COMMENT '腾讯云内容安全接口地域'");
                text+="配置中心模块，字段cmsRegion添加完成。";
            }else{
                text+="配置中心模块，字段cmsRegion已经存在，无需添加。";
            }
            i = jdbcTemplate.queryForObject("select count(*) from information_schema.columns where table_name = '"+prefix+"_apiconfig' and column_name = 'cmsSwitch';", Integer.class);
            if (i == 0){
                jdbcTemplate.execute("alter table "+prefix+"_apiconfig ADD `cmsSwitch` int(2) DEFAULT '0' COMMENT '腾讯云内容安全开关（0关闭，1文本，2图片，3全部）'");
                text+="配置中心模块，字段cmsSwitch添加完成。";
            }else{
                text+="配置中心模块，字段cmsSwitch已经存在，无需添加。";
            }
            i = jdbcTemplate.queryForObject("select count(*) from information_schema.columns where table_name = '"+prefix+"_apiconfig' and column_name = 'localPath';", Integer.class);
            if (i == 0){
                jdbcTemplate.execute("alter table "+prefix+"_apiconfig ADD `localPath` text COMMENT '本地存储地址'");
                text+="配置中心模块，字段localPath添加完成。";
            }else{
                text+="配置中心模块，字段localPath已经存在，无需添加。";
            }
            i = jdbcTemplate.queryForObject("select count(*) from information_schema.columns where table_name = '"+prefix+"_apiconfig' and column_name = 'adsVideoType';", Integer.class);
            if (i == 0){
                jdbcTemplate.execute("alter table "+prefix+"_apiconfig ADD `adsVideoType` int(2) DEFAULT '1' COMMENT '激励广告模式（0前端回调，1服务端回调）'");
                text+="配置中心模块，字段adsVideoType添加完成。";
            }else{
                text+="配置中心模块，字段adsVideoType已经存在，无需添加。";
            }
            i = jdbcTemplate.queryForObject("select count(*) from information_schema.columns where table_name = '"+prefix+"_apiconfig' and column_name = 'adsSecuritykey';", Integer.class);
            if (i == 0){
               jdbcTemplate.execute("alter table "+prefix+"_apiconfig ADD `adsSecuritykey` text COMMENT '激励广告安全码'");
                text+="配置中心模块，字段adsSecuritykey添加完成。";
            }else{
                text+="配置中心模块，字段adsSecuritykey已经存在，无需添加。";
            }

            //StarPro开始安装
            //检查Sy_Articlelog表
            i = jdbcTemplate.queryForObject("select count(*) from information_schema.columns where table_name = 'Sy_Articlelog';", Integer.class);
            if (i == 0) {
                jdbcTemplate.execute("CREATE TABLE `Sy_Articlelog` (" +
                        "`id` int(11) NOT NULL AUTO_INCREMENT," +
                        " `uid` varchar(240) NOT NULL," +
                        "`time` date NOT NULL," +
                        "  PRIMARY KEY (`id`)" +
                        ") ENGINE=InnoDB DEFAULT CHARSET=utf8;");

                text += "|StarPro开始安装| 文章日志表创建完成。";
            }else{
                text+="|StarPro开始安装| 文章日志表已存在，无需安装。";
            }
            //检查Sy_Forumlog表
            i = jdbcTemplate.queryForObject("select count(*) from information_schema.columns where table_name = 'Sy_Forumlog';", Integer.class);
            if (i == 0) {
                jdbcTemplate.execute("CREATE TABLE `Sy_Forumlog` (" +
                        "`id` int(11) NOT NULL AUTO_INCREMENT," +
                        " `uid` varchar(240) NOT NULL," +
                        "`time` date NOT NULL," +
                        "  PRIMARY KEY (`id`)" +
                        ") ENGINE=InnoDB DEFAULT CHARSET=utf8;");

                text += "帖子日志表创建完成。";
            }else{
                text+="帖子日志表已存在，无需安装。";
            }
            //检查Sy_banip表
            i = jdbcTemplate.queryForObject("select count(*) from information_schema.columns where table_name = 'Sy_banip';", Integer.class);
            if (i == 0) {
                jdbcTemplate.execute("CREATE TABLE `Sy_banip` (" +
                        " `id` int(11) NOT NULL AUTO_INCREMENT," +
                        " `ipAdd` varchar(100) NOT NULL COMMENT 'ip归属地'," +
                        "`Time` varchar(200) NOT NULL COMMENT '时间'," +
                        "`State` text NOT NULL COMMENT '拉黑ip'," +
                        "`text` varchar(100) NOT NULL COMMENT '备注'," +
                        "  PRIMARY KEY (`id`)" +
                        " ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;");

                text += "禁ip表创建完成。";
            }else{
                text+="禁ip表已存在，无需安装。";
            }
            //检查Sy_Commentslog表
            i = jdbcTemplate.queryForObject("select count(*) from information_schema.columns where table_name = 'Sy_Commentslog';", Integer.class);
            if (i == 0) {
                jdbcTemplate.execute("CREATE TABLE `Sy_Commentslog` (" +
                        "  `id` int(11) NOT NULL AUTO_INCREMENT," +
                        "  `uid` varchar(240) CHARACTER SET utf8 NOT NULL," +
                        "  `time` date NOT NULL," +
                        "  PRIMARY KEY (`id`)" +
                        ") ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;");
                text += "评论日志表创建完成。";
            }else{
                text+="评论日志表已存在，无需安装。";
            }
            //检查Sy_Dynamiclog表
            i = jdbcTemplate.queryForObject("select count(*) from information_schema.columns where table_name = 'Sy_Dynamiclog';", Integer.class);
            if (i == 0) {
                jdbcTemplate.execute("CREATE TABLE `Sy_Dynamiclog` (" +
                        "  `id` int(11) NOT NULL AUTO_INCREMENT," +
                        "  `uid` varchar(240) CHARACTER SET utf8 NOT NULL," +
                        "  `time` date NOT NULL," +
                        "  PRIMARY KEY (`id`)" +
                        ") ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;");
                text += "动态日志表创建完成。";
            }else{
                text+="动态日志表已存在，无需安装。";
            }
            //检查Sy_Followlog表
            i = jdbcTemplate.queryForObject("select count(*) from information_schema.columns where table_name = 'Sy_Followlog';", Integer.class);
            if (i == 0) {
                jdbcTemplate.execute("CREATE TABLE `Sy_Followlog` (" +
                        "  `id` int(11) NOT NULL AUTO_INCREMENT," +
                        "  `uid` varchar(240) CHARACTER SET utf8 NOT NULL," +
                        "  `time` date NOT NULL," +
                        "  PRIMARY KEY (`id`)" +
                        ") ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;");
                text += "关注日志表创建完成。";
            }else{
                text+="关注日志表已存在，无需安装。";
            }
            //检查Sy_functions表
            i = jdbcTemplate.queryForObject("select count(*) from information_schema.columns where table_name = 'Sy_functions';", Integer.class);
            if (i == 0) {
                jdbcTemplate.execute("CREATE TABLE `Sy_functions` (" +
                        "  `id` int(11) NOT NULL AUTO_INCREMENT," +
                        "  `Vipdiscount` varchar(255) DEFAULT NULL," +
                        "  `Vipprivilege` text," +
                        "  `Vippackage` int(11) DEFAULT NULL," +
                        "  `Vippackagetitle` varchar(255) DEFAULT NULL," +
                        "  `Vippackagetext` text," +
                        "  `Task1` int(11) DEFAULT NULL," +
                        "  `Taskexp1` int(11) DEFAULT NULL," +
                        "  `Taskasset1` int(11) DEFAULT NULL," +
                        "  `Task2` int(11) DEFAULT NULL," +
                        "  `Taskexp2` int(11) DEFAULT NULL," +
                        "  `Taskasset2` int(11) DEFAULT NULL," +
                        "  `Tasklimit2` int(11) DEFAULT NULL," +
                        "  `Task3` int(11) DEFAULT NULL," +
                        "  `Taskexp3` int(11) DEFAULT NULL," +
                        "  `Taskasset3` int(11) DEFAULT NULL," +
                        "  `Tasklimit3` int(11) DEFAULT NULL," +
                        "  `Task4` int(11) DEFAULT NULL," +
                        "  `Taskexp4` int(11) DEFAULT NULL," +
                        "  `Taskasset4` int(11) DEFAULT NULL," +
                        "  `Tasklimit4` int(11) DEFAULT NULL," +
                        "  `Task5` int(11) DEFAULT NULL," +
                        "  `Taskexp5` int(11) DEFAULT NULL," +
                        "  `Taskasset5` int(11) DEFAULT NULL," +
                        "  `Tasklimit5` int(11) DEFAULT NULL," +
                        "  `Task6` int(11) DEFAULT NULL," +
                        "  `Taskexp6` int(11) DEFAULT NULL," +
                        "  `Taskasset6` int(11) DEFAULT NULL," +
                        "  `Tasklimit6` int(11) DEFAULT NULL," +
                        "  `Taskasset7` int(11) DEFAULT NULL," +
                        "  `Taskexp7` int(11) DEFAULT NULL," +
                        "  `Taskasset8` int(11) DEFAULT NULL," +
                        "  `Taskexp8` int(11) DEFAULT NULL," +
                        "  `Tasklimit9` int(11) DEFAULT NULL," +
                        "  `Taskexp9` int(11) DEFAULT NULL," +
                        "  `Taskasset9` int(11) DEFAULT NULL," +
                        "  `Signinexp1` int(11) DEFAULT NULL," +
                        "  `Signinasset1` int(11) DEFAULT NULL," +
                        "  `Signinexp2` int(11) DEFAULT NULL," +
                        "  `Signinasset2` int(11) DEFAULT NULL," +
                        "  `Signinexp3` int(11) DEFAULT NULL," +
                        "  `Signinasset3` int(11) DEFAULT NULL," +
                        "  `Signinexp4` int(11) DEFAULT NULL," +
                        "  `Signinasset4` int(11) DEFAULT NULL," +
                        "  `Signinexp5` int(11) DEFAULT NULL," +
                        "  `Signinasset5` int(11) DEFAULT NULL," +
                        "  `Signinexp6` int(11) DEFAULT NULL," +
                        "  `Signinasset6` int(11) DEFAULT NULL," +
                        "  `Signinexp7` int(11) DEFAULT NULL," +
                        "  `Signinasset7` int(11) DEFAULT NULL," +
                        "  `Task9` int(11) DEFAULT NULL," +
                        "  PRIMARY KEY (`id`)" +
                        ") ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;");

                text += "配置表创建完成。";
            }else{
                text+="配置表已存在，无需安装。";
            }
            //查询配置表是否有数据
            i = jdbcTemplate.queryForObject("SELECT COUNT(*) FROM Sy_functions;", Integer.class);
            if (i == 0){
                jdbcTemplate.execute("INSERT INTO `Sy_functions` (`id`, `Vipdiscount`, `Vipprivilege`, `Vippackage`, `Vippackagetitle`, `Vippackagetext`, `Task1`, `Taskexp1`, `Taskasset1`, `Task2`, `Taskexp2`, `Taskasset2`, `Tasklimit2`, `Task3`, `Taskexp3`, `Taskasset3`, `Tasklimit3`, `Task4`, `Taskexp4`, `Taskasset4`, `Tasklimit4`, `Task5`, `Taskexp5`, `Taskasset5`, `Tasklimit5`, `Task6`, `Taskexp6`, `Taskasset6`, `Tasklimit6`, `Signinexp1`, `Signinasset1`, `Signinexp2`, `Signinasset2`, `Signinexp3`, `Signinasset3`, `Signinexp4`, `Signinasset4`, `Signinexp5`, `Signinasset5`, `Signinexp6`, `Signinasset6`, `Signinexp7`, `Signinasset7`, `Taskasset7`, `Taskexp7`, `Taskasset8`, `Taskexp8`, `Tasklimit9`, `Taskexp9`, `Taskasset9`, `Task9`) VALUES" +
                        "(1, '8', '整站商品X折购买<br>\\r\\n获得积分提现资格<br>\\r\\n可查看VIP专享圈子<br>\\r\\n可查看VIP内容<br>\\r\\n昵称尊贵红色外显<br>\\r\\nVIP尊贵身份标识', 0, '永久VIP 88￥', '庆祝XX社区成立<br>\\r\\n享受永久VIP特权 限量10份', 1, 20, 20, 1, 10, 5, 3, 1, 5, 3, 3, 1, 5, 3, 3, 1, 3, 1, 5, 1, 2, 1, 5, 4, 2, 6, 3, 8, 4, 10, 6, 12, 8, 15, 10, 20, 15, 10, 20, 10, 20, 3, 2, 2, 1);");
                text+="配置表数据添加完成。";
            }else{
                text+="配置表数据已经存在，无需添加。";
            }
           
            //检查Sy_ip表
            i = jdbcTemplate.queryForObject("select count(*) from information_schema.columns where table_name = 'Sy_ip';", Integer.class);
            if (i == 0) {
                jdbcTemplate.execute("CREATE TABLE `Sy_ip` (" +
                        "  `id` int(11) NOT NULL AUTO_INCREMENT," +
                        "  `ipAdd` varchar(100) NOT NULL COMMENT '\r\nip归属地'," +
                        "  `Time` varchar(200) NOT NULL COMMENT '访问时间'," +
                        "  `State` text NOT NULL COMMENT 'ip'," +
                        "  PRIMARY KEY (`id`)" +
                        ") ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;");
                text += "ip日志表创建完成。";
            }else{
                text+="ip日志表已存在，无需安装。";
            }
            //检查Sy_IPerror表
            i = jdbcTemplate.queryForObject("select count(*) from information_schema.columns where table_name = 'Sy_IPerror';", Integer.class);
            if (i == 0) {
                jdbcTemplate.execute("CREATE TABLE `Sy_IPerror` (" +
                        "  `id` int(11) NOT NULL AUTO_INCREMENT," +
                        "  `ipAdd` varchar(100) NOT NULL COMMENT 'ip归属地'," +
                        "  `Time` varchar(200) NOT NULL COMMENT '时间'," +
                        "  `State` text NOT NULL COMMENT '拉黑ip'," +
                        "  `text` varchar(100) NOT NULL COMMENT '备注'," +
                        "  PRIMARY KEY (`id`)" +
                        ") ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;");
                text += "封ip日志表创建完成。";
            }else{
                text+="封ip日志表已存在，无需安装。";
            }
            //检查Sy_login表
            i = jdbcTemplate.queryForObject("select count(*) from information_schema.columns where table_name = 'Sy_login';", Integer.class);
            if (i == 0) {
                jdbcTemplate.execute("CREATE TABLE `Sy_login` (" +
                        "  `id` int(11) NOT NULL AUTO_INCREMENT," +
                        "  `user` varchar(100) NOT NULL COMMENT '登录用户名'," +
                        "  `pw` char(32) NOT NULL COMMENT '登录密码'," +
                        "  PRIMARY KEY (`id`)" +
                        ") ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;");
                text += "后台用户表创建完成。";
            }else{
                text+="后台用户表已存在，无需安装。";
            }
            //查询用户表是否有数据
            i = jdbcTemplate.queryForObject("SELECT COUNT(*) FROM Sy_login;", Integer.class);
            if (i == 0){
                jdbcTemplate.execute("INSERT INTO `Sy_login` (`id`, `user`, `pw`) VALUES" +
                        "(1, 'admin', 'e10adc3949ba59abbe56e057f20f883e');");
                text+="后台用户数据添加完成。";
            }else{
                text+="后台数据已经存在，无需添加。";
            }
            //检查pages配置模块
            i = jdbcTemplate.queryForObject("select count(*) from information_schema.columns where table_name = 'Sy_pages';", Integer.class);
            if (i == 0) {
                jdbcTemplate.execute("CREATE TABLE `Sy_pages` (" +
                        "  `id` int(11) NOT NULL AUTO_INCREMENT," +
                        "  `Announcement` text," +
                        "  `Displaytime` int(64) DEFAULT NULL," +
                        "  `Searchtext` varchar(255) DEFAULT NULL," +
                        "  `Carouselswitch` int(11) DEFAULT NULL," +
                        "  `Iconswitch` int(11) DEFAULT NULL," +
                        "  `Noticeswitch` int(11) DEFAULT NULL," +
                        "  `Notice` text," +
                        "  `Postswitch` int(11) DEFAULT NULL," +
                        "  `Bannerswitch` int(11) DEFAULT NULL," +
                        "  `Bannernumber` int(11) DEFAULT NULL," +
                        "  `Bannerimg1` varchar(255) DEFAULT NULL," +
                        "  `Bannerurl1` varchar(255) DEFAULT NULL," +
                        "  `Bannerimg2` varchar(255) DEFAULT NULL," +
                        "  `Bannerurl2` varchar(255) DEFAULT NULL," +
                        "  `Bannerimg3` varchar(255) DEFAULT NULL," +
                        "  `Bannerurl3` varchar(255) DEFAULT NULL," +
                        "  `Bannerimg4` varchar(255) DEFAULT NULL," +
                        "  `Bannerurl4` varchar(255) DEFAULT NULL," +
                        "  `Bannerimg5` varchar(255) DEFAULT NULL," +
                        "  `Bannerurl5` varchar(255) DEFAULT NULL," +
                        "  `Bannerimg6` varchar(255) DEFAULT NULL," +
                        "  `Bannerurl6` varchar(255) DEFAULT NULL," +
                        "  `Admin` varchar(255) DEFAULT NULL," +
                        "  `Gallery` int(11) DEFAULT NULL," +
                        "  `Code` int(11) DEFAULT NULL," +
                        "  `Hyperlinks` int(11) DEFAULT NULL," +
                        "  `Comments` int(11) DEFAULT NULL," +
                        "  `Image` int(11) DEFAULT NULL," +
                        "  `Video` int(11) DEFAULT NULL," +
                        "  `Topic` int(11) DEFAULT NULL," +
                        "  `Shop` int(11) DEFAULT NULL," +
                        "  `Viptext` int(11) DEFAULT NULL," +
                        "  `Music` int(11) DEFAULT NULL," +
                        "  `Musicimg1` varchar(255) DEFAULT NULL," +
                        "  `Musicimg2` varchar(255) DEFAULT NULL," +
                        "  `Musicimg3` varchar(255) DEFAULT NULL," +
                        "  `Usernumber` int(11) DEFAULT NULL," +
                        "  `Circlestyle` int(11) DEFAULT NULL," +
                        "  `Dynamicimg` varchar(255) DEFAULT NULL," +
                        "  `homeMode` INT DEFAULT 1," +
                        "  `topStyle` INT DEFAULT 2," +
                        "  `actStyle` INT DEFAULT 3," +
                        "  `swiperStyle` VARCHAR(255) DEFAULT 'true'," +
                        "  `swiperOf` INT DEFAULT 1," +
                        "  `iconOf` INT DEFAULT 1," +
                        "  `kuaijie` INT DEFAULT 0," +
                        "  `radiusBoxStyle` INT DEFAULT 2," +
                        "  `swiperStyle2` VARCHAR(255) DEFAULT 'true'," +
                        "  `radiusStyle` INT DEFAULT 2," +
                        "  `fatherTitle` INT DEFAULT 1," +
                        "  `swiperType` INT DEFAULT 2," +
                        "  `noticeOf` INT DEFAULT 1," +
                        "  `circleOf` INT DEFAULT 1," +
                        "  `recommendOf` INT DEFAULT 1," +
                        "  PRIMARY KEY (`id`)" +
                        ") ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;");
                text += "pages模块创建完成。";
            }else{
                text+="pages模块已存在，无需安装。";
            }
            //查询pages模块是否有数据
            i = jdbcTemplate.queryForObject("SELECT COUNT(*) FROM Sy_pages;", Integer.class);
            if (i == 0){
                jdbcTemplate.execute("INSERT INTO `Sy_pages` (`id`, `Announcement`, `Displaytime`, `Searchtext`, `Carouselswitch`, `Iconswitch`, `Noticeswitch`, `Notice`, `Postswitch`, `Bannerswitch`, `Bannernumber`, `Bannerimg1`, `Bannerurl1`, `Bannerimg2`, `Bannerurl2`, `Bannerimg3`, `Bannerurl3`, `Bannerimg4`, `Bannerurl4`, `Bannerimg5`, `Bannerurl5`, `Bannerimg6`, `Bannerurl6`, `Admin`, `Gallery`, `Code`, `Hyperlinks`, `Comments`, `Image`, `Video`, `Topic`, `Shop`, `Viptext`, `Music`, `Musicimg1`, `Musicimg2`, `Musicimg3`, `Usernumber`, `Circlestyle`, `Dynamicimg`) VALUES" +
                        "(1, '公告内容...', 86400000, 'StarPro|森云|为什么|这么帅', 1, 1, 1, '滚动通知内容...', 1, 1, 6, 'https://starimg.starpro.site/upload/2024/1/26/d48af2f1-5933-46a8-89d9-714e9322aa43.png', 'https://www.yuque.com/senyun-ev0j3/starpro/', 'https://starimg.starpro.site/upload/2024/1/26/d48af2f1-5933-46a8-89d9-714e9322aa43.png', 'https://www.yuque.com/senyun-ev0j3/starpro/', 'https://starimg.starpro.site/upload/2024/1/26/d48af2f1-5933-46a8-89d9-714e9322aa43.png', 'https://www.yuque.com/senyun-ev0j3/starpro/', 'https://starimg.starpro.site/upload/2024/1/26/d48af2f1-5933-46a8-89d9-714e9322aa43.png', 'https://www.yuque.com/senyun-ev0j3/starpro/', 'https://starimg.starpro.site/upload/2024/1/26/d48af2f1-5933-46a8-89d9-714e9322aa43.png', 'https://www.yuque.com/senyun-ev0j3/starpro/', 'https://starimg.starpro.site/upload/2024/1/26/d48af2f1-5933-46a8-89d9-714e9322aa43.png', 'https://www.yuque.com/senyun-ev0j3/starpro/', '0', 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 'https://starimg.starpro.site/upload/2024/1/26/d48af2f1-5933-46a8-89d9-714e9322aa43.png', 'https://starimg.starpro.site/upload/2024/1/26/d48af2f1-5933-46a8-89d9-714e9322aa43.png', 'https://starimg.starpro.site/upload/2024/1/26/d48af2f1-5933-46a8-89d9-714e9322aa43.png', 1, 1, 'https://starimg.starpro.site/upload/2024/6/20/75837a2a-4bc0-4247-9667-765b834ec0c6.png');");
                text+="pages模块数据添加完成。";
            }else{
                text+="pages模块数据已经存在，无需添加。";
            }
             //检查pages表是否有homeMode字段
            i = jdbcTemplate.queryForObject("select count(*) from information_schema.columns where table_name = 'Sy_pages' and column_name = 'homeMode';", Integer.class);
            if (i == 0){
                jdbcTemplate.execute("alter table Sy_pages ADD `homeMode` int DEFAULT 1 COMMENT '首页模式'");
                text+="页面配置模块，字段homeMode添加完成。";
            }else{
                text+="页面配置模块，字段homeMode已经存在，无需添加。";
            }
            //检查pages表是否有homeMode字段
            i = jdbcTemplate.queryForObject("select count(*) from information_schema.columns where table_name = 'Sy_pages' and column_name = 'Findtop';", Integer.class);
            if (i == 0){
                jdbcTemplate.execute("alter table Sy_pages ADD `Findtop` text COMMENT '发现页顶部内容'");
                text+="页面配置模块，字段Findtop添加完成。";
            }else{
                text+="页面配置模块，字段Findtop已经存在，无需添加。";
            }
            //检查icon配置模块
            i = jdbcTemplate.queryForObject("select count(*) from information_schema.columns where table_name = 'Sy_icon';", Integer.class);
            if (i == 0) {
                jdbcTemplate.execute("CREATE TABLE `Sy_icon` (" +
                        "  `id` int(11) NOT NULL AUTO_INCREMENT," +
                        "  `url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL," +
                        "  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL," +
                        "  `lgof` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL," +
                        "  `link` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL," +
                        "  PRIMARY KEY (`id`)" +
                        ") ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;");
                text += "icon模块创建完成。";
            }else{
                text+="icon模块已存在，无需安装。";
            }
            //查询pages模块是否有数据
            i = jdbcTemplate.queryForObject("SELECT COUNT(*) FROM Sy_icon;", Integer.class);
            if (i == 0){
                jdbcTemplate.execute("INSERT INTO `Sy_icon` (`url`, `name`, `lgof`, `link`) VALUES " +
                        "('https://', '应用', 'false', '/pages/plugins/sy_appbox/home')," +
                        "('https://', '商城', 'false', '/pages/shop/shop')," +
                        "('https://', '签到', 'true', '/pages/user/signin')," +
                        "('https://', '任务', 'true', '/pages/user/task')," +
                        "('https://', '认证', 'true', '/pages/user/identifyblue')," +
                        "('https://', '圈子', 'false', '/pages/forum/section')," +
                        "('https://', '文章', 'false', '/pages/contents/contentlist?title=全部文章&type=top&id=commentsNum')," +
                        "('https://', 'AI对话', 'true', '/pages/plugins/sy_gpt/home')," +
                        "('https://', '会员', 'true', '/pages/user/buyvip')," +
                        "('https://', '客服', 'false', '/pages/user/media')");
                text+="icon模块数据添加完成。";
            }else{
                text+="icon模块数据已经存在，无需添加。";
            }
            //检查Sy_popups表
            i = jdbcTemplate.queryForObject("select count(*) from information_schema.columns where table_name = 'Sy_popups';", Integer.class);
            if (i == 0) {
                jdbcTemplate.execute("CREATE TABLE `Sy_popups` (" +
                        "  `id` int(11) NOT NULL AUTO_INCREMENT," +
                        "  `Postpopup` text," +
                        "  `Shoppopup` text," +
                        "  `Taskpopup` text," +
                        "  `Signpopup` text," +
                        "  `Alipaypopup` text," +
                        "  `Wechatpopup` text," +
                        "  `Camipopup` text," +
                        "  `Yipaypopup` text," +
                        "  `Loginpopup` text," +
                        "  `Registpopup` text," +
                        "  `Forgetpopup` text," +
                        "  `lvtext` text," +
                        "  `smtext` text," +
                        "  `yqtext` text," +
                        "  PRIMARY KEY (`id`)" +
                        ") ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;");
                text += "popups模块创建完成。";
            }else{
                text+="popups模块已存在，无需安装。";
            }
            //查询用户表是否有数据
            i = jdbcTemplate.queryForObject("SELECT COUNT(*) FROM Sy_popups;", Integer.class);
            if (i == 0){
                jdbcTemplate.execute("INSERT INTO `Sy_popups` (`id`, `Postpopup`, `Shoppopup`, `Taskpopup`, `Signpopup`, `Alipaypopup`, `Wechatpopup`, `Camipopup`, `Yipaypopup`, `Loginpopup`, `Registpopup`, `Forgetpopup`, `lvtext`, `smtext`, `yqtext`) VALUES" +
                        "(1, '发帖规范弹窗...', '发布商品规范弹窗...', '任务页说明...', '签到页说明...', '1.完成支付前请勿关闭当前窗口。<br>2.如果支付宝唤醒失败，请手动保存二维码前往支付宝扫码支付。<br>3.支付过程中请不要关闭APP后台，保持APP在后台运行。<br>4.若支付后未到账，请前往钱包获取订单号，联系客服。', '1.完成支付前请勿关闭当前窗口。<br>2.微信支付需要使用另外一个设备进行扫码支付<br>3.因官方限制，微信支付无法自动转跳支付<br>4.若支付后未到账，请前往钱包获取订单号，联系客服。', '卡密充值时的注意事项...（后台支持自定义）', '1.使用扫一扫支付相应金额，完成支付前请勿关闭当前窗口。<br>2.如无法跳转，可点击复制链接后，前往浏览器打开。<br>3.若支付后未到账，请前往钱包获取订单号，联系客服。', '', '', '', '蓝V申请要求...（后台支持自定义）', '1.平台身份认证为响应国家号召而提供的功能模块，完成认证后将获得相关权利和身份标识。<br />2.目前平台采用官方认证渠道，而非人工审核。所以请正确填写您的身份信息，错误的信息将导致审核不通过。<br />3.多次提交错误信息或审核不通过的用户将可能封号处理，所以请谨慎对待身份认证。<br />4.对于使用他人身份信息认证的，一经发现将撤销认证认证，并对账号进行封禁处理。', '1.可通过保存邀请码，并让被邀请者使用本平台应用进行扫码，即可开始邀请注册。<br />2.被邀请者注册后，可获得固定奖励，或消费分成奖励，以实际信息为准。');");
                text+="popups模块数据添加完成。";
            }else{
                text+="popups模块数据已经存在，无需添加。";
            }
            //检查Sy_set表
            i = jdbcTemplate.queryForObject("select count(*) from information_schema.columns where table_name = 'Sy_set';", Integer.class);
            if (i == 0) {
                jdbcTemplate.execute("CREATE TABLE `Sy_set` (" +
                        "  `id` int(11) NOT NULL AUTO_INCREMENT," +
                        "  `Waiterurl` varchar(255) NOT NULL COMMENT '客服链接'," +
                        "  `Groupurl` varchar(255) NOT NULL COMMENT '群聊链接'," +
                        "  `Auditurl` varchar(255) NOT NULL COMMENT '审核员链接'," +
                        "  `Share` tinyint(1) NOT NULL DEFAULT '0' COMMENT '分享开关 1开 0关'," +
                        "  `Tipping` tinyint(1) NOT NULL DEFAULT '0' COMMENT '打赏开关 1开 0关'," +
                        "  `Payswith` tinyint(1) NOT NULL DEFAULT '0' COMMENT '充值开关 1开 0关'," +
                        "  `Alipay` tinyint(1) NOT NULL DEFAULT '0' COMMENT '支付宝支付 1开 0关'," +
                        "  `WePay` tinyint(1) NOT NULL DEFAULT '0' COMMENT '微信支付 1开 0关'," +
                        "  `Cami` tinyint(1) NOT NULL DEFAULT '0' COMMENT '卡密兑换 1开 0关'," +
                        "  `Yipay` tinyint(1) NOT NULL DEFAULT '0' COMMENT '易支付 1开 0关'," +
                        "  `Qlogin` tinyint(1) NOT NULL DEFAULT '0' COMMENT 'QQ登录 1开 0关'," +
                        "  `wxlogin` tinyint(1) NOT NULL DEFAULT '0' COMMENT '微信登录 1开 0关'," +
                        "  `wblogin` tinyint(1) NOT NULL DEFAULT '0' COMMENT '微博登录 1开 0关'," +
                        "  `Tippingstyle` varchar(20) DEFAULT '2' COMMENT '打赏样式'," +
                        "  `Assetname` varchar(255) DEFAULT '积分' COMMENT '货币名称'," +
                        "  `Withdrawals` varchar(20) DEFAULT '0' COMMENT '提现权限'," +
                        "  `Premium` varchar(255) DEFAULT '0' COMMENT '提现手续费'," +
                        "  `Qgroup` varchar(20) DEFAULT 'contributor' COMMENT 'QQ用户组'," +
                        "  `Threshold` varchar(255) DEFAULT '0' COMMENT '提现门槛'," +
                        "  `Dummy` varchar(255) NOT NULL DEFAULT '0' COMMENT '用户冲假数量'," +
                        "  `Viewspw` varchar(255) NOT NULL DEFAULT '123456' COMMENT '刷浏览量密码'," +
                        "  `h5of` int(11) NOT NULL DEFAULT '0'," +
                        "  PRIMARY KEY (`id`)" +
                        ") ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='系统设置表';");
                text += "系统表创建完成。";
            }else{
                text+="系统表已存在，无需安装。";
            }
            //查询用户表是否有数据
            i = jdbcTemplate.queryForObject("SELECT COUNT(*) FROM Sy_set;", Integer.class);
            if (i == 0){
                jdbcTemplate.execute("INSERT INTO `Sy_set` (`id`, `Waiterurl`, `Groupurl`, `Auditurl`, `Share`, `Tipping`, `Payswith`, `Alipay`, `WePay`, `Cami`, `Yipay`, `Qlogin`, `Tippingstyle`, `Assetname`, `Withdrawals`, `Premium`, `Qgroup`, `Threshold`, `Dummy`, `Viewspw`, `h5of`) VALUES" +
                        "(1, 'https://', 'https://', 'https://', 0, 1, 1, 1, 0, 1, 0, 0, '1', '积分', '1', '10', 'contributor', '1000', '0', '12345', 0);");
                text+="系统表数据添加完成。";
            }else{
                text+="系统表数据已经存在，无需添加。";
            }
            //检查Sy_Signinlog表
            i = jdbcTemplate.queryForObject("select count(*) from information_schema.columns where table_name = 'Sy_Signinlog';", Integer.class);
            if (i == 0) {
                jdbcTemplate.execute("CREATE TABLE `Sy_Signinlog` (" +
                        "  `id` int(11) NOT NULL AUTO_INCREMENT," +
                        "  `uid` varchar(255) CHARACTER SET utf8mb4 NOT NULL," +
                        "  `time` datetime NOT NULL," +
                        "  `continuous` int(5) NOT NULL COMMENT '连续签到'," +
                        "  `assets` int(5) NOT NULL COMMENT '积分'," +
                        "  `exp` int(5) NOT NULL COMMENT '经验'," +
                        "  PRIMARY KEY (`id`)" +
                        ") ENGINE=InnoDB DEFAULT CHARSET=utf8;");
                text += "签到日志表创建完成。";
            }else{
                text+="签到日志表已存在，无需安装。";
            }
            //检查Sy_Tippinglog表
            i = jdbcTemplate.queryForObject("select count(*) from information_schema.columns where table_name = 'Sy_Tippinglog';", Integer.class);
            if (i == 0) {
                jdbcTemplate.execute("CREATE TABLE `Sy_Tippinglog` (" +
                        "  `id` int(11) NOT NULL AUTO_INCREMENT," +
                        "  `uid` varchar(240) NOT NULL," +
                        "  `time` date NOT NULL," +
                        "  PRIMARY KEY (`id`)" +
                        ") ENGINE=InnoDB DEFAULT CHARSET=utf8;");
                text += "打赏日志表创建完成。";
            }else{
                text+="打赏日志表已存在，无需安装。";
            }
            //检查Sy_warning表
            i = jdbcTemplate.queryForObject("select count(*) from information_schema.columns where table_name = 'Sy_warning';", Integer.class);
            if (i == 0) {
                jdbcTemplate.execute("CREATE TABLE `Sy_warning` (" +
                        "  `id` int(11) NOT NULL AUTO_INCREMENT," +
                        "  `ip` varchar(50) NOT NULL COMMENT 'ip地址'," +
                        "  `gsd` varchar(50) NOT NULL COMMENT '归属地'," +
                        "  `time` varchar(80) NOT NULL COMMENT '时间'," +
                        "  `file` varchar(100) NOT NULL COMMENT '路径'," +
                        "  PRIMARY KEY (`id`)" +
                        ") ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;");
                text += "登录日志表创建完成。";
            }else{
                text+="登录日志表已存在，无需安装。";
            }
            //查询用户表是否存在qqbang字段
            i = jdbcTemplate.queryForObject("select count(*) from information_schema.columns where table_name = '"+prefix+"_users' and column_name = 'qqbang';", Integer.class);
            if (i == 0){
                jdbcTemplate.execute("ALTER TABLE `typecho_users` ADD `qqbang` INT(11) NOT NULL DEFAULT '0';");
                text+="用户表，qq绑定字段添加完成。";
            }else{
                text+="用户表，qq绑定字段已经存在，无需添加。";
            }
            //查询用户表是否存在wxbang字段
            i = jdbcTemplate.queryForObject("select count(*) from information_schema.columns where table_name = '"+prefix+"_users' and column_name = 'wxbang';", Integer.class);
            if (i == 0){
                jdbcTemplate.execute("ALTER TABLE `typecho_users` ADD `wxbang` INT(11) NOT NULL DEFAULT '0';");
                text+="用户表，wx绑定字段添加完成。";
            }else{
                text+="用户表，wx绑定字段已经存在，无需添加。";
            }
            //查询用户表是否存在wbbang字段
            i = jdbcTemplate.queryForObject("select count(*) from information_schema.columns where table_name = '"+prefix+"_users' and column_name = 'wbbang';", Integer.class);
            if (i == 0){
                jdbcTemplate.execute("ALTER TABLE `typecho_users` ADD `wbbang` INT(11) NOT NULL DEFAULT '0';");
                text+="用户表，wb绑定字段添加完成。";
            }else{
                text+="用户表，wb绑定字段已经存在，无需添加。";
            }
            //查询用户表是否存在实名字段
            i = jdbcTemplate.queryForObject("select count(*) from information_schema.columns where table_name = '"+prefix+"_users' and column_name = 'consumer';", Integer.class);
            if (i == 0){
                jdbcTemplate.execute("ALTER TABLE `typecho_users` ADD `consumer` INT(11) NOT NULL DEFAULT '0';");
                text+="用户表，实名字段添加完成。";
            }else{
                text+="用户表，实名字段已经存在，无需添加。";
            }
            //查询用户表是否存在蓝V字段
            i = jdbcTemplate.queryForObject("select count(*) from information_schema.columns where table_name = '"+prefix+"_users' and column_name = 'company';", Integer.class);
            if (i == 0){
                jdbcTemplate.execute("ALTER TABLE `typecho_users` ADD `company` INT(11) NOT NULL DEFAULT '0';");
                text+="用户表，蓝V字段添加完成。";
            }else{
                text+="用户表，蓝V字段已经存在，无需添加。";
            }

            //查询Sy_set是否存在Lvof字段
            i = jdbcTemplate.queryForObject("select count(*) from information_schema.columns where table_name = 'Sy_set' and column_name = 'Lvof';", Integer.class);
            if (i == 0){
                jdbcTemplate.execute("ALTER TABLE `Sy_set` ADD `Lvof` INT(11) NOT NULL DEFAULT '0';");
                text+="pages表，Lvof字段添加完成。";
            }else{
                text+="pages表，Lvof字段已经存在，无需添加。";
            }
            //查询Sy_popups是否存在lvtext字段
            i = jdbcTemplate.queryForObject("select count(*) from information_schema.columns where table_name = 'Sy_popups' and column_name = 'lvtext';", Integer.class);
            if (i == 0){
                jdbcTemplate.execute("ALTER TABLE `Sy_popups` ADD `lvtext` TEXT NOT NULL DEFAULT '蓝V申请要求...';");
                text+="popups表，蓝V申请字段添加完成。";
            }else{
                text+="popups表，蓝V申请字段已经存在，无需添加。";
            }
            //查询Sy_popups是否存在smtext字段
            i = jdbcTemplate.queryForObject("select count(*) from information_schema.columns where table_name = 'Sy_popups' and column_name = 'smtext';", Integer.class);
            if (i == 0){
                jdbcTemplate.execute("ALTER TABLE `Sy_popups` ADD `smtext` TEXT NOT NULL DEFAULT '1.平台身份认证为响应国家号召而提供的功能模块，完成认证后将获得相关权利和身份标识。<br />2.目前平台采用官方认证渠道，而非人工审核。所以请正确填写您的身份信息，错误的信息将导致审核不通过。<br />3.多次提交错误信息或审核不通过的用户将可能封号处理，所以请谨慎对待身份认证。<br />4.对于使用他人身份信息认证的，一经发现将撤销认证认证，并对账号进行封禁处理。';");
                text+="popups表，实名字段添加完成。";
            }else{
                text+="popups表，实名字段已经存在，无需添加。";
            }
            //查询Sy_popups是否存在lvtext字段
            i = jdbcTemplate.queryForObject("select count(*) from information_schema.columns where table_name = 'Sy_popups' and column_name = 'yqtext';", Integer.class);
            if (i == 0){
                jdbcTemplate.execute("ALTER TABLE `Sy_popups` ADD `yqtext` TEXT NOT NULL DEFAULT '1.可通过保存邀请码，并让被邀请者使用本平台应用进行扫码，即可开始邀请注册。<br />2.被邀请者注册后，可获得固定奖励，或消费分成奖励，以实际信息为准。';");
                text+="popups表，邀请好友字段添加完成。";
            }else{
                text+="popups表，邀请好友字段已经存在，无需添加。";
            }
            //查询Sy_pages所有更新字段
            i = jdbcTemplate.queryForObject("select count(*) from information_schema.columns where table_name = 'Sy_pages' and column_name = 'kuaijie';", Integer.class);
            if (i == 0){
                jdbcTemplate.execute("ALTER TABLE `Sy_pages` ADD `kuaijie` INT DEFAULT 0;");
                text+="pages表，kuaijie字段添加完成。";
            }else{
                text+="pages表，kuaijie字段已经存在，无需添加。";
            }
            i = jdbcTemplate.queryForObject("select count(*) from information_schema.columns where table_name = 'Sy_pages' and column_name = 'radiusBoxStyle';", Integer.class);
            if (i == 0){
                jdbcTemplate.execute("ALTER TABLE `Sy_pages` ADD `radiusBoxStyle` INT DEFAULT 2;");
                text+="pages表，radiusBoxStyle字段添加完成。";
            }else{
                text+="pages表，radiusBoxStyle字段已经存在，无需添加。";
            }
            i = jdbcTemplate.queryForObject("select count(*) from information_schema.columns where table_name = 'Sy_pages' and column_name = 'radiusStyle';", Integer.class);
            if (i == 0){
                jdbcTemplate.execute("ALTER TABLE `Sy_pages` ADD `radiusStyle` INT DEFAULT 2;");
                text+="pages表，radiusStyle字段添加完成。";
            }else{
                text+="pages表，radiusStyle字段已经存在，无需添加。";
            }
            i = jdbcTemplate.queryForObject("select count(*) from information_schema.columns where table_name = 'Sy_pages' and column_name = 'fatherTitle';", Integer.class);
            if (i == 0){
                jdbcTemplate.execute("ALTER TABLE `Sy_pages` ADD `fatherTitle` INT DEFAULT 1;");
                text+="pages表，fatherTitle字段添加完成。";
            }else{
                text+="pages表，fatherTitle字段已经存在，无需添加。";
            }
            i = jdbcTemplate.queryForObject("select count(*) from information_schema.columns where table_name = 'Sy_pages' and column_name = 'swiperStyle2';", Integer.class);
            if (i == 0){
                jdbcTemplate.execute("ALTER TABLE `Sy_pages` ADD `swiperStyle2` VARCHAR(255) DEFAULT 'true';");
                text+="pages表，swiperStyle2字段添加完成。";
            }else{
                text+="pages表，swiperStyle2字段已经存在，无需添加。";
            }
            i = jdbcTemplate.queryForObject("select count(*) from information_schema.columns where table_name = 'Sy_pages' and column_name = 'swiperType';", Integer.class);
            if (i == 0){
                jdbcTemplate.execute("ALTER TABLE `Sy_pages` ADD `swiperType` INT DEFAULT 2;");
                text+="pages表，swiperType字段添加完成。";
            }else{
                text+="pages表，swiperType字段已经存在，无需添加。";
            }
            i = jdbcTemplate.queryForObject("select count(*) from information_schema.columns where table_name = 'Sy_pages' and column_name = 'recommendOf';", Integer.class);
            if (i == 0){
                jdbcTemplate.execute("ALTER TABLE `Sy_pages` ADD `recommendOf` INT DEFAULT 1;");
                text+="pages表，recommendOf字段添加完成。";
            }else{
                text+="pages表，recommendOf字段已经存在，无需添加。";
            }
            i = jdbcTemplate.queryForObject("select count(*) from information_schema.columns where table_name = 'Sy_pages' and column_name = 'homeMode';", Integer.class);
            if (i == 0){
                jdbcTemplate.execute("ALTER TABLE `Sy_pages` ADD `homeMode` INT DEFAULT 1;");
                text+="pages表，homeMode字段添加完成。";
            }else{
                text+="pages表，homeMode字段已经存在，无需添加。";
            }
            i = jdbcTemplate.queryForObject("select count(*) from information_schema.columns where table_name = 'Sy_pages' and column_name = 'topStyle';", Integer.class);
            if (i == 0){
                jdbcTemplate.execute("ALTER TABLE `Sy_pages` ADD `topStyle` INT DEFAULT 2;");
                text+="pages表，topStyle字段添加完成。";
            }else{
                text+="pages表，topStyle字段已经存在，无需添加。";
            }
            i = jdbcTemplate.queryForObject("select count(*) from information_schema.columns where table_name = 'Sy_pages' and column_name = 'actStyle';", Integer.class);
            if (i == 0){
                jdbcTemplate.execute("ALTER TABLE `Sy_pages` ADD `actStyle` INT DEFAULT 3;");
                text+="pages表，actStyle字段添加完成。";
            }else{
                text+="pages表，actStyle字段已经存在，无需添加。";
            }
            i = jdbcTemplate.queryForObject("select count(*) from information_schema.columns where table_name = 'Sy_pages' and column_name = 'swiperStyle';", Integer.class);
            if (i == 0){
                jdbcTemplate.execute("ALTER TABLE `Sy_pages` ADD `swiperStyle` VARCHAR(255) DEFAULT 'true';");
                text+="pages表，swiperStyle字段添加完成。";
            }else{
                text+="pages表，swiperStyle字段已经存在，无需添加。";
            }
            i = jdbcTemplate.queryForObject("select count(*) from information_schema.columns where table_name = 'Sy_pages' and column_name = 'noticeOf';", Integer.class);
            if (i == 0){
                jdbcTemplate.execute("ALTER TABLE `Sy_pages` ADD `noticeOf` INT DEFAULT 1;");
                text+="pages表，noticeOf字段添加完成。";
            }else{
                text+="pages表，noticeOf字段已经存在，无需添加。";
            }
            i = jdbcTemplate.queryForObject("select count(*) from information_schema.columns where table_name = 'Sy_pages' and column_name = 'circleOf';", Integer.class);
            if (i == 0){
                jdbcTemplate.execute("ALTER TABLE `Sy_pages` ADD `circleOf` INT DEFAULT 1;");
                text+="pages表，circleOf字段添加完成。";
            }else{
                text+="pages表，circleOf字段已经存在，无需添加。";
            }
            i = jdbcTemplate.queryForObject("select count(*) from information_schema.columns where table_name = 'Sy_pages' and column_name = 'swiperOf';", Integer.class);
            if (i == 0){
                jdbcTemplate.execute("ALTER TABLE `Sy_pages` ADD `swiperOf` INT DEFAULT 1;");
                text+="pages表，swiperOf字段添加完成。";
            }else{
                text+="pages表，swiperOf字段已经存在，无需添加。";
            }
            i = jdbcTemplate.queryForObject("select count(*) from information_schema.columns where table_name = 'Sy_pages' and column_name = 'iconOf';", Integer.class);
            if (i == 0){
                jdbcTemplate.execute("ALTER TABLE `Sy_pages` ADD `iconOf` INT DEFAULT 1;");
                text+="pages表，iconOf字段添加完成。";
            }else{
                text+="pages表，iconOf字段已经存在，无需添加。";
            }
            //给旧版本的日志表加uid字段
            i = jdbcTemplate.queryForObject("select count(*) from information_schema.columns where table_name = 'Sy_Forumlog' and column_name = 'uid';", Integer.class);
            if (i == 0){
                jdbcTemplate.execute("ALTER TABLE `Sy_Forumlog` ADD `uid` varchar(240) CHARACTER SET utf8 NOT NULL;");
                text+="Forumlog表，uid字段添加完成。";
            }else{
                text+="Forumlog表，uid字段已经存在，无需添加。";
            }
            i = jdbcTemplate.queryForObject("select count(*) from information_schema.columns where table_name = 'Sy_Articlelog' and column_name = 'uid';", Integer.class);
            if (i == 0){
                jdbcTemplate.execute("ALTER TABLE `Sy_Articlelog` ADD `uid` varchar(240) CHARACTER SET utf8 NOT NULL;");
                text += "Articlelog表，uid字段添加完成。";
            } else {
                text += "Articlelog表，uid字段已经存在，无需添加。";
            }
            i = jdbcTemplate.queryForObject("select count(*) from information_schema.columns where table_name = 'Sy_Commentslog' and column_name = 'uid';", Integer.class);
            if (i == 0){
                jdbcTemplate.execute("ALTER TABLE `Sy_Commentslog` ADD `uid` varchar(240) CHARACTER SET utf8 NOT NULL;");
                text += "Commentslog表，uid字段添加完成。";
            } else {
                text += "Commentslog表，uid字段已经存在，无需添加。";
            }
            i = jdbcTemplate.queryForObject("select count(*) from information_schema.columns where table_name = 'Sy_Dynamiclog' and column_name = 'uid';", Integer.class);
            if (i == 0){
                jdbcTemplate.execute("ALTER TABLE `Sy_Dynamiclog` ADD `uid` varchar(240) CHARACTER SET utf8 NOT NULL;");
                text += "Dynamiclog表，uid字段添加完成。";
            } else {
                text += "Dynamiclog表，uid字段已经存在，无需添加。";
            }
            i = jdbcTemplate.queryForObject("select count(*) from information_schema.columns where table_name = 'Sy_Followlog' and column_name = 'uid';", Integer.class);
            if (i == 0){
                jdbcTemplate.execute("ALTER TABLE `Sy_Followlog` ADD `uid` varchar(240) CHARACTER SET utf8 NOT NULL;");
                text += "Followlog表，uid字段添加完成。";
            } else {
                text += "Followlog表，uid字段已经存在，无需添加。";
            }
            i = jdbcTemplate.queryForObject("select count(*) from information_schema.columns where table_name = 'Sy_Signinlog' and column_name = 'uid';", Integer.class);
            if (i == 0){
                jdbcTemplate.execute("ALTER TABLE `Sy_Signinlog` ADD `uid` varchar(240) CHARACTER SET utf8 NOT NULL;");
                text += "Signinlog表，uid字段添加完成。";
            } else {
                text += "Signinlog表，uid字段已经存在，无需添加。";
            }
            i = jdbcTemplate.queryForObject("select count(*) from information_schema.columns where table_name = 'Sy_Tippinglog' and column_name = 'uid';", Integer.class);
            if (i == 0){
                jdbcTemplate.execute("ALTER TABLE `Sy_Tippinglog` ADD `uid` varchar(240) CHARACTER SET utf8 NOT NULL;");
                text += "Tippinglog表，uid字段添加完成。";
            } else {
                text += "Tippinglog表，uid字段已经存在，无需添加。";
            }
            //给旧版本的set表更新字段
            i = jdbcTemplate.queryForObject("SELECT COUNT(*) FROM information_schema.columns WHERE table_name = 'Sy_set' AND column_name = 'wxlogin';", Integer.class);
            if (i == 0) {
                jdbcTemplate.execute("ALTER TABLE `Sy_set` ADD `wxlogin` tinyint(1) NOT NULL DEFAULT '0' COMMENT '微信登录 1开 0关';");
                text += "set表，wxlogin字段添加完成。";
            } else {
                text += "set表，wxlogin字段已经存在，无需添加。";
            }
            i = jdbcTemplate.queryForObject("SELECT COUNT(*) FROM information_schema.columns WHERE table_name = 'Sy_set' AND column_name = 'wblogin';", Integer.class);
            if (i == 0) {
                jdbcTemplate.execute("ALTER TABLE `Sy_set` ADD `wblogin` tinyint(1) NOT NULL DEFAULT '0' COMMENT '微博登录 1开 0关';");
                text += "set表，wblogin字段添加完成。";
            } else {
                text += "set表，wblogin字段已经存在，无需添加。";
            }
            //查询Sy_popups是否存在settext字段
            i = jdbcTemplate.queryForObject("select count(*) from information_schema.columns where table_name = 'Sy_popups' and column_name = 'settext';", Integer.class);
            if (i == 0){
                jdbcTemplate.execute("ALTER TABLE `Sy_popups` ADD COLUMN `settext` TEXT NULL;");
                text+="popups表，settext字段添加完成。";
            }else{
                int nullCount = jdbcTemplate.queryForObject("select count(*) from Sy_popups where settext IS NULL;", Integer.class);
                if (nullCount > 0) {
                    jdbcTemplate.execute("UPDATE Sy_popups SET settext = '<p></p>' WHERE settext IS NULL;");
                    text += " settext字段为NULL的记录已更新。";
                }
                text+="popups表，settext字段已经存在，无需添加。";
            }
            //查询Sy_popups是否存在hometext字段
            i = jdbcTemplate.queryForObject("select count(*) from information_schema.columns where table_name = 'Sy_popups' and column_name = 'hometext';", Integer.class);
            if (i == 0){
                jdbcTemplate.execute("ALTER TABLE `Sy_popups` ADD COLUMN `hometext` TEXT NULL;");
                text+="popups表，hometext字段添加完成。";
            }else{
                text+="popups表，hometext字段已经存在，无需添加。";
            }
            //查询Sy_popups是否存在home2text字段
            i = jdbcTemplate.queryForObject("select count(*) from information_schema.columns where table_name = 'Sy_popups' and column_name = 'home2text';", Integer.class);
            if (i == 0){
                jdbcTemplate.execute("ALTER TABLE `Sy_popups` ADD COLUMN `home2text` TEXT NULL;");
                text+="popups表，home2text字段添加完成。";
            }else{
                text+="popups表，home2text字段已经存在，无需添加。";
            }
            //查询Sy_popups是否存在homecsstext字段
            i = jdbcTemplate.queryForObject("select count(*) from information_schema.columns where table_name = 'Sy_popups' and column_name = 'homecsstext';", Integer.class);
            if (i == 0){
                jdbcTemplate.execute("ALTER TABLE `Sy_popups` ADD COLUMN `homecsstext` TEXT NULL;");
                text+="popups表，homecsstext字段添加完成。";
            }else{
                text+="popups表，homecsstext字段已经存在，无需添加。";
            }
            //检查set表wzof字段
            i = jdbcTemplate.queryForObject("SELECT COUNT(*) FROM information_schema.columns WHERE table_name = 'Sy_set' AND column_name = 'wzof';", Integer.class);
            if (i == 0) {
                jdbcTemplate.execute("ALTER TABLE `Sy_set` ADD `wzof` tinyint(1) NOT NULL DEFAULT '1';");
                text += "set表，wzof字段添加完成。";
            } else {
                text += "set表，wzof字段已经存在，无需添加。";
            }
            //检查set表tzof字段
            i = jdbcTemplate.queryForObject("SELECT COUNT(*) FROM information_schema.columns WHERE table_name = 'Sy_set' AND column_name = 'tzof';", Integer.class);
            if (i == 0) {
                jdbcTemplate.execute("ALTER TABLE `Sy_set` ADD `tzof` tinyint(1) NOT NULL DEFAULT '1';");
                text += "set表，tzof字段添加完成。";
            } else {
                text += "set表，tzof字段已经存在，无需添加。";
            }
            //查询Sy_set是否存在homeStyle字段
            i = jdbcTemplate.queryForObject("select count(*) from information_schema.columns where table_name = 'Sy_set' and column_name = 'homeStyle';", Integer.class);
            if (i == 0){
                jdbcTemplate.execute("ALTER TABLE `Sy_set` ADD `homeStyle` INT(11) NOT NULL DEFAULT '1';");
                text+="set表，homeStyle字段添加完成。";
            }else{
                // 检查并更新不为1的值
                int nonOneCount = jdbcTemplate.queryForObject("SELECT COUNT(*) FROM Sy_set WHERE homeStyle != '1';", Integer.class);
                if (nonOneCount > 0) {
                    jdbcTemplate.execute("UPDATE Sy_set SET homeStyle = '1' WHERE homeStyle != '1';");
                    text+="set表，homeStyle字段值已更新为1。";
                } else {
                    text+="set表，homeStyle字段已经存在，无需添加。";
                }
            }
            //查询Sy_set是否存在wzof字段
            i = jdbcTemplate.queryForObject("select count(*) from information_schema.columns where table_name = 'Sy_set' and column_name = 'wzof';", Integer.class);
            if (i == 0){
                jdbcTemplate.execute("ALTER TABLE `Sy_set` ADD `wzof` INT(11) NOT NULL DEFAULT '1';");
                text+="set表，wzof字段添加完成。";
            }else{
                text+="set表，wzof字段已经存在，无需添加。";
            }
            //查询Sy_set是否存在tzof字段
            i = jdbcTemplate.queryForObject("select count(*) from information_schema.columns where table_name = 'Sy_set' and column_name = 'tzof';", Integer.class);
            if (i == 0){
                jdbcTemplate.execute("ALTER TABLE `Sy_set` ADD `tzof` INT(11) NOT NULL DEFAULT '1';");
                text+="set表，tzof字段添加完成。";
            }else{
                text+="set表，tzof字段已经存在，无需添加。";
            }
            //查询Sy_set是否存在shopof字段
            i = jdbcTemplate.queryForObject("select count(*) from information_schema.columns where table_name = 'Sy_set' and column_name = 'shopof';", Integer.class);
            if (i == 0){
                jdbcTemplate.execute("ALTER TABLE `Sy_set` ADD `shopof` INT(11) NOT NULL DEFAULT '1';");
                text+="set表，shopof字段添加完成。";
            }else{
                text+="set表，shopof字段已经存在，无需添加。";
            }
            //查询Sy_set是否存在modOrder字段
            i = jdbcTemplate.queryForObject("select count(*) from information_schema.columns where table_name = 'Sy_set' and column_name = 'modOrder';", Integer.class);
            if (i == 0){
                jdbcTemplate.execute("ALTER TABLE `Sy_set` ADD `modOrder` INT(11) NOT NULL DEFAULT '1';");
                text+="set表，modOrder字段添加完成。";
            }else{
                text+="set表，modOrder字段已经存在，无需添加。";
            }
            //检查update配置模块
            i = jdbcTemplate.queryForObject("select count(*) from information_schema.columns where table_name = 'Sy_update';", Integer.class);
            if (i == 0) {
                jdbcTemplate.execute("CREATE TABLE `Sy_update` (" +
                        "  `id` int(11) NOT NULL AUTO_INCREMENT," +
                        "  `version` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL," +
                        "  `versionIntro` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL," +
                        "  `versionUrl` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL," +
                        "  `versionCode` int(11) DEFAULT NULL," +
                        "  `force` int(11) DEFAULT NULL," +
                        "  PRIMARY KEY (`id`)" +
                        ") ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;");
                text += "update模块创建完成。";
            }else{
                text+="update模块已存在，无需安装。";
            }

            i = jdbcTemplate.queryForObject("select count(*) from information_schema.columns where table_name = '"+prefix+"_apiconfig' and column_name = 'smsType';", Integer.class);
            if (i == 0){
                jdbcTemplate.execute("alter table "+prefix+"_apiconfig ADD `smsType` int(2) DEFAULT '0' COMMENT '短信发信类型'");
                text+="配置中心模块，字段smsType添加完成。";
            }else{
                text+="配置中心模块，字段smsType已经存在，无需添加。";
            }
            i = jdbcTemplate.queryForObject("select count(*) from information_schema.columns where table_name = '"+prefix+"_apiconfig' and column_name = 'smsbaoUsername';", Integer.class);
            if (i == 0){
                jdbcTemplate.execute("alter table "+prefix+"_apiconfig ADD `smsbaoUsername` text COMMENT '短信宝Username'");
                text+="配置中心模块，字段smsbaoUsername添加完成。";
            }else{
                text+="配置中心模块，字段smsbaoUsername已经存在，无需添加。";
            }

            i = jdbcTemplate.queryForObject("select count(*) from information_schema.columns where table_name = '"+prefix+"_apiconfig' and column_name = 'SmsbaoApikey';", Integer.class);
            if (i == 0){
                jdbcTemplate.execute("alter table "+prefix+"_apiconfig ADD `smsbaoApikey` text COMMENT '短信宝Apikey'");
                text+="配置中心模块，字段smsbaoApikey添加完成。";
            }else{
                text+="配置中心模块，字段smsbaoApikey已经存在，无需添加。";
            }

            i = jdbcTemplate.queryForObject("select count(*) from information_schema.columns where table_name = '"+prefix+"_apiconfig' and column_name = 'smsbaoTemplate';", Integer.class);
            if (i == 0){
                jdbcTemplate.execute("alter table "+prefix+"_apiconfig ADD `smsbaoTemplate` varchar(255) DEFAULT '' COMMENT '短信宝Template'");
                text+="配置中心模块，字段smsbaoTemplate添加完成。";
            }else{
                text+="配置中心模块，字段smsbaoTemplate已经存在，无需添加。";
            }

            i = jdbcTemplate.queryForObject("select count(*) from information_schema.columns where table_name = '"+prefix+"_comments' and column_name = 'pic';", Integer.class);
            if (i == 0){
                jdbcTemplate.execute("alter table "+prefix+"_comments ADD `pic` text COMMENT '图片'");
                text+="文章评论模块，字段pic添加完成。";
            }else{
                text+="文章评论模块，字段pic已经存在，无需添加。";
            }

            i = jdbcTemplate.queryForObject("select count(*) from information_schema.columns where table_name = '"+prefix+"_forum_comment' and column_name = 'pic';", Integer.class);
            if (i == 0){
                jdbcTemplate.execute("alter table "+prefix+"_forum_comment ADD `pic` text COMMENT '图片'");
                text+="帖子评论模块，字段pic添加完成。";
            }else{
                text+="帖子评论模块，字段pic已经存在，无需添加。";
            }
            //查询Sy_pages是否存在HomeMode字段
            i = jdbcTemplate.queryForObject("select count(*) from information_schema.columns where table_name = 'Sy_pages' and column_name = 'Hometop';", Integer.class);
            if (i == 0){
                jdbcTemplate.execute("ALTER TABLE `Sy_pages` ADD `Hometop` INT(11) NOT NULL DEFAULT '2';");
                text+="pages表，Hometop字段添加完成。";
            }else{
                text+="pages表，Hometop字段已经存在，无需添加。";
            }
            //查询Sy_pages是否存在topStyle字段
            i = jdbcTemplate.queryForObject("select count(*) from information_schema.columns where table_name = 'Sy_pages' and column_name = 'topStyle';", Integer.class);
            if (i == 0){
                jdbcTemplate.execute("ALTER TABLE `Sy_pages` ADD `topStyle` INT(11) NOT NULL DEFAULT '2';");
                text+="pages表，topStyle字段添加完成。";
            }else{
                text+="pages表，topStyle字段已经存在，无需添加。";
            }


        }catch (Exception e){
            e.printStackTrace();
            return Result.getResultJson(0,"数据库语句执行失败，请检查数据库版本及服务器性能后重试。",null);
        }
        text+="  ------ 执行结束，StarPro安装执行完成";
        JSONObject response = new JSONObject();
        response.put("code" , 1);
        response.put("msg"  ,text);
        return response.toString();
    }
    /**
     * 让内容字段变为utf8mb4，以支持emoji标签
     */
    @RequestMapping(value = "/toUtf8mb4")
    @ResponseBody
    @LoginRequired(purview = "-3")
    public String toUtf8mb4(@RequestParam(value = "webkey", required = false,defaultValue = "") String  webkey) {
        if(!webkey.equals(this.key)){
            return Result.getResultJson(0,"请输入正确的访问KEY。如果忘记，可在服务器/opt/application.properties中查看",null);
        }
        try{
            String isRepeated = redisHelp.getRedis("isTypechoInstall",redisTemplate);
            if(isRepeated==null){
                redisHelp.setRedis("isTypechoInstall","1",15,redisTemplate);
            }else{
                return Result.getResultJson(0,"你的操作太频繁了",null);
            }
            jdbcTemplate.execute("ALTER TABLE `" + prefix + "_contents` MODIFY COLUMN `title` VARCHAR(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;");
            jdbcTemplate.execute("ALTER TABLE `" + prefix + "_contents` MODIFY COLUMN `text` TEXT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;");
            jdbcTemplate.execute("ALTER TABLE `" + prefix + "_shop` MODIFY COLUMN `title` VARCHAR(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;");
            jdbcTemplate.execute("ALTER TABLE `" + prefix + "_shop` MODIFY COLUMN `text` TEXT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;");
            jdbcTemplate.execute("ALTER TABLE `" + prefix + "_shop` MODIFY COLUMN `value` TEXT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;");
            jdbcTemplate.execute("ALTER TABLE `" + prefix + "_inbox` MODIFY COLUMN `text` TEXT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;");
            jdbcTemplate.execute("ALTER TABLE `" + prefix + "_comments` MODIFY COLUMN `text` TEXT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;");
            jdbcTemplate.execute("ALTER TABLE `" + prefix + "_forum` MODIFY COLUMN `title` TEXT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;");
            jdbcTemplate.execute("ALTER TABLE `" + prefix + "_forum` MODIFY COLUMN `text` TEXT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;");
            jdbcTemplate.execute("ALTER TABLE `" + prefix + "_forum_comment` MODIFY COLUMN `text` TEXT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;");
            return Result.getResultJson(1,"操作成功",null);
        }catch (Exception e){
            e.printStackTrace();
            return Result.getResultJson(1,"操作失败",null);
        }

    }
}
