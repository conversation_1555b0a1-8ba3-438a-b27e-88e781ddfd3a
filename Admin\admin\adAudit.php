<?php
session_start();
?>



<?php
include_once 'Nav.php';
$aid = $_GET['aid'];

$article = "SELECT * FROM typecho_ads WHERE aid='$aid' limit 1";
$resarticle = mysqli_query($connect, $article);
$mod = mysqli_fetch_array($resarticle);
?>
<div class="row">

    <div class="col-lg-12">
        <div class="card">
            <div class="card-body">
                <h4 class="header-title mb-3 size_18">审核广告</h4>
                    <div class="form-group col-sm-4">
                        <label for="validationCustom01">广告aid</label>
                        <input type="text" class="form-control" id="validationCustom01"
                               name="aid" value="<?php echo $aid ?>" readonly>
                    </div>
                    <div class="form-group col-sm-4">
                        <label for="validationCustom01">发布者UID</label>
                        <input type="text" class="form-control" id="validationCustom01" placeholder="请输入发布者UID"
                               name="uid" value="<?php echo $mod['uid'] ?>" readonly>
                    </div>
                    <div class="form-group col-sm-4">
                        <label for="validationCustom01">广告类型</label>
                            <select class="form-control" id="example-select" name="type" disabled>
                                <?php if ($mod['type']== '0') { ?>
                                <option value="0" selected>推流</option>
                                <?php } else if ($mod['type']== '1') { ?>
                                <option value="1" selected>横幅</option>
                                <?php } else if ($mod['type']== '2') { ?>
                                <option value="2" selected>启动图</option>
                                <?php } else { ?>
                               <option value="3" selected>轮播图</option>
                                <?php } ?>
                                
                            </select>
                    </div>
                    <div class="form-group mb-3">
                        <label for="validationCustom01">广告标题</label>
                        <input type="text" class="form-control" id="validationCustom01" placeholder="请输入标题"
                               name="articletitle" value="<?php echo $mod['name'] ?>" readonly>
                    </div>
                    <label for="validationCustom01">广告简介</label>
                    <textarea id="notice" class="form-control" rows="6" name="text" readonly><?php echo $mod['intro'] ?></textarea>
                    <br />
                    <div class="form-group col-sm-4">
                        <label for="validationCustom01">转跳类型</label>
                            <select class="form-control" id="example-select" name="urltype" disabled>
                                <?php if ($mod['urltype']== '0') { ?>
                                <option value="0" selected>APP内部打开</option>
                                <?php } else { ?>
                               <option value="1" selected>转跳浏览器</option>
                                <?php } ?>
                                
                            </select>
                    </div>
                    <div class="form-group mb-3">
                        <label for="validationCustom08">转跳链接</label>
                        <input type="url" class="form-control" id="validationCustom08"
                                name="url" value="<?php echo $mod['url'] ?>" readonly>
                    </div>
                    <div class="form-group col-sm-4">
                        <label for="yl" style="margin-top:.5rem">广告图片：</label><br>
                        <span class="dtr-data" id="yl"><img style="width: 100%;object-fit: cover;margin-top:20px;margin-right:20px;box-shadow: 0 8px 12px #c9cbcfd6;border-radius: 6px" src="<?php echo $mod['img'] ?>" class="spotlight"></span>
                    </div>
                    
                    <?php 
                                $timestamp = $mod['close'];
                                $date = date("Y-m-d H:i:s", $timestamp);?>
                     <div class="form-group mb-3">
                        <label for="validationCustom08">到期时间</label>
                        <input type="datetime-local" class="form-control" id="validationCustom08"
                                name="bantime" value="<?php echo $date ?>" readonly>
                    </div>
                    
                    <div class="form-group mb-3 text_right">
                        <a class="fabu" onclick="Pass('<?php echo $aid ;?>')">
                            <button class="btn btn-primary" id="payoutPost" style="margin-right:10px">通过</button>
                        </a>
                        <a class="fabu" onclick="Refuse('<?php echo $aid ;?>')">
                            <button class="btn btn-danger" id="payoutPost">驳回</button>
                        </a>
                    </div>

            </div> <!-- end card-body-->
        </div> <!-- end card-->
    </div> <!-- end col-->
</div>


<!--<script src="https://cdn.bootcss.com/jquery/1.11.3/jquery.min.js"></script>-->


<script>
    function Pass(aid) {
        if (confirm('您确认要通过该广告吗？')) {
            location.href = 'adAuditPost.php?aid=' + aid +'&status=Pass';
        }
    }
    function Refuse(aid) {
        if (confirm('您确认要拒绝该广告吗？')) {
            location.href = 'adAuditPost.php?aid=' + aid +'&status=Refuse';
        }
    }
    
</script>

<?php
include_once 'Footer.php';
?>

</body>
</html>