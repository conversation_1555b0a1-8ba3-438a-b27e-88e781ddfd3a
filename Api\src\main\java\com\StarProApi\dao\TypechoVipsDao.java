package com.StarProApi.dao;

import com.StarProApi.entity.*;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * dao层接口
 * TypechoVipsDao
 * <AUTHOR>
 * @date 2023/06/09
 */
@Mapper
public interface TypechoVipsDao {

    /**
     * [新增]
     **/
    int insert(TypechoVips typechoVips);

    /**
     * [批量新增]
     **/
    int batchInsert(List<TypechoVips> list);

    /**
     * [更新]
     **/
    int update(TypechoVips typechoVips);

    /**
     * [删除]
     **/
    int delete(Object key);

    /**
     * [批量删除]
     **/
    int batchDelete(List<Object> list);

    /**
     * [主键查询]
     **/
    TypechoVips selectByKey(Object key);

    /**
     * [条件查询]
     **/
    List<TypechoVips> selectList (TypechoVips typechoVips);

    /**
     * [分页条件查询]
     **/
    List<TypechoVips> selectPage (@Param("typechoVips") TypechoVips typechoVips, @Param("page") Integer page, @Param("pageSize") Integer pageSize);

    /**
     * [总量查询]
     **/
    int total(TypechoVips typechoVips);
}
