package com.StarProApi.service.impl;

import com.StarProApi.entity.*;
import com.StarProApi.common.PageList;
import com.StarProApi.dao.*;
import com.StarProApi.service.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 业务层实现类
 * TypechoForumServiceImpl
 * <AUTHOR>
 * @date 2023/02/20
 */
@Service
public class TypechoForumServiceImpl implements TypechoForumService {

    @Autowired
	TypechoForumDao dao;

    @Override
    public int insert(TypechoForum typechoForum) {
        return dao.insert(typechoForum);
    }

    @Override
    public int batchInsert(List<TypechoForum> list) {
    	return dao.batchInsert(list);
    }

    @Override
    public int update(TypechoForum typechoForum) {
    	return dao.update(typechoForum);
    }

    @Override
    public int delete(Object key) {
    	return dao.delete(key);
    }

    @Override
    public int batchDelete(List<Object> keys) {
        return dao.batchDelete(keys);
    }

	@Override
	public TypechoForum selectByKey(Object key) {
		return dao.selectByKey(key);
	}

	@Override
	public List<TypechoForum> selectList(TypechoForum typechoForum) {
		return dao.selectList(typechoForum);
	}

	@Override
	public PageList<TypechoForum> selectPage(TypechoForum typechoForum, Integer offset, Integer pageSize,String order,String searchKey) {
		PageList<TypechoForum> pageList = new PageList<>();

		int total = this.total(typechoForum,searchKey);

		Integer totalPage;
		if (total % pageSize != 0) {
			totalPage = (total /pageSize) + 1;
		} else {
			totalPage = total /pageSize;
		}

		int page = (offset - 1) * pageSize;

		List<TypechoForum> list = dao.selectPage(typechoForum, page, pageSize,order,searchKey);

		pageList.setList(list);
		pageList.setStartPageNo(offset);
		pageList.setPageSize(pageSize);
		pageList.setTotalCount(total);
		pageList.setTotalPageCount(totalPage);
		return pageList;
	}

	@Override
	public int total(TypechoForum typechoForum,String searchKey) {
		return dao.total(typechoForum,searchKey);
	}
}